package ru.naumen.metainfoadmin.server.scparams;

import static ru.naumen.metainfoadmin.shared.scparams.SCParametersUIConstants.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.DtoFactory;
import ru.naumen.core.server.dispatch.Dispatch;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.server.settings.SettingsStorage;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.settings.AgreementServiceEditPrs;
import ru.naumen.core.shared.settings.AgreementServiceSetting;
import ru.naumen.core.shared.settings.SCParameters;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.guic.server.controller.FormControllerBase;
import ru.naumen.guic.shared.components.Form;
import ru.naumen.guic.shared.environment.IUIEnvironment;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.dispatch2.GetSettingsAction;
import ru.naumen.metainfo.shared.dispatch2.SaveSCParametersAction;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfo.shared.script.ScriptDtoFactory;
import ru.naumen.metainfoadmin.shared.scparams.SCParametersFormUtils;

/**
 * <AUTHOR>
 * @since Mar 5, 2015
 */
@Component("ru.naumen.metainfoadmin.server.scparams.EditSCParametersFormController")
public class EditSCParametersFormController extends FormControllerBase
{
    @Inject
    private DtoFactory dtoFactory;
    @Inject
    private SCParametersFormUtils formUtils;
    @Inject
    private SettingsStorage settingsStorage;
    @Inject
    private ScriptStorageService scriptStorageService;
    @Inject
    private ScriptDtoFactory scriptDtoFactory;
    @Inject
    private MetainfoService metainfo;

    @Inject
    private Dispatch dispatch;

    @Override
    public void onApply(Form form, IUIEnvironment env)
    {
        SCParameters parameters = dispatch.executeExceptionSafe(new GetSettingsAction()).get().getScParameters()
                .clone();
        SaveSCParametersAction saveAction = new SaveSCParametersAction(parameters);
        saveAction.setWithScripts(true);
        transform(form, env, parameters, saveAction);
        dispatch.executeExceptionSafe(saveAction);
    }

    @Override
    public void onOpen(Form form, IUIEnvironment env)
    {
        SCParameters parameters = settingsStorage.getSettings().getScParameters();
        transform(parameters, env);
        env.setProperty(AGREEMENT_SERVICE_SETTING_ELEMENTS, dtoFactory.create(AgreementServiceSetting.values()));

        Map<String, SimpleDtObject> prsIndex = getPresentationsIndex();
        env.setProperty(AGREEMENT_SERVICE_EDIT_PRS_MAP, new HashMap<>(prsIndex));

        formUtils.propagateSettings(form, env);
    }

    private Map<String, SimpleDtObject> getPresentationsIndex()
    {
        List<AgreementServiceEditPrs> presentations = new ArrayList<>();
        presentations.add(AgreementServiceEditPrs.TreeList);
        presentations.add(AgreementServiceEditPrs.HierarchicalTreeList);
        presentations.add(AgreementServiceEditPrs.List);
        presentations.add(AgreementServiceEditPrs.FoldersTree);

        //Иерархический список услуг есть тогда, когда услуги иерархичны
        if (metainfo.getMetaClass(Constants.SlmService.FQN).hasAttribute(Constants.PARENT_ATTR))
        {
            presentations.add(AgreementServiceEditPrs.ServiceTreeList);
            presentations.add(AgreementServiceEditPrs.HierarchicalServiceTreeList);
        }

        ArrayList<SimpleDtObject> dtos = dtoFactory.create(presentations);
        Map<String, SimpleDtObject> prsMap = CollectionUtils.index(dtos, CommonUtils.UUIDExtractor.INSTANCE);
        return prsMap;
    }

    private void processAgreementFiltrationScript(Form form, IUIEnvironment env, SCParameters scParameters,
            SaveSCParametersAction saveAction)
    {
        scParameters.setFilterAgreements(false);
        scParameters.setAgreementsFiltrationScript(null);
        if (form.getChild(IS_FILTER_AGREEMENTS).isVisible())
        {
            scParameters.setFilterAgreements(env.<Boolean> getProperty(IS_FILTER_AGREEMENTS));
        }

        ScriptDto agrScript = null;
        if (scParameters.isFilterAgreements())
        {
            agrScript = env.<ScriptDto> getProperty(AGREEMENTS_FILTRATION_SCRIPT);
        }
        else
        {
            agrScript = ScriptDtoFactory.createWithout();
        }

        scParameters.setAgreementsFiltrationScript(agrScript.getCode());
        saveAction.setAgreementsFiltrationScript(agrScript);
    }

    private void processServicesFiltrationScript(Form form, IUIEnvironment env, SCParameters scParameters,
            SaveSCParametersAction saveAction)
    {
        scParameters.setFilterServices(false);
        scParameters.setServicesFiltrationScript(null);
        if (form.getChild(IS_FILTER_SERVICES).isVisible())
        {
            scParameters.setFilterServices(env.<Boolean> getProperty(IS_FILTER_SERVICES));
        }

        ScriptDto slmScript = null;
        if (scParameters.isFilterServices())
        {
            slmScript = env.<ScriptDto> getProperty(SERVICE_FILTRATION_SCRIPT);
        }
        else
        {
            slmScript = ScriptDtoFactory.createWithout();
        }

        scParameters.setServicesFiltrationScript(slmScript.getCode());
        saveAction.setServicesFiltrationScript(slmScript);
    }

    private void transform(Form form, IUIEnvironment env, SCParameters parameters, SaveSCParametersAction saveAction)
    {
        String agsSettingsCode = env.getProperty(AGREEMENT_SERVICE_SETTING);
        parameters.setAgreementServiceSetting(AgreementServiceSetting.valueOf(agsSettingsCode));

        String prsCode = env.<DtObject> getProperty(AGREEMENT_SERVICE_EDIT_PRS).getUUID();
        parameters.setAgreementServiceEditPrs(AgreementServiceEditPrs.valueOf(prsCode));

        processAgreementFiltrationScript(form, env, parameters, saveAction);
        processServicesFiltrationScript(form, env, parameters, saveAction);
    }

    private void transform(SCParameters parameters, IUIEnvironment env)
    {
        Script agreementScript = scriptStorageService.getScript(parameters.getAgreementsFiltrationScript());
        Script serviceScript = scriptStorageService.getScript(parameters.getServicesFiltrationScript());

        ScriptDto agreementScriptDto = scriptDtoFactory.create(agreementScript);
        ScriptDto serviceScriptDto = scriptDtoFactory.create(serviceScript);

        env.setProperty(AGREEMENT_SERVICE_SETTING, parameters.getAgreementServiceSetting().getCode());
        env.setProperty(AGREEMENT_SERVICE_EDIT_PRS, dtoFactory.create(parameters.getAgreementServiceEditPrs()));
        env.setProperty(IS_FILTER_AGREEMENTS, parameters.isFilterAgreements());
        env.setProperty(AGREEMENTS_FILTRATION_SCRIPT, agreementScriptDto);
        env.setProperty(IS_FILTER_SERVICES, parameters.isFilterServices());
        env.setProperty(SERVICE_FILTRATION_SCRIPT, serviceScriptDto);
    }
}