<?xml version="1.0" encoding="UTF-8"?>
<licensingPolicy xmlns="http://www.naumen.ru/licensingPolicy" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.naumen.ru/licensingPolicy xsd/licensingPolicy.xsd ">
	<metaClasses>
		<metaClass>
			<fqn>
				<id>serviceCall</id>
			</fqn>
			<workflow>
				<transitions>
					<transition>
						<!-- Переход из любого статуса в статус "Закрыт" (closed) -->
						<to>closed</to>
					</transition>
					<transition>
						<!-- Переход из любого статуса в статус "Возобновлен" (resumed) -->
						<to>resumed</to>
					</transition>
				</transitions>
			</workflow>
		</metaClass>
	</metaClasses>
</licensingPolicy>
