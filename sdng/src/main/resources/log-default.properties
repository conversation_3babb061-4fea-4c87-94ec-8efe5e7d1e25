# ограничение длины для формировании стоки логирования
ru.naumen.log.dispatch.maxLength=1000
# Максимальное количество логов, выводимое на вкладку "Администрирование"
ru.naumen.log.max-logs-count=30
#Лимит длины сообщения log4j в памяти, 0 = нет лимита (int)
ru.naumen.log.message.memory.limit=1000000
# лимит времени (миллисекунды), с превышением которого логируются экшены в dispatch, 0 = логируется все
ru.naumen.log.min.duration=0
ru.naumen.log.useTraceId=true
ru.naumen.log.clientInfoType=DEFAULT