# Порт на котором будет поднят stomp acceptor при инициализации встроенного в SMP artemis
# при пустом значении stomp acceptor не поднимается
ru.naumen.artemis.embedded.stomp.acceptor.port=
# Строка для подключения к внешнему брокеру для обработки stomp-сообщений
ru.naumen.external.stomp.broker.uri=
#Перечень url через запятую, для которых отключена проверка заголовка Origin.
#В случае пустого значения разрешены все Origin
ru.naumen.async.queue.websocket.allowedOriginUrls=
#Включить проверку заголовка Origin в websocket
ru.naumen.async.queue.websocket.checkOriginHeader=false
#Вызывать ли отключение websocket канала, или просто отключать клиента от шины shared worker
ru.naumen.websocket.needRealDisconnectStompClient.enabled=false
#Таймаут в мс для подключение клиента к stomp брокеру
ru.naumen.websocket.stompClientConnectionTimeout=20000
#Задержка в мс для ожидания heartbeat сообщения от stomp брокера
ru.naumen.websocket.stompClientHeartbeatIncomingTimeout=20000
#Задержка в мс для отправки heartbeat сообщения stomp брокеру
ru.naumen.websocket.stompClientHeartbeatOutgoingTimeout=20000
#Задержка в мс для ожидания повторного подключения
ru.naumen.websocket.stompClientMaxReconnectDelayTimeout=60000
#Задержка в мс для попытки переподключения клиента к stomp брокеру
ru.naumen.websocket.stompClientReconnectDelayTimeout=20000