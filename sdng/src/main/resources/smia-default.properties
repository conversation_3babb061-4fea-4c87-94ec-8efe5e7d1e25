# Таймаут выполнения скрипта подготовки данных
smia.dataPreparationScript.timeout=3600
# Признак необходимости выгрузки файловых моделей на диск во время запуска приложения
smia.fileModel.downloadModelFilesToDisk=false
# Таймаут выполнения скрипта сбора данных
smia.gatheringDataScript.timeout=3600
# Таймаут выполнения скрипта обучения и тестирования
smia.learningProcessAndValidationScript.timeout=1800
# Максимальное число потоков, в которых может выполняться весь цикл "процесса обучения"
smia.listeners.concurrency=1
# Максимальное время (в часах) инвалидации значения в кэше моделей после последнего доступа к значению
smia.modelCache.expireAfterAccess=24
# Максимальное число моделей, содержащихся в кэше после загруки через api.ml.getModel(...)
smia.modelCache.maxSize=1
# Таймаут подключения к серверу удаленной модели
smia.remoteModel.connectionTimeout=30000
# Таймаут ожидания ответа от удаленной модели после отправки запроса
smia.remoteModel.readTimeout=300000
# Таймаут выполнения скрипта сохранения данных
smia.saveDataScript.timeout=3600
# Размер буфера потока вывода при записи в файл в скрипте сохранения данных
smia.saveDataScript.writerBuffer=26214400
# Период удаления устаревших задач обучения в миллисекундах (по умолчанию 2 недели)
smia.learnTask.deleteExpiredLearningTaskJob.period=1209600000
# Время по истечение которого задача обучения становится устаревшей (по умолчанию 1 неделя)
smia.learnTask.deleteExpiredLearningTaskJob.historyLength=604800000
# Размер пачки объектов в шт. при удалении устаревших задач обучения системной задачей планировщика
smia.learnTask.deleteExpiredLearningTaskJob.batch_size=1000
# Период загрузки файлов активных моделей на диск в миллисекундах
smia.scheduler.SmiaDownloadModelFilesJob.period=900000