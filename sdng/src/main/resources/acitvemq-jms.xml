<?xml version="1.0" encoding="UTF-8"?>
<configuration xmlns="urn:activemq" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="urn:activemq /schema/artemis-jms.xsd">

    <connection-factory name="ConnectionFactoryInVm">
-        <connectors>
        -            <!-- коннектор с таким названием определен в ru.naumen.core.server.jms.JmsConfiguration.NauEmbeddedJMS.IN_VM_CONNECTOR_NAME -->
        -
        <connector-ref connector-name="in-vm"/>
        -
    </connectors>
-        <entries>
-            <entry name="ConnectionFactoryInVm"/>
-        </entries>
-        
-        <!-- Два параметра ниже более не используются. См. ru.naumen.core.server.jms.JmsConfiguration -->
-        <!-- размер кэша для получения сообщений -->
-        <consumer-window-size>104857600</consumer-window-size>
-        <!-- просто размер буфера, который сервер может послать клиенту - 1Мб - достаточно для наших небольших сообщений -->
-        <producer-window-size>1048576</producer-window-size>
-    </connection-factory>
    

    <queue name="Queue.bridgeNdapAlertIn">
        <entry name="/queue/bridgeNdapAlertIn" />
    </queue>

    <queue name="Queue.AdvImport">
        <entry name="/queue/AdvImport" />
    </queue>

    <queue name="Queue.DocumentIndexer">
        <entry name="/queue/DocumentIndexer" />
    </queue>
    
    <queue name="Queue.DocumentFileIndexer">
        <entry name="/queue/DocumentFileIndexer" />
    </queue>

    <queue name="Queue.EventAction">
        <entry name="/queue/EventAction" />
    </queue>

    <queue name="Queue.EventAction.Notifications">
        <entry name="/queue/EventAction/Notifications" />
    </queue>

    <queue name="Queue.EventAction.Pushes">
        <entry name="/queue/EventAction/Pushes" />
    </queue>

    <queue name="Queue.External.EventAction">
        <entry name="/queue/External/EventAction" />
    </queue>

    <queue name="Queue.PlannedEvent.PEForSubjectCreateMessage">
        <entry name="/queue/PlannedEvent/PEForSubjectCreateMessage" />
    </queue>

    <queue name="Queue.PlannedEvent.PEByRuleCreateMessage">
        <entry name="/queue/PlannedEvent/PEByRuleCreateMessage" />
    </queue>

    <queue name="Queue.PlannedEvent.PECleanerQueue">
        <entry name="/queue/PlannedEvent/PECleanerQueue" />
    </queue>

    <queue name="Queue.PlannedEvent.DeleteAllPEQueue">
        <entry name="/queue/PlannedEvent/DeleteAllPEQueue" />
    </queue>

    <queue name="Queue.PlannedEvent.AddPlannedEventMessages">
        <entry name="/queue/PlannedEvent/AddPlannedEventMessages" />
    </queue>

    <queue name="Queue.Reports.BuildReport">
        <entry name="/queue/Reports/BuildReport" />
    </queue>
    
    <!-- Очередь асинхронной выгрузки отчетов -->
    <queue name="Queue.Reports.Export">
        <entry name="/queue/Reports/Export" />
    </queue>

    <queue name="Queue.Advlist.Export">
        <entry name="/queue/Advlist/Export" />
    </queue>

    <queue name="Queue.Hierarchy.Export">
        <entry name="/queue/Hierarchy/Export" />
    </queue>
    
    <queue name="Queue.smia">
        <entry name="/queue/smia" />
    </queue>

    <topic name="Topic.Reports.ReportReady">
        <entry name="/topic/Reports/ReportReady" />
    </topic>
</configuration>