<?xml version="1.0" encoding="UTF-8"?>
<licensingPolicy xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                 xmlns="http://www.naumen.ru/licensingPolicy"
                 xsi:schemaLocation="http://www.naumen.ru/licensingPolicy xsd/licensingPolicy.xsd ">
    <metaClasses>
        <metaClass>
            <fqn>
                <id>abstractBO</id>
            </fqn>
            <systemAttributes>
                <attributes>
                    <default>
                        <viewable>true</viewable>
                        <editable>false</editable>
                    </default>
                    <attribute>
                        <code>metaClass</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>title</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>lastModifiedDate</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>creationDate</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>removed</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>removalDate</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>author</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>state</code>
                        <editable>true</editable>
                    </attribute>
                    <attribute>
                        <code>stateStartTime</code>
                        <viewable>false</viewable>
                    </attribute>
                    <attribute>
                        <code>responsible</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>responsibleEmployee</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>responsibleTeam</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>responsibleStartTime</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>folders</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                </attributes>
            </systemAttributes>
            <userAttributes>
                <attributes>
                    <default>
                        <viewable>true</viewable>
                        <editable>false</editable>
                    </default>
                </attributes>
                <attributeTypes>
                    <attributeType>
                        <type>dtInterval</type>
                        <viewable>true</viewable>
                        <editable>true</editable>
                    </attributeType>
                    <attributeType>
                        <type>date</type>
                        <viewable>true</viewable>
                        <editable>true</editable>
                    </attributeType>
                    <attributeType>
                        <type>dateTime</type>
                        <viewable>true</viewable>
                        <editable>true</editable>
                    </attributeType>
                    <attributeType>
                        <type>bool</type>
                        <viewable>true</viewable>
                        <editable>true</editable>
                    </attributeType>
                    <attributeType>
                        <type>catalogItemSet</type>
                        <viewable>true</viewable>
                        <editable>true</editable>
                    </attributeType>
                    <attributeType>
                        <type>string</type>
                        <viewable>true</viewable>
                        <editable>true</editable>
                    </attributeType>
                    <attributeType>
                        <type>text</type>
                        <viewable>true</viewable>
                        <editable>true</editable>
                    </attributeType>
                    <attributeType>
                        <type>integer</type>
                        <viewable>true</viewable>
                        <editable>true</editable>
                    </attributeType>
                    <attributeType>
                        <type>catalogItem</type>
                        <viewable>true</viewable>
                        <editable>true</editable>
                    </attributeType>
                    <attributeType>
                        <type>json</type>
                        <viewable>true</viewable>
                        <editable>true</editable>
                    </attributeType>
                </attributeTypes>
            </userAttributes>
        </metaClass>
        <metaClass>
            <!-- Анкета -->
            <fqn>
                <id>questionary</id>
            </fqn>
            <systemAttributes>
                <attributes>
                    <default>
                        <viewable>true</viewable>
                        <editable>true</editable>
                    </default>
                </attributes>
            </systemAttributes>
            <userAttributes>
                <attributes>
                    <default>
                        <viewable>true</viewable>
                        <editable>true</editable>
                    </default>
                </attributes>
            </userAttributes>
        </metaClass>
        <metaClass>
            <fqn>
                <id>agreement</id>
            </fqn>
            <systemAttributes>
                <attributes>
                    <attribute>
                        <code>recipients</code>
                        <viewable>false</viewable>
                    </attribute>
                    <attribute>
                        <code>recipientsOU</code>
                        <viewable>false</viewable>
                    </attribute>
                    <attribute>
                        <code>recipientTeam</code>
                        <viewable>false</viewable>
                    </attribute>
                    <attribute>
                        <code>supplier</code>
                        <viewable>false</viewable>
                    </attribute>
                    <attribute>
                        <code>inventoryNumber</code>
                        <viewable>false</viewable>
                    </attribute>
                    <attribute>
                        <code>supplierEmpoyee</code>
                        <viewable>false</viewable>
                    </attribute>
                    <attribute>
                        <code>supplierTeam</code>
                        <viewable>false</viewable>
                    </attribute>
                    <attribute>
                        <code>priorityRule</code>
                        <viewable>false</viewable>
                    </attribute>
                    <attribute>
                        <code>resolutionTimeRule</code>
                        <viewable>false</viewable>
                    </attribute>
                </attributes>
            </systemAttributes>
        </metaClass>
        <metaClass>
            <fqn>
                <id>employee</id>
            </fqn>
            <systemAttributes>
                <attributes>
                    <attribute>
                        <code>performer</code>
                        <viewable>false</viewable>
                    </attribute>
                    <attribute>
                        <code>license</code>
                        <viewable>false</viewable>
                    </attribute>
                    <attribute>
                        <code>employeeSecGroups</code>
                        <viewable>false</viewable>
                    </attribute>
                    <attribute>
                        <code>teams</code>
                        <viewable>false</viewable>
                    </attribute>
                    <attribute>
                        <code>email</code>
                        <editable>true</editable>
                    </attribute>
                    <attribute>
                        <code>cityPhoneNumber</code>
                        <editable>true</editable>
                    </attribute>
                    <attribute>
                        <code>internalPhoneNumber</code>
                        <editable>true</editable>
                    </attribute>
                    <attribute>
                        <code>homePhoneNumber</code>
                        <editable>true</editable>
                    </attribute>
                    <attribute>
                        <code>mobilePhoneNumber</code>
                        <editable>true</editable>
                    </attribute>
                    <attribute>
                        <code>login</code>
                        <editable>true</editable>
                    </attribute>
                    <attribute>
                        <code>image</code>
                        <editable>true</editable>
                    </attribute>
                </attributes>
            </systemAttributes>
        </metaClass>
        <metaClass>
            <fqn>
                <id>serviceCall</id>
            </fqn>
            <systemAttributes>
                <attributes>
                    <default>
                        <editable>true</editable>
                    </default>
                    <attribute>
                        <code>description</code>
                    </attribute>
                    <attribute>
                        <code>urgency</code>
                    </attribute>
                    <attribute>
                        <code>clientLinkName</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>client</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>solvedBy</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>closedBy</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>clientOU</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>clientTeam</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>clientEmployee</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>agreement</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>service</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>clientName</code>
                    </attribute>
                    <attribute>
                        <code>clientPhone</code>
                    </attribute>
                    <attribute>
                        <code>clientEmail</code>
                    </attribute>
                    <attribute>
                        <code>registrationDate</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>deadLineTime</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>startTime</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>requestDate</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>massProblem</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>masterMassProblem</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>massProblemSlaves</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>number</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>solvedByTeam</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>solvedByEmployee</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>closedByTeam</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>closedByEmployee</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>codeOfClosing</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>impact</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>timeZone</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>priority</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>categories</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>resolutionTime</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>serviceTime</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>totalTimeTimer</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>processingTimeTimer</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>timeAllowanceTimer</code>
                        <viewable>true</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>wfProfile</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                </attributes>
            </systemAttributes>
            <userAttributes>
                <attributes>
                    <default>
                        <viewable>true</viewable>
                        <editable>true</editable>
                    </default>
                </attributes>
            </userAttributes>
            <workflow>
                <transitions>
                    <transition>
                        <from>resolved</from>
                        <to>closed</to>
                    </transition>
                    <transition>
                        <from>resolved</from>
                        <to>resumed</to>
                    </transition>
                </transitions>
            </workflow>
        </metaClass>
        <metaClass>
            <fqn>
                <id>slmService</id>
            </fqn>
            <systemAttributes>
                <attributes>
                    <attribute>
                        <code>inventoryNumber</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>callCases</code>
                        <viewable>false</viewable>
                        <editable>false</editable>
                    </attribute>
                </attributes>
            </systemAttributes>
        </metaClass>
        <metaClass>
            <!-- Голосование -->
            <fqn>
                <id>vote</id>
            </fqn>
            <systemAttributes>
                <attributes>
                    <default>
                        <editable>true</editable>
                    </default>
                </attributes>
            </systemAttributes>
            <userAttributes>
                <attributes>
                    <default>
                        <editable>true</editable>
                    </default>
                </attributes>
            </userAttributes>
            <workflow>
                <transitions>
                    <transition>
                        <from>registered</from>
                        <to>closed</to>
                    </transition>
                    <transition>
                        <from>registered</from>
                        <to>qualification</to>
                    </transition>
                    <transition>
                        <from>registered</from>
                        <to>accepted</to>
                    </transition>
                    <transition>
                        <from>qualification</from>
                        <to>closed</to>
                    </transition>
                    <transition>
                        <from>qualification</from>
                        <to>accepted</to>
                    </transition>
                    <transition>
                        <from>closed</from>
                        <to>accepted</to>
                    </transition>
                    <transition>
                        <from>accepted</from>
                        <to>closed</to>
                    </transition>
                </transitions>
            </workflow>
        </metaClass>
        <metaClass>
            <fqn>
                <id>interfaceTour</id>
            </fqn>
            <userAttributes>
                <attributes>
                    <attribute>
                        <code>disabledUsers</code>
                        <viewable>true</viewable>
                        <editable>true</editable>
                    </attribute>
                    <attribute>
                        <code>suspendedUsers</code>
                        <viewable>true</viewable>
                        <editable>true</editable>
                    </attribute>
                </attributes>
            </userAttributes>
        </metaClass>
        <metaClass>
            <fqn>
                <id>file</id>
            </fqn>
            <systemAttributes>
                <attributes>
                    <attribute>
                        <code>description</code>
                        <editable>true</editable>
                    </attribute>
                </attributes>
            </systemAttributes>
            <userAttributes>
                <attributes>
                    <default>
                        <viewable>true</viewable>
                        <editable>true</editable>
                    </default>
                </attributes>
            </userAttributes>
        </metaClass>
    </metaClasses>
</licensingPolicy>
