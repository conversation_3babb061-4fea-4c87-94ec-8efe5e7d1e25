# Параметры отчетов
#################################################################################
# Устанавливает экспорт отчетов в excel в формате .xls, если параметр не установлен, то по умолчанию .xlsx.
pentaho.report.export.excel.in.xls=false
# Максимальный размер отчета, построение которого идет в многопоточном режиме
reports.instances.max-concurrent-table-size=1073742
# Максимальный размер данных отчета, построенного в потоковом режиме, МB
reports.instances.max-memory-streaming.mb=1024
# Параметры построения отчетов.
#################################################################################
# Максимальное потребление памяти при построении отчета в памяти, МB (при этом размер самих данных будет в ~50 раз меньше)
reports.instances.max-memory.mb=1024
# В количестве ячеек (актуальный параметр, для отключения выставить -1) 1024 х 1024 х 1024/1000 = 1073742 ячейки = 1024 МB
reports.instances.max-table-size=1073742
# максимальный размер отчета для которого доступна оперативная выгрузка
# экспериментально установлено что 1311 ячейки это приметрно 256mb памяти, потребляемой
# при выгрузке отчета в формате pdf
reports.instances.sync.export-table-size=1311
# максимальный размер отчета, просмотр и печать которого доступны,
# при превышении система попросит воспользоваться выгрузкой
# при значении -1 ограничение не установлено
reports.max-show-table-size=-1
ru.naumen.reports.streaming.fallback=false
