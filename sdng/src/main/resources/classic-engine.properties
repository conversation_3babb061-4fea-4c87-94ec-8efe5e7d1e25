#файл конфигурации Pentaho Reports
#регистрируем свой модуль парсинга xml конфигурации отчета
org.pentaho.reporting.libraries.resourceloader.factory.modules.org.pentaho.reporting.engine.classic.core.DataFactory.pentaho-sql=ru.naumen.reports.engine.classic.core.modules.parser.data.sql.SQLResourceXmlFactoryModuleImpl
org.pentaho.reporting.engine.classic.core.modules.output.pageable.pdf.Encoding=UTF-8
org.pentaho.reporting.engine.classic.core.modules.output.pageable.plaintext.Encoding=UTF-8