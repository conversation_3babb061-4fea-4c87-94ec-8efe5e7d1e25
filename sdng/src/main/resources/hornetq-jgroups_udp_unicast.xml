<!--
  Default stack using IP multicasting. It is similar to the "udp"
  stack in stacks.xml, but doesn't use streaming state transfer and flushing
  author: <PERSON><PERSON> Ban

  dkirpichenkov: проверненный конфиг на UPD.
  Положение протокола в XML имеет значение

	ucast_recv_buf_size="212K"
	ucast_send_buf_size="212K"
	mcast_recv_buf_size="212K"
	mcast_send_buf_size="212K"
	max_bundle_size="64K"

-->

<config xmlns="urn:org:jgroups"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xsi:schemaLocation="urn:org:jgroups http://www.jgroups.org/schema/JGroups-3.4.xsd">

	<UDP
			mcast_addr="${jgroups.bus.mcast_addr:*********}"
			mcast_port="${jgroups.bus.mcast_port:50588}"
			tos="8"
			loopback="true"
			max_bundle_timeout="30"
			ip_ttl="${jgroups.udp.ip_ttl:8}"
			enable_diagnostics="true"
			thread_naming_pattern="cl"

			ip_mcast="false"

			timer_type="new3"
			timer.min_threads="4"
			timer.max_threads="10"
			timer.keep_alive_time="3000"
			timer.queue_max_size="500"

			thread_pool.enabled="true"
			thread_pool.min_threads="2"
			thread_pool.max_threads="8"
			thread_pool.keep_alive_time="5000"
			thread_pool.queue_enabled="true"
			thread_pool.queue_max_size="10000"
			thread_pool.rejection_policy="discard"

			oob_thread_pool.enabled="true"
			oob_thread_pool.min_threads="1"
			oob_thread_pool.max_threads="8"
			oob_thread_pool.keep_alive_time="5000"
			oob_thread_pool.queue_enabled="false"
			oob_thread_pool.queue_max_size="100"
			oob_thread_pool.rejection_policy="discard"/>

	<BPING timeout="2000" num_initial_members="2"/>
	<MERGE3 max_interval="30000"
			min_interval="10000"/>
	<FD_SOCK/>
	<FD_ALL/>
	<VERIFY_SUSPECT timeout="1500"/>

	<pbcast.NAKACK2 xmit_interval="500"
					xmit_table_num_rows="100"
					xmit_table_msgs_per_row="2000"
					xmit_table_max_compaction_time="30000"
					max_msg_batch_size="500"
					use_mcast_xmit="false"
					discard_delivered_msgs="true"/>
	<UNICAST3 xmit_interval="500"
			  xmit_table_num_rows="100"
			  xmit_table_msgs_per_row="2000"
			  xmit_table_max_compaction_time="60000"
			  conn_expiry_timeout="0"
			  max_msg_batch_size="500"/>
	<pbcast.STABLE stability_delay="1000" desired_avg_gossip="50000"
				   max_bytes="4M"/>
	<pbcast.GMS print_local_addr="true" join_timeout="3000"
				view_bundling="true"/>

	<FRAG2 frag_size="60K"/>
	<!-- Таймаут синхронных сообщений сейчас 100 секунд. Интервал повторных отправок - 2 сек -->
	<RSVP resend_interval="2000" timeout="100000"/>

	<!-- Централизованные локи в кластере -->
	<CENTRAL_LOCK stats="true" num_backups="1" level="DEBUG"/>

	<!-- pbcast.FLUSH  /-->
</config>
