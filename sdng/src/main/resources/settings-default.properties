#UUID'ы ключей доступа без обновления поля last_usage_date (разделитель ',')
accessKeys.nonUpdatable.usageDate=
#Codepages settings
alphabet=АБВГДЕЁЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдеёжзийклмнопрстуфхцчшщъыьэюяABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz1234567890-=—+_}{]["|;:'/?><.,`~��!@#$%^&*()№ \r\n
# Позволяет указать, что необходимо возвращать на клиента только видимые атрибуты.
attributes.only_visible=true
# url of stand for mail links, must end with /
# url стенда для формирования ссылок в оповещениях, должна оканчиваться на /
baseurl=http://localhost:8888/sd/
#Параметр отвечает за изначальное отображение блока "Подробнее" у комментариев.
#По умолчанию (в т.ч. если не указан) имеет значение false.
bo.showCommentPropertiesByDefault=false
# Использовать ли фильтр NaumenConcurrentSessionFilter
concurrent-session-filter.enabled=true
# Параметр механизма чистки временных файлов при запуске приложения.Необходима в случае, если мы хотим анализировать время последнего доступа к файлу, и на момент проверки удалять не
# все фалы без разбора, а только те, которые использовались, к примеру, раньше чем за несколько часов до начала чистки.
# Данное значение должно быть >= 0, если 0, те будут удалятся все найденные файлы. Доступна для изменения в runtime.
# Измеряется в МИЛЛИСЕКУНДАХ
count.time.passed.for.safely.delete=0
# Путь к оперативным данным приложения (каталог, в котором хранятся файлы очереди событий системы, поисковые индексы, почтовые сообщения)
data.dir=${user.home}/.naumen/sd/data
#Добавление случайной задержки для планировщиков событий
#################################################################################
# Случайная задержка для очистки закодированного текста. Значение задаётся в секундах
delayRandomValue.EncodedTextCleaner=0
#Случайная задержка для очистки истории. Значение задаётся в секундах
delayRandomValue.EventActionCleanerJob=0
#Случайная задержка для очистки устаревших дивертов. Значение задаётся в секундах
delayRandomValue.JMSDivertCleaner=0
#Случайная задержка для очистки неиспользуемых (помеченных к удалению) очередей JMS. Значение задаётся в секундах
delayRandomValue.JMSQueueCleaner=0
#Случайная задержка для регулярно проверки лицензионных модулей на актуальность. Значение задаётся в секундах
delayRandomValue.LicenseModulesCheckerJob=0
#Случайная задержка для очистки таблиц с логом входящей почты и письмами входящей почты. Значение задаётся в секундах
delayRandomValue.MailCleaner=0
#Случайная задержка для очистки экземпляров уведомлений. Значение задаётся в секундах
delayRandomValue.PushCleaner=0
#Случайная задержка для очистки экземпляров уведомлений на портал. Значение задаётся в секундах
delayRandomValue.PushPortalCleaner=0
#Случайная задержка для очистки временных файлов. Значение задаётся в секундах
delayRandomValue.TempFileCleanerJob=0
#Случайная задержка пользовательских задач
delayRandomValue.UsersJob=0
# Подключать ли js-библиотеки из профиля ФГИС ДО. Библиотеки нужны для использования КриптоПро ЭЦП в браузере
eds.enable=false
#################################################################################
# Записывать ли статистику выполнения скриптов
enable.script.statistic=false
# Параметры сервиса кодирования (сжатия) текста
#################################################################################
#Включить кодирования (сжатия) текста (раньше параметр отвечал за кодирование fqn в 2х символьное представление, название осталось прежним)
encode-fqn=true
#Включает шифрование паролей к внешним системам
encryption.enabled=false
# Отключить использование ClassValue, чтобы метаданные по классам не кэшировались. Отключать можно только для java 9 и старше. https://issues.apache.org/jira/browse/GROOVY-7591
groovy.use.classvalue=true
##############ru.naumen.core.server.push.EventActionCleanerJob###################
history.cleaner.allowed_hours_begin=0
history.cleaner.allowed_hours_end=23
history.cleaner.batch_size=1000
history.cleaner.metastorage.allowed.hours.begin=0
history.cleaner.metastorage.allowed.hours.end=23
# количество версий, которое нужно отнять от текущей, для поиска тех, что можно удалить
history.cleaner.metastorage.count.version.from.current=10
# интервал запуска задачи MetaStorageCleaner в виде крон-выражения
history.cleaner.metastorage.cron.expression=
#OWASP
#Игнорировать XSS-защиту в безопасном RTF и в строковых атрибутах
ignore.owasp.in.safe.html=true
#if null use default infinispan configuration xml
infinispan.configuration=
#Код модуля Jwt-авторизации, по умолчанию jwtAuthUtils
jwt.authModule.code=jwtAuthUtils
#кука для sd выдается на две минуты, она нужна только для входа в ИО, после выбора роли
jwt.cookie.maxAge=2
jwt.cookie.name=AccessToken
jwt.cors.allowedOrigins=*
jwt.filter.enabled=false
#################################################################################
jwt.mobile.accessToken.maxAge=10
jwt.mobile.filter.enabled=false
jwt.mobile.refreshToken.maxAge=20
#по умолчанию токен выдается на день(в минутах)
jwt.token.maxAge=1440
#Регистрозависимость логина
login.casesensitive=true
#Максимальное количество потоков массового редактирования объектов.
#Выбирается наименьшее значение между размером пула соединений с БД и указанным здесь значением.
#Если указанное здесь значение - ноль или менее, то оно расчитывается как количество аппаратных потоков системы (ядер и HT) * 3.
mass.edit.max.cpu.threads=0
# Максимальный размер картинки (ширина или высота) в атрибуте типа "Текст в формате RTF", px
max.rtf.image.size=700
persistence_context.ast.enabled=true
#################################################################################
# Константы для конфигурации соединений планировщика к БД
#################################################################################
#Таймаут для подключений планировщика к БД в секундах. Исключает бесконечное ожидание ответа на запрос в случае потери связи с БД.
quartz.datasource.connection.timeout=15
#################################################################################
# Константы, использующиеся в механизме восстановления планировщика
#################################################################################
#Автоматически восстановить поток кварца при потере соединения с БД
quartz.autoRecovery=true
#Период времени, в течении которого полное восстановление планировщика может быть запущено не более 1 раза (в секундах)
quartz.recovery.startupRestrictionPeriod=60
#Сколько раз пытаться восстановить планировщик после ошибки
quartz.recovery.attemptsCount=10
#################################################################################
# Константы, использующиеся в механизме обнаружения и восстановления зависших задач (Watchdog)
#################################################################################
#Использовать специальный поток, анализирующий ход выполнения задач планировщика и в случае необходимости перезапускающий их.
quartz.watchdog.enabled=true
#Количество обходов watchdog'а, в течение которых поток задачи планировщика может оставаться на одном месте.
#В случае, когда достигнуто указанное число обходов, поток будет признан зависшим и перезапущен.
quartz.watchdog.maxStuckTime=2
#Промежуток времени между обходами watchdog'a планировщика. Указывается в секундах.
quartz.watchdog.sleepTime=600
#################################################################################
# Константы на механизм вето планировщика
#################################################################################
#Включение механизма тротлинга выполнения задач - "не чаще 1 раз в 15 секунд" (по умолчанию операция отключена).
#По умолчанию механика отключена, с перспективой на удаление из платформы
quartz.veto.tooOftenCheckOperation.enabled=false
#################################################################################
# Сжимать большие изображения до максимально допустимого размера
rtf.image.compress=false
# Процент сжатия изображения (должен быть больше 0). От 0 до 1 - сжатие, 1 - исходное изображение, от 1 - увеличение
rtf.image.compress.ratio=0.75
#включено ли на стенде прерывание выполенния запросов на сервер, если пользователь перешел в другое место в браузере
ru.naumen.abort.dispatch.enabled=true
# время жизни кэша запущенных процессов экспорта адвлиста (в часах)
ru.naumen.advListExport.startedProcesses.cache.ttl.hours=1
#Таймаут выполнения sql-запроса для sql-источника данных. В секундах.
ru.naumen.advimport.sql.timeout=3600
#################################################################################
# Константы, использующиеся в Advimport
#################################################################################
# Использовать статусбар в записях Advimport
ru.naumen.advimport.statusbar=true
# Используется ли при advimport внутренний семафор. Отключение позволяет снизить накладные расходы.
# При отключении возможны проблемы с остановкой импорта.
ru.naumen.advimport.useThreadSemaphore=true
ru.naumen.advlist.export.batch=100
#Шрифт импорта списка
ru.naumen.advlist.export.font=
# ru.naumen.advlist.export.live_day явзяется устаревшим. Новый параметр ru.naumen.export.lifetime
ru.naumen.advlist.export.live_day=3
ru.naumen.advlist.export.size=1000
# оптимизация выборки доступных значений для фильтрации advlist'а для ссылочных атрибутов
ru.naumen.advlist.filter.possibleValues.query.addDistinct=true
#Максимальное количество сущностей, возвращаемое из хранилища за запрос
ru.naumen.api.key_value.find.max_results=1000
#Видимость иконки с описанием атрибута рядом с названием
ru.naumen.attribute.showDescriptionIcon=true
#Включить сохранение ранее введенного значения времени при редактировании даты.
ru.naumen.attributes.dataTime.saveOldValueTime=false
#Включить заполнение определяемого атрибута по пересечению с подмножеством определяющего атрибута
ru.naumen.attributes.valueMap.subsetIntersection=false
#Использовать ли InCriterionBig при формировании HQL-запросов, содержащих выражение IN
ru.naumen.big_in_constructed_sql_enable=true
# Использовать ли временную таблицу при формировании HQL-запросов, содержащих выражение IN, вместо постоянной, т.е.
# если true, то для всех бд будет использоваться промежуточная временная таблица для запросов IN в зависимости от параметра
# ru.naumen.in_with_id_storage_activate_border, если же false, то используем старое поведение, т.е. для базы MSSQL
# используем постоянную таблицу для запросов IN, а в других БД не используем никаких промежуточных таблиц
ru.naumen.useTempTableForIdStorage=true
#Таймаут асинхронной операции для comet-соединения в секундах. Нулевое или отрицательное значение снимает ограничение.
ru.naumen.comet.connections.asyncTimeout=120
#Помечать ли соединение завершенным при возникновении ошибок.
ru.naumen.comet.connections.completeOnError=true
#Максимальное количество активных comet-соединений. 0 - не ограничено
ru.naumen.comet.connections.limit=0
#Множитель прогрессивного таймаута. Используется для расчета времени
#повторной инициализации comet после неудачной попытки.
#Повторная попытка будет произведена черех 3^attemptNumber * multiplier секунд
ru.naumen.comet.reconnect.multiplier=300
#Вкл/выкл поиск и фильтрацию по подтверждению пользователя
ru.naumen.confirm.filtration=false
# Ширина страницы для расчёта минимальных допустимых размеров блоков страницы
# Считаются по формуле:
# [Данный параметр]*([процент колонки заданный в настройке интерфейса]-[Коэффициент усушки]) em
ru.naumen.content.pageWidthForCalcMinLayoutColumnSize=
#Вычислять только видимые вычислимые атрибуты на карточке
ru.naumen.core.client.card.computeOnlyVisibleCompAttributes=false
#Вычислять только видимые атрибуты на формах (true/false)
ru.naumen.core.client.form.evalOnlyVisibleAttributes=false
#Показывать или нет полный текст сообщения об ошибке при валидациях
ru.naumen.core.client.form.showFullValidationText=false
#Возможность скрывать архивные объекты в атрибутах типа Обратная ссылка и Набор ссылок на БО
ru.naumen.core.server.attr.enableHideArchivedObjects=false
#Исключать из hbm атрибуты связанного объекта. При этом: true -исключает все атрибуты связанного объекта, false - только атрибуты связанного объекта типа счетчик.
ru.naumen.core.server.excludeAllRelatedAttrs=false
#Использовать подзапрос получения команд сотрудников в запросе получения групп пользователей, если общее количество команд превышает указанное значение
ru.naumen.core.server.groupMembers.use_subquery_if_teams_more_then=20000
#Число дней, которое записи в очереди писем не удаляются задачей чистки. По умолчанию - 7
ru.naumen.core.server.mail.MailQueueCleaner.agingPeriod=7
#Флаг, включающий/отключающий задачу архивирования старых версий метаинформации
ru.naumen.core.server.metastorage.compressor.on=true
#Включить отслеживание времени истечения обратных таймеров с последующим изменением статуса в базе
ru.naumen.core.server.timerStatusChangeHandler.enable=false
#Допустимая разница между текущим временем и временем истечения обратного таймера для срабатывания события "Истечение времени обратного счетчика"
ru.naumen.core.server.timerStatusChangeHandler.timerStatusChangeDelay=100
#Включить обновление значений счетчиков в БД c определенной периодичностью
ru.naumen.core.server.timerStatusRefresh.enable=false
#Периодичность обновления значений счетчиков в БД(задаётся в минутах, не рекомендуется снижать установленное значение)
ru.naumen.core.server.timerStatusRefresh.timerStatusRefreshDelay=30
#Параметр для включения формата отображения даты вместе с её локальным значением
ru.naumen.core.shared.attr.formatters.dateTimeWithLocalFormat=true
#Перезагружать ли субъекты во время открытия формы ПДПС
ru.naumen.core.userEvent.reloadSubjectsOnFire=false
#Количество элементов списка, которые будут выводиться при вызове ScriptDtOCollection#toString()
#Если < 0 => выводятся все элементы, если >= 0 => выводится указанное количество
ru.naumen.dto.collection.countValueInString=10
#Параметр для включение режима ограничения фильтрации по списку для сложной формы
ru.naumen.dynaform.client.content.complexrelation.form.list.filterRestrictionForComplexForm=true
#Параметр для включения поднятия облегченных объектов(без ссылочных атрибутов), используемых в контенте "Выбор контрагента"
#использовать с осторожностью, тк все ссылочные атрибуты у объектов в контенте будут null
ru.naumen.dynaform.client.useDtoObjectsWithoutLinkAttrs=false
# Список кодов встраиваемых приложений, которые могут отображаться на модальных формах
ru.naumen.embeddedapplication.applicationsAvailableOnModalForm=
# Инициализировать ли встроенное приложение, если оно находится в Read-Only ноде кластера
ru.naumen.embeddedapplication.showApplicationsInReadOnly=true
#Определяет длину ключа шифрования
ru.naumen.encryption.key256.enabled=false
ru.naumen.export.lifetime=3
# Использование html-парсера для текста со ссылками на файлы в системе (по умолчанию true, иначе исп. xml-парсер)
ru.naumen.file.base64.converter.parser.html=true
# Ограничение на размер файла при преобразовании в base64 (в килобайтах)
ru.naumen.file.base64.limit=51200
#Параметр включает использование ByteArrayInputStream в месте утечки обычного стрима,
# параметр нужен на случай, если приложение начнет падать с OutOfMemory
ru.naumen.fileContentStorage.useByteArray=true
#Default files cacheControl
ru.naumen.files.cache.control=max-age=3600, must-revalidate, post-check=0, pre-check=0
#Controls filestorage directory structure creation on application start
ru.naumen.filestorage.filesystem.structure.onstart.create=true
# Включить создание превью картинок
ru.naumen.filestorage.imagePreview.enabled=true
# Включена синхронизация массовых действий с файлами (на данный момент это чистка почты и перенос файлов)
ru.naumen.filestorage.mass_operation_sync=false
ru.naumen.filestorage.move.batch.size=1000
ru.naumen.filestorage.upload.direct=false
#Таймаут в миллисекундах на операцию записи (удаления) для изменения (удаления) содержимого файлов или переноса файлов из БД во внешнее хранилище
ru.naumen.filestorage.write.timeout=5000
#Доступность сложной формы добавления связи с полнотекстовым поиском, по умолчанию false
ru.naumen.form.complex.fullTextSearchFormEnabled=false
#Open complex form on press Enter if focus is in select box
ru.naumen.form.complex.open.onenter=false
ru.naumen.form.lock.blockingButtonsLogging=false
ru.naumen.form.lock.blockingButtonsTimeout=0
#Отображение полного названия объектов на плашках мультиселектов
ru.naumen.form.tag.multiline=false
#Switch browser autocomplete of textboxes
ru.naumen.form.textbox.autocomplete=false
# Включить/отключить эмоджи в редакторе фроала
ru.naumen.froala.emoji=false
#Включить перенаправление rest запросов в шлюз
ru.naumen.gateway.restEndpoint.enabled=false
# Количество повторов запроса PrefixObjectLoaderServiceImpl#get
ru.naumen.get.retry.maxAttempts=5
# Паузы между запросами PrefixObjectLoaderServiceImpl#get
ru.naumen.get.retry.maxDelay=1000
#Использовать ли механизм который перед выполнением скрипта поднимает весь объект в контекстную переменную subject
ru.naumen.getSubjectBeforeScript=false
#Ограничение для загрузки списков объектов из базы данных с родителями и папками
ru.naumen.greedyFetching.maxSize=8192
#Ignore timeout error in dispatch
ru.naumen.gwt.rpc.ignore-request-timeout=true
#Ограничение поиска родительских и дочерних объектов по структуре для Api
ru.naumen.hierarchyGrid.lookupObjectLimit=10000
#Лимит для перебора объектов при поиске страницы в иерархическом списке
ru.naumen.hierarchyGrid.searchPageForFocusOnObjectLimit=1000
#Разрешить использование обратных ссылок для построения запроса по связи между уровнями иерархии
ru.naumen.hierarchyGrid.useBackLinkForLevelJoin=true
#Разрешить использование временных таблиц для оптимизации запросов по набору идентификаторов (IN)
ru.naumen.hierarchyGrid.useTemporaryStorageForInQueries=true
#Разрешить использование временных таблиц для оптимизации запросов по связи между уровнями иерархии
ru.naumen.hierarchyGrid.useTemporaryStorageForLevelSubtree=true
#Разрешить использование временных таблиц для оптимизации запросов по вложенным в себя элементам структуры иерархии
ru.naumen.hierarchyGrid.useTemporaryStorageForSelfNested=true
# XSS
#Убирать атрибуты-события из html в тексте rtf. true - убирать, false - оставлять, по умолчанию true
ru.naumen.html.removeEventsFromRtf=true
#Максимальная глубина вложенности html-тегов. При превышении содержимое отбрасывается
ru.naumen.html.sanitizer.nestingLimit=256
#Убирать атрибуты-обработчики событий
ru.naumen.html.sanitizer.remove_events=true
#Убирать значения атрибутов, являющиеся javascript
ru.naumen.html.sanitizer.remove_js_attr_value=true
#Список запрещенных тегов (удаляются при сохранении)
ru.naumen.html.sanitizer.tags_to_cut=script, iframe
#Список запрещенных тегов во фроале (удаляются при вставке во фроалу)
ru.naumen.html.sanitizer.froalaProhibitedTags=iframe
# HttpClientApi Parameters
#################################################################################
ru.naumen.httpclientapi.postjson.timeout=5000
#################################################################################
#Использовать два подзапроса для отрисовки контента "Связанный список" с включенным признаком "Показывать объекты с двух сторон связи" вместо кросс-джойна
ru.naumen.inDirectAndBackLinkFilter.useSubCriterias=false
#Задает количество объектов в атрибуте-коллекции(границу включения), при котором будет использован InCriterionWithIdStorage для атрибутов-коллекций uuid-ов
ru.naumen.in_with_id_storage_activate_border=10000
#Отключить обработку внешних сущностей в XML
ru.naumen.isProcessingExternalEntityInXML=false
#Настройки задачи по удалению устаревших дивертов
ru.naumen.jmsDivert.cleaner.allowed_hours_begin=0
ru.naumen.jmsDivert.cleaner.allowed_hours_end=23
#Настройки задачи по удалению неиспользуемых очередей
ru.naumen.jmsQueue.cleaner.allowed_hours_begin=0
ru.naumen.jmsQueue.cleaner.allowed_hours_end=23
# При включенном параметре генерация json схем будет ленивая, т.е. происходит в момент первого запроса схемы
# При выключенном параметре генерация json схемы будет происходить вместе с компиляцией модулей
ru.naumen.jsonScheme.init.lazy=true
ru.naumen.loadAllPropertiesForNewObjectParent=true
#Загружать ли все свойства объектов на клиент при открытии форм добавления
ru.naumen.load_all_prop_for_objects=false
ru.naumen.load_all_prop_for_resolve_client=false
#Сохранение введенных пользователей значений в localstorage для последующего восстановления в случае разрыва сессии
ru.naumen.localstorage.saveInputValues=true
#Для тестирования. Отключает проверку сертификатов при отправке почты по протоколу EWS
#Небезопасно использовать в работающем приложении
ru.naumen.mailsender.server.trustAllCerts=false
ru.naumen.maintenance.disableOnStart=false
#Максимамальный суммарный размер выгружаемых файлов из авдлиста в Мб
ru.naumen.max.files.size=500
# Скрыть кнопки "Сбросить параметры" для атрибутов типа/класса, карточки объекта, форм добавления и редактирования
ru.naumen.metainfoadmin.client.resetButtons.hidden=false
# Токен для доступа к ендпоинту sd/metrics, когда не задан - ограничений нет
ru.naumen.metrics.authToken=
# Зашифрованный токен для доступа к ендпоинту sd/metrics, когда не задан - ограничений нет
ru.naumen.metrics.authToken.enc=
# Путь до скрипта для выполнения на старте приложения после импорта метаинформации
ru.naumen.migration.once.path=
##############ru.naumen.stacktrace###############################################
#Параметр указывает на каталог для хранения стек-трейсов приложения относительно data.dir.
ru.naumen.monitoring.stack_file_folder=jstack
#################################################################################
#MSOffice filter
ru.naumen.msofficefilter.enabled=false
# Вкл/выкл механизм обновления таблицы с именованием статусов и классов/типов во время загрузки метаинформации/модификации типа/класса и/или статуса
ru.naumen.noUpdateClassTitlesOnMetainfoLoad=false
#Параметр интервал задержки в сек для автосохранения настроек после последнего изменения в сложном списке
ru.naumen.objectlist.client.mode.active.extended.advlist.autoSaveAdvlistSettingsInterval=10
#Параметр включения режима фонового сохранения настроек для списков
ru.naumen.objectlist.client.mode.active.extended.advlist.saveContentSettings=true
#Таймаут на получение блокировки при блокировании объектов для редактирования
ru.naumen.objects.locking.acquire.timeout=900000
#User recorder
ru.naumen.onstart.for.all.duration=-1
# Параметры, используемые для подписи pkcs7 с помощью openssl
#################################################################################
#Путь до сертификата
ru.naumen.pkcs7.cert=
#команда openssl, которая подписывает строку.
ru.naumen.pkcs7.commandTemplate=openssl smime -engine gost -sign -inkey {0} -signer {1} -outform pem -in {2} -out {3}
#Путь до файла с защищенным приватным ключом.
ru.naumen.pkcs7.privateKey=
# Пароль от приватного ключа
ru.naumen.pkcs7.privateKeyPassword=
# Зашифрованный пароль от приватного ключа
ru.naumen.pkcs7.privateKeyPassword.enc=
#Включение пушей в портал
ru.naumen.portal.push.enabled=true
#################################################################################
#Possible agreements fetch configuration
#if set, full possible agreements hierarchy will be computed and stored in cache
#otherwise possible agreements will be loaded from DB with paging
ru.naumen.possible.agreements.fetch.fallback.mode=true
#Использовать частичное вычисление ответственный в поле типа список выбора
ru.naumen.possibleResponsibleTree.usePartialLoad=false
# Параметр отвечает за обработку упоминаний при получении значения атрибута "Текст RTF" через RichTextServlet
ru.naumen.processMentionsInRtfServlet=true
##############ru.naumen.core.server.push.PushCleaner#############################
ru.naumen.push.cleaner.allowed_hours_begin=0
ru.naumen.push.cleaner.allowed_hours_end=23
ru.naumen.push.cleaner.batch_size=1000
ru.naumen.push.not_read_by_user_age=7
ru.naumen.push.read_by_user_age=7
#Параметр, отвечающий за оборачивание пуш-уведомлений, а так же пуш-уведомлений в логе уведомлений в iframe
#true-обернуть пуш в iframe иначе false
#по умолчанию false
ru.naumen.push.wrapPushInIframe=false
##############ru.naumen.core.server.push.PushPortalCleaner#############################
ru.naumen.push_portal.cleaner.allowed_hours_begin=0
ru.naumen.push_portal.cleaner.allowed_hours_end=23
ru.naumen.push_portal.cleaner.batch_size=1000
ru.naumen.push_portal.not_read_by_user_age=-1
ru.naumen.push_portal.read_by_user_age=-1
ru.naumen.read_only.dispatch.percent=0
ru.naumen.read_only.rest.percent=0
#Крон выражение для задания периодичности выполнения задачи, если не задано - задача не будет создана
ru.naumen.recalculate.endStateTimers.cron=0 */5 * ? * *
#Отложенный пересчет счетчиков времени при изменении признака конечности статуса
ru.naumen.recalculate.endStateTimers.deferred=false
#Количество поднимаемых объектов за одно выполнение задачи
ru.naumen.recalculate.endStateTimersTimers.batch=10000
#Использовать объект карточки вместо объекта связи в контенте "Параметры связанного объекта" при проверке прав на пользовательское действие по событию
ru.naumen.relObjPropList.tools.permissions.useCardObjectInsteadRelObject=false
# использовать Accessor для проверки наличия значения атрибута в remove-customizer
ru.naumen.remove.customizer.use.accessor=true
# Maximum parallel executions of the REST API methods
ru.naumen.rest.connections.queue.maxSize=100
# REST API throttling configuration settings
# Timeout for waiting if queue is full
ru.naumen.rest.connections.queue.timeoutInMillis=20000
#Execute module methods in exec with user (true) or superuser (false) rights
ru.naumen.rest.exec.restricted=false
#Наименования rest endpoint-ов для которых бизнес-пользователи будут получать ошибку с кодом 403 (разделитель ',')
ru.naumen.rest.denied=
#Наименования earest endpoint-ов для которых бизнес-пользователи будут получать ошибку с кодом 403 (разделитель ',')
ru.naumen.earest.denied=
#Наименования portalrest endpoint-ов для которых бизнес-пользователи будут получать ошибку с кодом 403 (разделитель ',')
ru.naumen.portalrest.denied=
#Rest find default limit
ru.naumen.rest.limit.find=5000
#Использовать в методе find новую логику поиска с учетом прав на карточку объекта и меток
ru.naumen.rest.safety.find=false
#Использовать в методе get новую логику получения объекта с учетом прав на карточку объекта и меток
ru.naumen.rest.safety.get=false
#Использовать ли старую логику при выполнении функции в рест-запросах (в старой логике некорректно отрабатывает кеш скриптов)
ru.naumen.rest.exec.legacyLogic=false
#Восстанавливать ли сессию после выполнения REST запроса
ru.naumen.rest.updateSessionControl=false
ru.naumen.rtfEditor.insertImageAsURLEnabled=true
#Использовать редактор RTF Froala
ru.naumen.rtf.editor.froala=true
#Максимально допустимый размер загружаемых в редакторе Froala изображений в байтах,
#при активном режиме загрузки изображений в виде ссылки
ru.naumen.rtf.editor.froala.imageMaxSize=10485760
#Допустимые протоколы для ссылок в редакторе RTF Froala, через разделитель: ','
ru.naumen.rtf.editor.froala.link-protocols=mailto,tel,sms,notes,data,onenote,smp
#Выставить уменьшенную "минимальную высоту" текстового поля типа RTF (true/false)
ru.naumen.rtf.editor.froala.minHeightReduced=false
#Список недопустимых мест в интерфейсе администратора для отображения редактора RTF Froala
#Возможные значения:
#1. "all" - запрещено всюду;
#2. "eventActions" - запрещено для ДПС;
#3. "escalations" - запрещено для эскалаций;
#4. "styleTemplates" - запрещено для шаблонов стилей;
#5. "attrDescr" - запрещено для описания атрибутов;
#6. "attrDef" - запрещено для значений по умолчанию атрибутов.
#7. Значение не указано (по умолчанию) - разрешено всюду.
#Возможные разделители: ',' , ';'.
ru.naumen.rtf.editor.froala.unavailability.admin=
# Опция инициализации Froala для настройки оповещений.
# True (значение по-умолчанию во Froala) - включено использование внешних свойств CSS
# и преобразование их в инлайн-стили. False - выключено использование внешних CSS.
ru.naumen.rtf.editor.froala.useClassesNotification=false
#Использовать исправление блокировки backspace в полях ввода
ru.naumen.rtf.editor.froala.use-image-editor-close-fix=true
# Использовать оригинальный файл при обработке изображений в rtf-атрибуте методом
# api.preview.shrinkImages(subject, '<код RTF>', false)
ru.naumen.rtf.editor.useOriginalFileInShrinkImages=true
# Включить использование логгера класса скрипта вместо логгера общего класса Script
ru.naumen.scriptClassLoggingEnabled=false
#Amount of saved script events before flushing to disk
ru.naumen.script.events.buffer.size=10
# Валидация значений ссылочных атрибутов на соответствие результату выполнения скрипта фильтрации.
ru.naumen.scriptFilter.validateOnSave=false
# Режим компиляции модулей. Возможные значения:
# all - Компилировать вместе (когда необходимо иметь зависимости между модулями)
# oneByOne - Компилировать независимо друг от друга
ru.naumen.script_modules.compilation.mode=all
# Использовать построение зависимостей между модулями для компиляции в режиме ALL
# Это значит, что будут компилироваться модули, которые изменились и которые используют изменившиеся модули.
ru.naumen.script_modules.compilation.all.useModuleDependencies=true
# нужно ли применять кастомное разрывание связей, чтобы сделать классы доступными для выгрузки из памяти (удаление упоминаний классов из кэшей и метаданных GroovyClassLoader-а
ru.naumen.script_modules.needBreakLinksWithOldClassLoader=true
# Включить компиляцию с invokedynamic для скриптов и модулей
ru.naumen.script_modules.useInvokeDynamic=false
# Включение этапа чистки genericInfo у Generic-классов в процессе освобождения classLoader-ов для скриптов и модулей
ru.naumen.script_modules.clearGenericInfo=true
# Группа для Дашбордов
ru.naumen.secGroup.dashboardMaster.enabled=false
#Разрешить использование символа ';' в url запросов. Включать данный параметр не рекомендуется из соображений безопасности.
#см. https://wiki.owasp.org/index.php/Reflected_File_Download
ru.naumen.security.allow.url.semicolon=false
# Нужно ли проверять права на карточку при переходе на форму редактирования
ru.naumen.security.checkViewCardRightOnEditForm.enabled=true
#Использовать функционал профилей администрирования
ru.naumen.security.customAdminProfiles.enabled=false
#Сохранить accesskey при смене логина
ru.naumen.security.editLogin.saveAccessKey.enabled=false
# Проверка прав при получении иерархии
ru.naumen.security.hierarchy.check_permission=true
# Разрешено ли передавать в URL аргументы сообщения об ошибке на форме входа
ru.naumen.security.loginForm.allowArgumentsInUrl=true
# Валидация токенов доступа к данным списков.
ru.naumen.security.objectList.dataToken.enabled=true
# Валидация токенов доступа к данным выпадающих списков выбора значений.
ru.naumen.security.possibleValues.dataToken.enabled=true
# Разрешает или запрещает логирование ключей доступа в REST-вызовах.
ru.naumen.security.rest.allowAccessKeyLogging=false
# Валидация токенов доступа на значениях атрибута типа "Текст в формате RTF"
ru.naumen.security.richText.dataToken.enabled=true
# Удалять большинство тегов перед выводом через setHtml/setInnerHtml
ru.naumen.security.sanitizeHtml=true
# Обнаружение аномалий сессии — смена IP-адреса или User-Agent.
ru.naumen.security.session.anomalyDetection.enabled=true
##############cookie###################
# Имя домена
ru.naumen.security.session.cookie.domainName=
# Устанавливать принудительно параметр HttpOnly для сессионной cookie (JSESSIONID); Логический
ru.naumen.security.session.cookie.httpOnly=true
# Время жизни куки
ru.naumen.security.session.cookie.maxAge=
# Имя сессионной переменной
ru.naumen.security.session.cookie.name=
# Префикс пути, на котором доступны куки
ru.naumen.security.session.cookie.path=
# Устанавливать принудительное значение параметра SameSite для сессионной cookie (JSESSIONID). Варианты - None, Lax, Strict, <пусто>
ru.naumen.security.session.cookie.sameSite=None
# Устанавливать принудительно параметр Secure для сессионной cookie (JSESSIONID)
ru.naumen.security.session.cookie.secure=false
# Имя Cookie
ru.naumen.security.session.embedded.cookie=JSESSIONID
# Embedded session control
ru.naumen.security.session.embedded.enabled=false
# Размер пула, который будет заниматься удалением протухших сессий.
ru.naumen.security.session.embedded.expiration.pool.size=-1
# Интервал бездействия в секундах до того как сессия станет "протухшей"
ru.naumen.security.session.embedded.max_inactive_interval=1800
# Включить расширенное логирование информации о сессии, теле запроса и ответа
ru.naumen.security.session.extendedLogger.enabled=false
# Включить ли логирование заголовков запроса и ответа при включенном расширенном логировании
ru.naumen.security.session.extendedLogger.logHeaders=false
# Включить ли логирование тела запроса при включенном расширенном логировании
ru.naumen.security.session.extendedLogger.logRequestBody=false
# Включить ли логирование куки запроса и ответа при включенном расширенном логировании
ru.naumen.security.session.extendedLogger.logCookies=false
# Обнаружение аномалий сессии — смена пользователя в сессии.
ru.naumen.security.session.principalTransformDetection.enabled=true
#################################################################################
#Включить изменение значения пароля vendor
ru.naumen.security.password.vendor.editEnabled=true
# добавлять UUID пользователя в заголовки запросов (необходимо для записи трафика)
ru.naumen.server.request.addUserUuidToHeader=false
# Ограничение количества пересоздаваемых классов, при перезагрузке загрузчика обновленных классов.
ru.naumen.sessionFactory.classLoader.updatedClassesLimit=100
#Таймаут для soap-запроса
ru.naumen.soap.soap-request-timeout-seconds=180
#################################################################################
# Настройки для сбора стектрейсов в случае долгих операций
# Время в мс, по истечении которого снимется стектрейс долго выполняющегося потока
ru.naumen.stacktraces.collect.allowedDuration=300000
# Включена ли сборка медленных стектрейсов
ru.naumen.stacktraces.collect.enable=false
# Куда сохранять стектрейсы (DATABASE, FILE)
ru.naumen.stacktraces.collect.storage=FILE
# Список операций, для которых запрещается сбор стектрейсов (перечисление через запятую без пробелов)
ru.naumen.stacktraces.collect.forbidden.processes=AdvImport
# Кол-во хранящихся стектрейсов в хранилище
ru.naumen.stacktraces.collect.maxCountSavedStackTraces=100
#################################################################################
# Число, до которого производится точный подсчет объектов на вкладках
# Если количество объектов будет больше данного числа, будет выводиться >${ru.naumen.tab.counting.maxValue}
ru.naumen.tab.counting.maxValue=1000
# Количество объектов на вкладке, после которого начинается кэширование этого количества объектов в сервисе CountObjectsCacheService
ru.naumen.tab.counting.minCacheOnTabs=100
# Время жизни (в мин) кеша количества объектов в сервисе CountObjectsCacheService
ru.naumen.tab.counting.timeLiveCacheOnTabs=0
#Максимальное количество удаляемых временных файлов в одной транзакции
ru.naumen.temp_file.cleaner.batch_size=100
##############ru.naumen.core.server.filestorage.TemporaryFileCleaner###################
#Максимальное количество удаляемых временных файлов за одно прохождение задачи планировщика
ru.naumen.temp_file.cleaner.max_size=3000
#User time tracking
ru.naumen.time_tracking=false
#Tour script module code (tours are disabled when empty)
ru.naumen.tour.module=
# Глубина вложенности транзакций, при которой операция прерывается исключением и производится запись в лог с уровнем ERROR
# По умолчанию = Integer#MAX_VALUE
ru.naumen.tx.depth.error=2147483647
# Глубина вложенности транзакций, при которой начинается запись в лог с уровнем WARN
ru.naumen.tx.depth.warn=5
# Использовать комментарии, обновляющиеся через websocket-канал
ru.naumen.use.live.comments=false
#Использовать ли оптимизацию, которая позволяет не поднимать обратные ссылки на всех формах
ru.naumen.useOptimizationBackLink=true
security.csrf.disable=false
security.headers.frameoptions.disable=false
security.headers.frameoptions.value=SAMEORIGIN
security.password.encoder=bcrypt
# Отправлять в оповещениях уменьшенные копии вместо оригинальных изображений
send.previews.with.rtf=false
# Включает режим приложения в котором система перестает отправлять сообщения во вне (блокируются все исходящие и входящие
# почтовые сервера)
silent.mode=false
#Таймаут устанавливаемый на операции чтения/записи на уровне сокетов.
#Устанавливается в миллисекундах.
#При выставлении таймаута нужно иметь в виду что он установится для
#ВСЕХ сокетов которые использует приложение.
socket.timeout=0
# Задает список ip-адресов с которыми можно производить взаимодействие (забирать и отсылать почту). Имеет смысл только
# если silentMode=true (если сборка на профиле developer, то эти данные будут проигнорированы)
# строка из IP адресов или хостов, через разделитель: ','
suitable.ips=
# Разрешенный час начала запуска задач очистки временных файлов. Значения от 0 до 23
temp.files.cleaner.allowed_hours_begin=0
# Предельный час окончания запуска задач очистки временных файлов. Значения от 0 до 23
temp.files.cleaner.allowed_hours_end=23
# Предоставляет возможность включения/отключения очистки временных файлов в каталоге javamelody, может находиться в состоянии true (включен) или false (отключен)
temp.files.cleaner.service.clean.javamelody.enable=false
# Отвечает за включение механизма по чистке временных файлов, может находиться в состоянии true (включен) или false (отключен)
# Доступна для изменения в runtime.
temp.files.cleaner.service.enable=false
# Нужно ли учитывать иерархию контрагенты при получении шаблона объекта на форме добавления
template.client.no.hierarchy=true
#Разрешенный час начала запуска задач очистки. Значения от 0 до 23
textencodingservice.cleaner.allowed_hours_begin=22
#Предельный час окончания запуска задач очистки. Значения от 0 до 23
textencodingservice.cleaner.allowed_hours_end=6
#Размер пачки для задачи очистки
textencodingservice.cleaner.batch_size=1000
#Время жизни сгенерированных ссылок, дней
textencodingservice.objectListLinksLifetime=30
#Использовать в DefaultMetaObjectToDtObjectMapper ограничение количества элементов при конвертации значений
#предопределенных в коде списочных атрибутов (те у которых accessor = ru.naumen.core.server.common.ReflectionAccessor)
use.hardcoded.relation.collection.limit=true
# Включить/отключить старую логику работы silent mode до реализации
# http://sd-jira.naumen.ru/browse/NSDPRD-5033
# Старая логика не проверяет возможность подключения к удаленной системе на уровне сети
use.legacy.silent.mode=false
# Минимально необходимое кол-во пользователей не администраторов после которого будет пересчитываться кеш инлайн МИ
users.threshold.for.recalc=-1
# Идентификатор включения дополнительных проверок заполненности обязательных атрибутов и комментариев при смене статуса
workflow.transitions.extended_actions_check=false