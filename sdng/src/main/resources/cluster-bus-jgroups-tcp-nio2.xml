<!--
  dabramov: проверенный дефолтный конфиг на TCP_NIO2.

  Пример настройки JGroups. Стек протокол и их параметры.
  Необходимо соблюдать последовательность объявления протоколов
-->
<config xmlns="urn:org:jgroups"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xsi:schemaLocation="urn:org:jgroups http://www.jgroups.org/schema/jgroups-5.2.xsd">

	<!-- Транспортный протокол -->
	<TCP_NIO2 bind_addr="${naumen.cluster.bind.addr:localhost}"
			  bind_port="${naumen.cluster.bind.port:7802}"
			  send_buf_size="640k"
			  sock_conn_timeout="300"
			  thread_naming_pattern="cl"
			  use_virtual_threads="true"
			  thread_pool.enabled="true"/>
	<!-- Протокол для совмещения ранее разбитых групп -->
	<MERGE3 max_interval="30000"
			min_interval="10000"/>
	<!-- Протокол определения пропавших нод -->
	<FD_SOCK2 bind_addr="${naumen.cluster.bind.addr}"/>
	<!-- Протокол определения пропавших нод через heart-beat пакеты -->
	<FD_ALL3/>
	<!-- Протокол проверки подозреваемой в "смерти" ноды -->
	<VERIFY_SUSPECT2 timeout="1500"/>
	<!-- Протокол для гарантирования доставки сообщений и сохранения порядка доставки сообщений от ноды. ( FIFO guarantees that all messages from sender P will be received in the order P sent them) -->
	<pbcast.NAKACK2 xmit_interval="200"
					xmit_table_num_rows="50"
					xmit_table_msgs_per_row="1024"
					xmit_table_max_compaction_time="30000"
					use_mcast_xmit="false"
					discard_delivered_msgs="true"/>
	<!-- Удаление уже полученных всеми нодами сообщений. Сборка сетевого мусора -->
	<pbcast.STABLE desired_avg_gossip="5000" max_bytes="1M"/>
	<!-- Протокол объединения в группы. Отвечает за (при)отсоединение нод и формирование нового состава группы с уведомлением нод о составе	                -->
	<pbcast.GMS print_local_addr="true" join_timeout="3000"/>
	<!-- Multicast flow control между отправителем и всеми нодами-получателями. Предотвращение избыточного общения нод -->
	<MFC max_credits="4M"
		 min_threshold="0.4"/>
	<!-- Протокол для разбиения большого сообщения на кучку маленьких при отправке и наоборот -->
	<FRAG4 frag_size="60K"/>
	<!-- Таймаут синхронных сообщений сейчас 100 секунда. Интервал повторных отправок - 2 сек -->
	<RSVP resend_interval="2000" timeout="100000"/>
</config>