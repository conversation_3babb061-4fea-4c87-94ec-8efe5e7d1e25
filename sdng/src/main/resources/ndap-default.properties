# Включать ли слушатель очереди Queue.bridgeNdapAlert. Принимает значения: true/false. Значение по умолчанию: null
# При значении null слушатель запустится в зависимости от наличия кластерной конфигурации и типа ноды
eventActions.ndapAlerts.enabled=
# Number of threads for Queue.bridgeNdapAlert
eventActions.ndapAlerts.threadNumber=1-5

# Максимальное количество потоков при синхронизации объектов аудита
# Если значение 0, то рассчитывается как (0.75 * число ядер ЦП), но не менее 2
ndap.audit.synchronization.threadCount=0
# NDAP connection and read timeout, ms
ndap.rest.timeout=15000
# NDAPServerCheckerJob. Задача планировщика проверки доступности серверов мониторинга.
# Период проверки доступности серверов мониторинга в миллисекундах
ndap.scheduler.NDAPServerCheckerJob.period=300000
# Количество потоков для запросов доступности серверов мониторинга
ndap.scheduler.NDAPServerCheckerJob.threadCount=3

# Отключение кэширования системно-вычислимых атрибутов метрики и триггера
ndap.systemComputableAttrsCache.enabled=true

# Параметры для создания центрального сервера, если он не существует
ru.naumen.ndap.centralServer.url=http://localhost:9999
ru.naumen.ndap.centralServer.username=
ru.naumen.ndap.centralServer.password=
ru.naumen.ndap.centralServer.password.enc=

# Параметры для обратной совместимости приложения со старыми настройками Artemis
# В случае если в dbaccess.properties будут указаны новые имена настроек, то они переопределят указанные ниже.
ru.naumen.jms.artemis.server.standalone_acceptor=${hornetq.server.standalone_acceptor:false}
ru.naumen.jms.artemis.server.standalone_acceptor.ssl.enabled=${hornetq.server.standalone_acceptor.ssl.enabled:false}
ru.naumen.jms.artemis.server.standalone_acceptor.ssl.needClientAuth=${hornetq.server.standalone_acceptor.ssl.needClientAuth:true}
ru.naumen.jms.artemis.server.standalone_host=${hornetq.server.standalone_host:localhost}
ru.naumen.jms.artemis.server.standalone_port=${hornetq.server.standalone_port:5445}
