ADImageDecoder.incorrectDecoder="Incorrectly specified decoder: "{0}". Possible values: {1}"
#ru.naumen.metainfo.server.utils.AbstractCopyOperation
AbstractCopyOperation.emptyCopyList=List of objects in which the values of specified attributes are to be copied - empty.
AbstractCopyOperation.finish=Copy of attributes - finish.
AbstractCopyOperation.impossibleCopyForFqn=Unable to copy attributes for objects of the metaclass ''{0}'', because: {1}
AbstractCopyOperation.impossibleCopyOperation=Unable to copy attributes because of the following error: {0}
AbstractCopyOperation.start=Copy of attributes - start.
#ru.naumen.metainfo.server.utils.AbstractCopyValidator
AbstractCopyValidator.finish=Validation of attributes - finish.
AbstractCopyValidator.start=Validation of attributes - start.
AbstractImportMetaInfoStrategy.validationTemplateListAttributeGroup=Parameter "List template" ({0}) refers to the attribute group, which does not exist in the system: uuid = "{1}".
AbstractImportMetaInfoStrategy.validationTemplateListUsagePointInContent=Parameter "List template" ({0}) in the list "Usage places" refers to the list, which does not exist in the system: uuid = "{1}".
AbstractImportMetaInfoStrategy.validationTemplateListUsagePointInLeftMenu=Parameter "List template" ({0}) in the list "Usage places" refers to the menu item, which does not exist in the system: uuid = "{1}".
AbstractImportMetaInfoStrategy.validationUITemplateMissingDefault=Parameter "Default template" in the metaclass "{0} ({1})" refers to the template, which does not exist in the system: uuid = {2}.
AbstractLeftMenuItemActionHandler.itemNotAvailableForProfiles=It is necessary Mark the "View a list of objects on a separate page" permission marker for the profiles: {0}.
#ru.naumen.metainfo.server.utils.AbstractValueEditor
AbstractValueEditor.success=Copy of attributes completed successfully.
AccessKeyDaoImpl.inactive=The page cannot be displayed: Authorization key [{0}] is inactive
AccessKeyDaoImpl.lifetimeExpire=The page cannot be displayed: The authorization key [{0}] expired
AccessKeyDaoImpl.notFound=The page cannot be displayed: authorization key [{0}] not found or cannot be reused
AccessKeyDaoImpl.notPortalRest=Portal license expired or not installed
ActionType.changeTracking=Change tracking
ActionType.integration=Send to external queue
ActionType.notification=Notification
ActionType.push=Push
ActionType.pushMobile=Push for Mobile Client
ActionType.pushPortal=Notice in portal
ActionType.script=Script
AdminLogOperation.objectCannotBeCreated=Object of the class "Record of admin actions log" cannot be created.
AdminLogOperation.objectCannotBeDeleted=Object of the class "Record of admin actions log" cannot be deleted.
AdminLogOperation.objectCannotBeEdited=Record of admin actions log with UUID "{0}" cannot be changed.
AdminSettings.addSuperUserWithoutProfiles=Superuser with login "{0}" was created without a user administration profile. This superuser's access to the administrator and operator interfaces is limited.
AdminSettings.deleteProfileAssociatedSuperUserWithSingleProfile=Unable to delete the administration profile with code "{0}" as it is the only administration profile linked to the superuser(s): {1}.
AdminSettings.deleteProfileAssociatedWithSuperUsers=Unable to delete administration profile with code "{0}", the profile is associated with superusers: {1}.
AdvImportSchedulerTask=Synchronization
#ru.naumen.core.server.advlist.export.DeferredExport.AdvlistExportListener
Advlist.export.downloadMail.description=Link for downloading file with list of {0} objects of class ''{1}'': <br><a href="{2}">{2}</a><br><br>Only user who exported list can download file.<br>File will be available for download {3} (until {4}).
Advlist.export.downloadMail.subject=Object list of the class ''{0}'' formed {1}.
#ru.naumen.core.server.advlist.GetAdvlistSettingsSetActionHandler
AdvlistSettings.authorDeleted=Author of the view is deleted
#ru.naumen.core.server.advlist.DeleteAdvlistSettingsActionHandler
AdvlistSettings.cannotDelete=You cannot delete settings of the list of the other user.
#ru.naumen.bcp.server.validation.AggregateValueValidator
AggregateValueValidator.parameterError=Aggregated values ''{0}'' and ''{1}'' of the parameter ''{2}'' are not linked
AggregateValueValidator.valueError=Aggregated values ''{0}'' and ''{1}'' of the attribute ''{2}'' are not linked
AllScriptModulesCompilationService.errorUsedOtherModules=Module resources are used in other modules: {0}
ApiCriteriaPropertyColumn.baseLocaleIsNotAvailable=Unable to access <title.base> because a catalog item has no base locale.
ApiCriteriaPropertyColumn.localeIsNotAvailable=Unable to access <title.{0}> because locale {0} is not in the list of available ones.
ApiCriteriaPropertyColumn.localizationDisabled=Unable to access <title.{0}> because localization of the title attribute is off.
AssignableAdministratorProfile=To assign an administration profile "{0}" it is necessary to add at least one access marker within the profile.
AssignableAdministratorProfiles=To assign an administration profiles "{0}" it is necessary to add at least one access marker within the profiles.
Association.default=Association {0}
Association.main=Main association {0}
AttachmentDownloadServlet.ErrorInvalidIndex=Invalid attachment index
AttrTemplates.attrCannotBeComposite=The attribute cannot be composite. The template of composite attribute "{2}" contains this attribute "{0}" ({1})
AttrTemplates.attrRequirements=not computable and not composite attribute codes ($'{'attr'}', where attr - attribute code) of current metaclass. Allowed to specify not computable and not composite attribute codes following types: Real number, Data, Date/time, Boolean, Link to BO, String, Integer, Catalog item and system attributes "{0}" (metaclass) and "{1}" (state)".
AttrTemplates.attrUsedInTemplate=The attribute is used in the template of attribute {0}
AttrTemplates.canBeFilledBy=The field can be filled in with
AttrTemplates.cannotBeFilledWith=Not allowed to specify:
AttrTemplates.currentAttrCode=current composite attribute code.
AttrTemplates.defaultPasswordTitle=Password
AttrTemplates.defaultStateTitle=State
AttrTemplates.isComposite=The attribute "{0}" cannot be used in template. Attribute is composite.
AttrTemplates.isComputable=The attribute "{0}" cannot be used in template. Attribute is computable.
AttrTemplates.isForbiddenSystem=The attribute "{0}" cannot be used in template. Specified code of system attribute "{1}"
AttrTemplates.jsonAttrCode=code of attribute type "Value of dynamic fields".
AttrTemplates.linkToSameClassUsed=The attribute "{0}" cannot be used in template. Specified code of attribute with type "Link to BO", which refers to the current metaclass.
AttrTemplates.linkedObjectAttrCode=code of the attribute type "Attribute of related object".
AttrTemplates.noSuchAttr=The attribute "{0}" in template does not exist in metaclass {1}
AttrTemplates.sameAttr=The attribute "{0}" cannot be used in template. Specified code of current attribute.
AttrTemplates.sboAttrToCurrentMetaClassCode=code of the attribute type "Link to BO" if it refers to the current metaclass.
AttrTemplates.sboAttrToMetaClassWithCompositeTitleCode=code of the attribute type "Link to BO" if in the class, that is referenced by this attribute, the attribute "Title" (title) is a composite.
AttrTemplates.strConstants=string constants;
AttrTemplates.systemAttrCode=code of system attribute "{0}" ({1});
AttrTemplates.templateAttrDescription="{0}" (type "{1}" class "{2}")
AttrTemplates.thereIsLinkToClass=The attribute cannot be composite. The template of composite attribute "{0}" contains attribute with type "Link to BO", which refers to the current metaclass.
AttrTemplates.usesObjWithCompositeTitle=The attribute {0} cannot be used in template. Attribute "{1}" (title) is composite in the class "{2}:{3}".
AttrTemplates.wrongAttribute=Attribute "{0}" cannot be used in template. The attribute has illegal type for using in template. Completing instructions are in [Help] section.
AttrTemplates.wrongFormat=Invalid template format. Completing instructions are in [Help] section.
#ru.naumen.metainfo.server.utils.AttributeHelper
AttributeHelper.attrNotExists=Attribute ''{1}'' not found in the metaclass ''{0}''.
AttributeHelper.attrTypes=Type ''{2}'' set for the attribute ''{0}'' in the metaclass ''{1}'', while one of the following types expected: {3}.
AttributeHelper.attrTypesIsNotEquals=Attributes types do not match
AttributeHelper.checkCatalogs=Failed to copy as attributes are the catalog items (or set of the catalog items), but refer to different catalogs.
AttributeHelper.checkLinks=Failed to copy the attribute ''{0}'' of the metaclass ''{1}'' to the attribute ''{2}'' of the metaclass ''{3}'' as these attributes refer to the objects of different classes.
AttributeHelper.classNotExists=Class ''{0}'' does not exist!
AttributeHelper.emptyCopyList=List of objects in which the values of specified attributes are to be copied - empty.
AttributeHelper.equalsTypes=Attributes are of different types: ''{0}'' and ''{1}''.
AttributeHelper.getRelObj=Object related to the object "{0}" by the attribute ''{1}'' does not exist.
AttributeHelper.oneRelObj=Unable to get the single related object because:\n{0}
AttributeHelper.referencedTypesIsNotEquals=Classes or catalogs, referenced by the attributes, do not match
AttributeHelper.typeNotExixts=Type ''{0}'' in the class ''{1}'' does not exist!
AttributeType.shortName.arbitrary=Arbitrary
AttributeType.shortName.boLinks=BO Links
AttributeType.shortName.bool=Bool
AttributeType.shortName.date=Date
AttributeType.shortName.dateTime=Date/Time
AttributeType.shortName.double=Double
AttributeType.shortName.dtInterval=Time Int.
AttributeType.shortName.file=File
AttributeType.shortName.integer=Integer
AttributeType.shortName.object=BO Link
AttributeType.shortName.richtext=RTF Text
AttributeType.shortName.string=String
AttributeType.shortName.text=Text
AttributesForSearchResults=Attributes for search results
AuthenticationApi.getAccessKeyForEmptyLogin=contains invalid link: user login not specified
#ru.naumen.core.server.script.api.AuthenticationApi
AuthenticationApi.userNotFound=contains invalid link: user with the login ''{0}'' not found in the system
AuthenticationApi.userNotFoundByCertificate=User by the certificate "{0}" not found in the system
AuthenticationApi.userNotFoundByUUID=contains invalid link: user with the uuid ''{0}'' not found in the system
#ru.naumen.core.server.AuthorizationServiceImpl.checkPermission
AuthorizationService.accessDenied=You don''t have permissions to perform this operation
AuthorizationService.accessDeniedInClass=You don''t have permissions to {0} in the class {1}
AuthorizationService.accessDeniedInType=You don''t have permissions to {0} in the type {1} of the class {2}
AuthorizationService.accessDeniedOn=You don''t have permissions to {0}
AuthorizationService.copingCommentToMasterDenied=You don''t have permissions to copy comments from slave to master object
AuthorizationService.copingCommentToSlaveDenied=You don''t have permissions to copy comments from master to slave objects
AuthorizationService.editAttrNoAccess=You don''t have permissions to edit the attribute ''{0}'' in the class/type {1}
AuthorizationService.editAttrNotEditable=Attribute {0} of the object {1} is not editable
AuthorizationService.readOnlyMode=Action unavailable: read-only mode
AuthorizationService.roleScriptError=Computation error of the role ''{0}'':\n{1}
AuthorizationService.systemFileAccessDenied=You don''t have permissions to view system files
AuthorizationService.viewAttrNoAccess=You don''t have permissions to read the attribute ''{0}'' in the class/type {1}
AvdListSemanticFilteringAttrSchemaUpdater.indexCreationException=Failed to enable "Semantic filtering in advanced lists", an error occurred while creating the index. Try turning on the option again. If the error persists, contact technical support.
BarcodeApi.emptyParameters=Incorrect input parameters values
BarcodeApi.incorrectSize=Incorrect width/height of barcode image
BarcodeApi.unsupportedBarcodeType=Unsupported barcode type
BarcodeApi.unsupportedImgType=Unsupported barcode image format
#ru.naumen.bcp.server.registry.BcpResourceRegistration.init()
BcpResourceRegistration.init.info=Business operations from the {0} registered
BeforeAttributeDeleteEvent.usedInDynamicFieldConfiguration=The attribute is used in actual dynamic field configuration.
BeforeAttributeGroupDeleteListener.errorUsedInComplex=The group is used in option "The group of attributes list" for attributes : {0}.
BeforeAttributeGroupDeleteListener.errorUsedInComplexParam=The group is used in option "The group of attributes list" for parameter of event action {0}
BeforeAttributeGroupDeleteListener.errorUsedInComplexParamPart=''{0}''(Event action ''{1}'')
BeforeAttributeGroupDeleteListener.errorUsedInMobilePropertiesList=The group is used in settings of the content ''{0}'' ({1}) of the object card in the mobile application with the code ''{2}''.
BeforeDeleteScriptModuleEvent.errorUsedInMobileLoginForm=The script module is used in applications: {0}.
BeforeDeleteScriptModuleEvent.errorUsedInMobileLoginSettings=The script module is used in the mobile application settings ("Other" tab, "Security" block).
BeforeMetaClassDeleteEvent.usedInDynamicFieldConfiguration=The class is used in actual dynamic field configuration.
#ru.naumen.core.server.bo.bop.AbstractCalculateValueMapOperation
CalculateValueMapOperation.attrDoesNotHaveDefValue=Values cannot be calculated using correspondence table and set by default for the following attributes: {1}.
CannotLicensedAndUnlicensed=The employee {0} cannot be licensed and unlicensed simultaneously.
Catalog.icons.description=Contains icons that can be used for the controls placed in the display interface (Buttons, Attributes of "Set of catalog items" type and the "Catalog item" type).
Catalog.icons.title=Icons for controls
Catalog.icons.vector.description=Contains icons that can be used for controls placed in the display interface (Left Menu, Custom buttons located at attribute values).
ChangeTracking.autoReload=Updated automatically.
ChangeTracking.body=Text
ChangeTracking.commentAdded={0} has added comment: {1}.
ChangeTracking.commentEdited={0} has edited comment: {1}.
ChangeTracking.editFormOpened={0} is editing: {1}.
ChangeTracking.errorInScript=error in script
ChangeTracking.fileAdded={0} has added file {1}.
ChangeTracking.object=object: ''{0}''
ChangeTracking.objectEdited={0} has changed: {1}.
ChangeTracking.responsibleChanged={0} has changed responsible to {1}.
ChangeTracking.script=Script
ChangeTracking.stateChanged={0} has changed state to {1}.
ChangeTracking.warn.disabled=The action will not be performed because change tracking is disabled.
ChangeTracking.warn.emptyTo=The action will not be performed because the list of recipients is empty (the "To" field is not filled).
ChangeTracking.warn.liveCommentsConflict=The setting may be incorrect. For the "{0}" events, different actions are configured in the web interface at the same time: "Auto-update" (enabled in the configuration settings) and "Notification about a change with the Update button". We recommend disabling auto-update in the configuration settings.
ChangeTracking.warn.otherEventsConflict=The action may be configured incorrectly. For the event "{0}", different actions are configured in the web interface at the same time: "Auto-update" and "Notification about a change with the Update button".
ChangesInPermissionSetUnlicUsers.casePrefixFirstCapital=Type
ChangesInPermissionSetUnlicUsers.casePrefixLowercase=type
ChangesInPermissionSetUnlicUsers.classPrefixFirstCapital=Class
ChangesInPermissionSetUnlicUsers.classPrefixLowercase=class
ChangesInPermissionSetUnlicUsers.editAttrGroup=Edit Attributes
ChangesInPermissionSetUnlicUsers.emptyResult=There are no changes in access rights for unlicensed users. Reconfiguration of the rights is not required.
ChangesInPermissionSetUnlicUsers.marker=Marker
ChangesInPermissionSetUnlicUsers.viewAttrGroup=View attributes
CheckPermissionAccessMarker=You don''t have the permission to perform this operation. To get access, you need to contact the administrator.
CheckPermissionSets=You don''t have the permission to edit the set of settings "{0}" ("{1}"), so the current setting "{2}" ("{3}") belonging to this set is not available for editing.
#ru.naumen.metainfo.server.spi.listeners.CheckRoleProfileUsageListener.onApplicationEvent()
CheckRoleProfileUsageListener.roleUsedInProfiles=Role is used in the following profiles: {0}
#ru.naumen.metainfo.server.spi.listeners.CheckUiListener
CheckUiListener.usedInChangeCaseForm=is used in settings of the change case form of classes and types: {0}
CheckUiListener.usedInChangeResponsibleForm=is used in settings of the change responsible form of classes and types: {0}
CheckUiListener.usedInContent=is used in settings of the content {0}
CheckUiListener.usedInEditForm=is used in settings of the edit form of classes and types: {0}
CheckUiListener.usedInMassEditForm=is used in settings of the mass edit form "{0}" of classes and types: {1}
CheckUiListener.usedInNewForm=is used in settings of the add form of classes and types: {0}
CheckUiListener.usedInQuickForm=is used in settings of the quick add and edit form "{0}" of classes and types: {1}
CheckUiListener.usedInTemplates=is used in settings of the templates: {0}
CheckUiListener.usedInWindow=is used in settings of the interface of classes and types: {0}
#ru.naumen.metainfo.server.impl.ClassMetainfoUsedListener
ClassMetainfoUsedListener.ObjectListExists=The type {0} cannot be deleted. The type is used in settings of the interface of classes and types: {1}.
ClassMetainfoUsedListener.linkExists=Type is used in settings of the class {0}.
Comment=Comment
Comment.format=Comment by {0}. Author {1}
#ru.naumen.core.server.bo.CommentDtoMapper
CommentDtoMapper.employee=Employee
#ru.naumen.core.server.CommonUtils
CommonUtils.secGroupWithGivenTitlesDoesntExist=A user group with the names "{0}" does not exist.
CompOnEdit.loopDependency=The script used to compute value during edit specified forms loop dependence with the attributes: {0}
ConcurrentLicenseExceeded=The maximum number of concurrently working users for the licenses {0} exceeded.
#ru.naumen.core.server.systeminfo.SystemInfo
ConfiguredPropertiesContainer=Application settings (spring)
ConnectionEvent.failure=Scheduler task {0} could not connect to the incoming mail server {1} for the following reason:
CopyMassProblemAttrsOperation.changeMassProblemInheritedAttrsDisabled=Attribute values ''{0}'' can not be changed while the request is related to the mass request.
#ru.naumen.core.server.bo.bop.CopyMassProblemAttrsOperation
CopyMassProblemAttrsOperation.changeWfInheritedAttrsDisabled=Values of the attributes ''{0}'' cannot be changed since they are inherited from the mass request.
CopyOperation.methodFailed=System error. Parameters transferred to the method incorrectly. Please, contact your support.
CustomForm.cantDelParameter=Parameter "{0}" cannot be deleted.
CustomForm.changeCase=Change case form
CustomForm.changeResponsible=Change responsible form
CustomForm.changeState=Transition form
CustomForm.invalidParameterInDateTimeRestrictionScript=The value restriction script refers to non-existent parameter: {0}
CustomForm.invalidParametersInDateTimeRestrictionScript=The value restriction script refers to non-existent parameters: {0}
CustomForm.paramUsedInDateTimeRestriction=Parameter is related to parameter: "{0}".
CustomForm.quickAddAndEdit=Quick add and edit forms
CustomForms.codeMustBeUnique=Parameter code must be unique within event.
CustomForms.invalidParamsInCOFScript=The script used to compute value refers non-existent parameters: {0}
CustomForms.invalidParamsInFiltrationScript=The script used to filter values during edit refers non-existent parameters: {0}
CustomForms.loopDetected=The script used to compute value specified forms loop dependence with the parameters: {0}
CustomForms.paramCannotBeAdded=Parameter with the code ''{0}'' cannot be added.
CustomForms.paramCannotBeEdited=Parameter with the code ''{0}'' cannot be edited.
DBStorageOperations.unableCompressFileError.fileIsUsed=Unable to compress file {0} because file is used.
DBStorageOperations.unableDeleteFileError.fileIsUsed=Unable to delete file {0} because file is used.
DBStorageOperations.unableEditFileError.fileIsUsed=Unable to edit file {0} because file is used.
DBStorageOperations.unableReadFileError.fileIsUsed=Unable to read file {0} because file is used.
DecodeParametersActionHandler.contactYourAdministrator=Please contact your administrator to resolve the problem.
DecodeParametersActionHandler.gridParametersIncorrect=The hierarchy tree display parameters are incorrect ({0}).
DecodeParametersActionHandler.linkAttributeNotFound=link attribute not found
DecodeParametersActionHandler.listParametersIncorrect=The list display parameters are incorrect ({0}). Please contact your administrator to resolve the problem.
DecodeParametersActionHandler.objectToLinkNotFound=object to link with list objects not found
DecodeParametersActionHandler.placeUnaccessible=This menu item is undefined for superuser
DefaultDao.relationNotExists=Parent link between objects ''{0}'' and ''{1}'' does not exist.
DefaultMetaObjectToDtObjectMapper.superuser=Superuser
DelAttributeGroupActionHandler.commentAttributeGroupDelete=Attribute group is used in configuring workflow items: {0}
#DelAttributeGroupActionHandler
DelAttributeGroupActionHandler.errorMessage=Attribute group ''{0}'' cannot be deleted. {1}
DelAttributeGroupActionHandler.group=Group
DelAttributeGroupActionHandler.linkToContentAttributeGroupDelete=Attribute group is used in configuring menu items: {0}
#ru.naumen.metainfo.server.spi.dispatch.sec.DelAttributeMarkerActionHandler.processDelete(String, ClassFqn)
DelAttributeMarkerActionHandler.markerDeclaredAtParent=Marker is defined in the parent class or type.
DelAttributeMarkerActionHandler.markerIsSystem=This is the system marker.
#ru.naumen.metainfo.server.spi.dispatch.DelMetaClassActionHandler
DelMetaClassActionHandler.afterDelete.clazz=Class
DelMetaClassActionHandler.afterDelete.type=Type
DelMetaClassActionHandler.afterDeleteError={0} cannot be deleted. {1}
DelMetaClassActionHandler.cantDelete.clazz=Class ''{0}'' cannot be deleted.
DelMetaClassActionHandler.cantDelete.type=Type ''{0}'' cannot be deleted.
DelMetaClassActionHandler.catalog=catalog
DelMetaClassActionHandler.clazz=class
DelMetaClassActionHandler.errorMessage={0} is referenced by additional attributes of classes and types: {1}
DelMetaClassActionHandler.metaClassUsedInTimer={0} is used in the timers: {1}.
DelMetaClassActionHandler.relatedToFormParams={0} is referenced by parameters: {1}
#ru.naumen.metainfo.server.spi.dispatch.sec.DelSecurityRoleActionHandler.execute()
DelSecurityRoleActionHandler.systemRoleCanNotBeDeleted=Can not delete system role "{0}".
DeleteAttributeActionHandler.linkToContentAttributeDelete=Attribute is used in configuring menu items: {0}
#ru.naumen.core.server.filestirage.bcp.DeleteFileOperation
DeleteFileOperation.errorDeleteFileUsed=File is used by the object ''{0}''.
DeleteMetaClassNavigationSettingsListener.usedCaseInBreadCrumb=There are "breadcrumbs" elements for this type.
DeleteMetaClassNavigationSettingsListener.usedClassInBreadCrumb=There are "breadcrumbs" elements for this class.
DeleteMetaClassNavigationSettingsListener.usedClassInHomePage=Class/type ''{0}'' cannot be deleted. It or its nested types are used in the settings of the home page: {1}
DeleteMetaClassNavigationSettingsListener.usedInLeftMenuElements=Class/type ''{0}'' is used in the left menu settings in the elements: {1}
DeleteMetaClassNavigationSettingsListener.usedInTopMenuElements=Class/type ''{0}'' is used in the top menu settings in the elements: {1}
DeleteNavigationMenuItem.itemIsAbsent=Top menu item with the code "{0}" not found. Item moved or deleted.
DeleteNavigationMenuItem.parentItemIsAbsent=Top menu item with the code "{0}" specified in the field "Nested in section" not found. Item moved or deleted.
DeleteReportTemplateActionHandler.canNotDelete=Template "{0}" cannot be deleted for the following reasons: {1}
DeleteTimerDefinitionActionHandler.error=Timer ''{0}'' cannot be deleted. Timer is referenced by the additional attributes of classes and types: {1}.
#ru.naumen.metainfo.server.spi.dispatch.wf.DeleteWfProfileActionHandler
DeleteWfProfileActionHandler.canNotDeleteUsedAt=Profile of related workflows ''{0}'' cannot be deleted. The profile is used in the following objects: {1}
EditAttributeForm.invalidAttributeInDateTimeRestrictionScript=The value restriction script refers to non-existent attribute: {0}
EditAttributeForm.invalidAttributesInDateTimeRestrictionScript=The value restriction script refers to non-existent attributes: {0}
EditNavigationMenuItemActionHandler.chapterContainNestingChapters=Menu section ''{0}'' contains another sections and cannot be nested in another section
EditNavigationMenuItemActionHandler.chapterNestedInAnotherChapter=Menu section ''{0}'' nested in another section and cannot contain any sections
EditSecurityProfileActionHandler.cannotReset=Profile settings cannot be saved for the following reasons:
#ru.naumen.metainfo.server.spi.dispatch.wf.EditStateEnabledActionHandler
EditStateEnabledActionHandler.stateUsedInWfProfile=State ''{0}'' cannot be disabled. It is referenced by profiles of related workflows: {1}
#ru.naumen.core.server.script.api.EmployeeApi
EmployeeApi.cannotSetEmulatedStack=Cannot set emulated stack mode
EmployeeApi.cannotSetStrippedStack=Cannot set strip stack mode
EntityCounterContainer=Information about the number of objects
ErrorDetails.childObject=Child object {0}
ErrorDetails.employeeOuRelationInValueMap=The employee cannot be deleted from department. This relation is used in correspondence table ''{1}''.
ErrorDetails.employeeServiceCallResponsible={0} is/was responsible for objects: {1}.
ErrorDetails.employeeTeamRelatedTo=You cannot unlink. Employee within the team is related to the following non-archive objects: {1}
ErrorDetails.employeeTeamRelationInValueMap={0} cannot be deleted from team. This relation is used in correspondence table ''{1}''.
ErrorDetails.forObject={0} to the object {1}.
ErrorDetails.incompleteList=The list is incomplete, the full list is not displayed due to performance limitations.
#ru.naumen.core.server.util.ErrorDetails
ErrorDetails.relatedTo={0} is related to the following objects: {1}.
ErrorDetails.relatedToWithAttribute=The value of the "{2}" attribute cannot be changed because the {0} is linked to the following objects: {1}.
ErrorDetails.relatedToWithAttribute_f=The value of the "{2}" attribute cannot be changed because the {0} is linked to the following objects: {1}.
ErrorDetails.relatedTo_f={0} is related to the following objects: {1}.
ErrorDetails.serviceCallClient={0} is the client of open requests: {1}
ErrorDetails.serviceCallResponsible={0} is responsible for open requests: {1}
ErrorDetails.serviceCallResponsible_f={0} or its member is responsible for open requests: {1}
ErrorDetails.teamCanNotDeletedCauseResponsible_f=Team or its members are/were responsible for the following objects: {1}.
ErrorDetails.forbiddenForSystemIcons=The operation is forbidden for system icons.
ErrorDetails.usedAsDefaultParamValue={0} is used as the default value for parameters: {1}.
ErrorDetails.usedAsDefaultValue={0} is used as the default attribute value: {1}.
ErrorDetails.usedAsDeterminer={0} is used to define attribute values: {1}.
ErrorDetails.usedAsIconForObjectMenu=Object actions menu
ErrorDetails.usedAsIconForUIElement={0} is used as the icon for control: {1}.
ErrorDetails.usedAsMetaClassProperties={0} is used in settings of metaclasses: {1}.
ErrorDetails.usedInAttrPresentation={0} is used in attribute representation settings : {1}
ErrorDetails.usedInDefaultSCProperties={0} is used as "Default request parameter" for the following classes/types: {1}
ErrorDetails.usedInValueMap={0} is used as value in correspondence tables: {1}.
ErrorDetails.vMapHasRemovedValues=catalog ''Correspondence tables'' item is related to the following archive objects: {0}.
EscalationScheme.codeAlreadyExist=Escalation scheme with the code "{0}" already exists.
EscalationScheme.codeMustBeUnic=Escalation scheme with the code "{0}" can not be added. The code must be unique.
EscalationScheme.deleteDefaultObject=Escalation scheme {0} is used as the default value in the following correspondence tables for escalation schemes: {1}
EscalationScheme.deleteTargetValue=Escalation scheme {0} is used as the definable value in the following correspondence tables for escalation schemes: {1}
EscalationScheme.eventActionsBadFqn=Escalation scheme cannot be changed. Types of objects in the escalation scheme do not match the types of objects of related actions: {0}
EscalationScheme.rulesSettingsCodeMustBeUnic=Item of correspondence table with the code "{0}" can not be added. The code must be unique.
EscalationScheme.usedAsDefaultValue=Escalation scheme cannot be disabled. It is used as the default value in the correspondence tables: {0}
EventAction.ChangeFqnsDenied=An error has occurred while loading an event action with the code "{0}". The set of object cases cannot be changed.
EventAction.ChangeTypeDenied=An error has occurred while loading an event action with the code "{0}". The event type is not editable.
EventAction.DeleteMultiEscalationBadFqns=Action {0} is used in the escalation schemes {1}.
EventAction.DeleteSingleEscalationBadFqns=Action {0} is used in the escalation scheme {1}.
EventAction.ErrorCompileGroovyScript=The field "{0}" contains error: "{1}"
EventAction.NonEdit=Event action with the code "{0}" can not be edit. Updated the list.
EventAction.NonUniqueCode=Event action with the code "{0}" can not be added. The code must be unique.
EventAction.SaveMultiArriveMessageOnQueue=Event action cannot be created. The queue already has an event action of the "Message arrival in the queue" type or the queue was removed.
EventAction.SaveMultiEscalationBadFqns=Action {0} is used in the escalation schemes {1}. To save changes to class/type of objects, please delete the current action from the escalation scheme and try again
EventAction.SaveSingleEscalationBadFqns=Action {0} is used in the escalation scheme {1}. To save changes to class/type of objects, please delete the current action from the escalation scheme and try again
EventAction.attachedFiles=Files attached to object
EventAction.changedMetaClassError=Event action executing is canceled, because object type was changed.
EventAction.error=An error has occured during execution of synchronous event action. Please, contact the system administrator.
EventAction.notificationContent=Notification text
EventAction.subject=Subject
EventAction.template=Template
EventAction.warn.disabled=Action was off by tags. Action will not be executed at the specified event. To enable the event, enable all used tags or remove tags from the action.
EventAction.warn.expiredLicense=The action was disabled due to the expiration of the license for the {0} module. To enable the action, update the license file.
EventAction.warn.expiredNDAP=NDAP
EventActionApi.absentEventAction=There is no action on the event with the code: {0}
#ru.naumen.core.server.dispatch.ExecScriptActionHandler
ExecScriptActionHandler.UnsupportedCharset=The script contains unsupported encoding ''{0}''
ExecuteScriptTask=Script
FileAliasValidationOperation.error=File alias ''{0}'' must include at least one character, but not more than {1}, begin with the latin alphabet character and consist only of latin alphabet characters and numbers.
FillFileAttrOperation.errorFileEmpty=File wasn''t uploaded. Unable to upload blank file.
FillFileAttrOperation.errorLargeFile=File wasn''t uploaded. File''s size exceeds the limit set by your system administrator ({0} MB).
FillFileAttrOperation.errorLargeFilesGroup=The total size of uploaded files exceeds the maximum size ({0} MB). If you need to upload all selected files contact your system administrator
FillFileAttrOperation.errorLongTitle=File cannot be uploaded. File name cannot contain more than 255 characters.
#ru.naumen.core.server.filestirage.bcp.FillFileAttrOperation
FillFileAttrOperation.errorNoFileTransferred=File wasn''t uploaded. Possible reasons: file size exceeds the limit, the server file system is not writable, or the file is considered malicious.
FillFileAttrOperation.errorOnlyOneFile=Single file should be selected in this context.
FillFileAttrOperation.errorUnacceptableExtension=File wasn''t uploaded. Unacceptable extension.
FilteredTreeCache.notExist.error=When executing the script for filtering values of the attribute ''{1}'' ({1}) of the class/type ''{2}'' ({3}), an error occurred: {4}.
#ru.naumen.core.server.treefilter.FilteredTreeCache
FilteredTreeCache.notPermittedTypes.warn=Script to filter values of attribute ''{0}'' ({1}) of the class/type ''{2}'' ({3}) returned illegal values: {4}.
FolderCatalog.Description=Contains folders for objects of the class {0}.
#ru.naumen.core.server.dispatch.GetDtObjectTreeSelectionStateActionHandler
GetDtObjectTreeSelectionStateActionHandler.objectsSelected=Selected: {0} (objects of class ''{1}'')
GetNavigationReferencesToTabActionHandler.referenceToTabBar={0} (content)
GetPossibleCasesForAddingBOActionHandler.emptyPossibleList=No type has been declared for the class. Objects cannot be created.
GetPossibleCasesForAddingBOActionHandler.permissionError=You have no rights for this operation
GetReportParametersActionHandler.reportParameterWithNotExistDtObject=This report uses a non-existent object
HandlerUtils.validatePermittedTypes.error=The default value ''{0}'' do not meet type restrictions: type ''{1}'' is not selected in the ''Object types'' list
HandlerUtils.validatePermittedTypesOnCompositeTitle.permittedTypeHasCompositeTitle=Current attribute cannot point to type ''{0}'' of class ''{1}'' because \n1.Attribute ''{2}'' (''{3}'') in that type is composite and \n2.The template of composite attribute ''{4}'' (''{5}'') contains current attribute
Hierarchy.export.downloadMail.description=Link for downloading file with {0} objects from a hierarchical tree ''{1}'': <br><a href="{2}">{2}</a><br><br>Only user who exported list can download file.<br>File will be available for download {3} (until {4}).
Hierarchy.export.downloadMail.subject=List of objects from a hierarchical tree ''{0}'' formed {1}.
HyperlinkTitleColumnDBRestriction=Attribute ''{0}'' ({1}) of Hyperlink type of ''{2}'' ({3}) metaclass can not contain more than {4} symbols in title.
HyperlinkUrlColumnDBRestriction=Attribute ''{0}'' ({1}) of Hyperlink type of ''{2}'' ({3}) metaclass can not contain more than {4} symbols in URL.
ImportMetainfoAction.uploadError=Metainformation cannot be loaded for technical reasons (see the application log).
ImportMetainfoAction.uploadError.attributeValidation=Metainformation cannot be uploaded. Error in validating reference attribute  {0} ({1}): the object class has been changed in attribute being loaded.
ImportModulesActionHandler.changeAttributeType=Settings from the file cannot be uploaded. For the type/class ''{0}'' in attribute with code ''{1}'' value type in uploaded settings differs from current value type.
ImportModulesActionHandler.isNotAnInteger="Module version" attribute''s value is not an integer number.
ImportModulesActionHandler.mandatoryElemsAreEmpty=The following required parameters are not specified: {0}.
ImportModulesActionHandler.moduleCode=module code
ImportModulesActionHandler.moduleText=module text
ImportModulesActionHandler.overrideAndDeclarationTogether=Settings from the file cannot be uploaded. For the type/class ''{0}'', attribute with code ''{1}'' is declared and overridden in uploaded settings simultaneously.
ImportModulesActionHandler.overrideWithoutDeclaration=Settings from the file cannot be uploaded. For the type/class ''{0}'', currently declared attribute with code ''{1}'' is declared overridden in uploaded settings, with no declaration provided from parent types/classes.
ImportModulesActionHandler.wrongCode=Incorrectly specified code: "{0}".
ImportModulesActionHandler.wrongFileFormat=Invalid file format selected
ImportReportTemplatesActionHandler.importError=Templates import error: {0}
ImportReportTemplatesActionHandler.loadError.wrongFileFormat=An error occurred. Templates can not be uploaded. File has invalid format.
ImpossibleToDelete.TaskMustHaveNoMessages=You cannot delete the task ''{0}'' ({1}):\n - There are unprocessed messages.
#ru.naumen.mailreader.server.dispatch.DeleteInboundMailServerConfigActionHandler
InboundMailServerConfig.cantDeleteHasTask=You cannot delete incoming mail server settings, since scheduler task ''{0}'' refers to them
InboundMessageEnqueuer.logDebug=Enqueued message: {0}
IncorrectAddresses=incorrect e-mail addresses of recipients
InternalApplication.IOError=Error reading embedded application file
InternalApplication.applicationFileCorrupted=Unable to decode the embedded application file "{0}" ({1}).
InternalApplication.applicationFileNotFound=No application file
InternalApplication.cannotConnectToInternalApplicationsServer=Problem with connection to built in application server
InternalApplication.cantDecryptApplication=Unable to decrypt file. Please contact a provider of the embedded application.
InternalApplication.editArchiveError=An error occurred while changing files in the archive of the embedded application ''{0}''. {1}
InternalApplication.editScriptModuleInArchiveError=An error occurred while changing the script module ''{0}'' in the archive of the embedded application ''{1}''. {2}
InternalApplication.fileIsNotZipped=Invalid embedded application file format "{0}" ({1}). File must be in zip format and not empty.
InternalApplication.internalApplicationError=Internal application error
InternalApplication.internalApplicationErrorWithDetails=Internal application error ({0})
InternalApplication.licenseValidate.authorNotSpecified=The file author is not specified.
InternalApplication.licenseValidate.cantBeLoaded=The licence file cannot be uploaded.
InternalApplication.licenseValidate.checksumIncorrect=The checksum of the embedded application file is incorrect.
InternalApplication.licenseValidate.creationDateInFuture=The file creation date is specified in the future.
InternalApplication.licenseValidate.creationDateNotSpecified=The file creation date is not specified.
InternalApplication.licenseValidate.expirationDateIncorrectFormat=License expiration date has an incorrect format. The parameter value must be specified in the format "YYYY.MM.DD".
InternalApplication.licenseValidate.expirationDatePassed=License expiration date has passed.
InternalApplication.licenseValidate.formatError=The licence file cannot be uploaded because it has an inappropriate format.
InternalApplication.licenseValidate.hashNotSpecified=The checksum of the embedded application file is not specified.
InternalApplication.requiredFilesError=The archive of the embedded application "{0}" ({1}) must contain the index.html file or files with the licensed embedded application: license.xml and app.zip.
InternalApplication.requiredIndexHtmlError=File index.html must be in the archive.
InternalApplication.silentModeError=Connection to server of internal applications is unavailable. Silent Mode is on.
InternalApplication.uploadCompleted=Application files were uploaded
InternalApplication.uploadCompletedWithWarning=Application files were uploaded. Several files were not uploaded because of missing of embedded applications: {0}
InternalApplication.uploadFailedWithWarning=Application files were not uploaded because of missing of embedded applications: {0}
JaxbStorageLicenseSerializer.actionsForUnlicensed=blocks of actions for unlicensed users:
#LicenseImportValidation
JaxbStorageLicenseSerializer.formatException=The value of parameter ''expirationDate'' has invalid format in following {0}. The value of parameter must be specified in the format ''YYYY.MM.DD''.
JaxbStorageLicenseSerializer.groupsOfModules=groups of modules:
JaxbStorageLicenseSerializer.licenseGroups=license groups:
JaxbStorageLicenseSerializer.outOfRangeDate=The value of parameter ''expirationDate'' is invalid in following {0}. The specified dates do not exist.
LicenseContainer=License information
#ru.naumen.core.server.license.LicenseChecker
LicenseDisabled=You do not have permissions to log in with the licenses {0}
LicenseExpired=Licenses {0} expired
LicenseMissing=Licenses {0} not set
LicenseValidation.existingObjectsViolateLicense=The number of existing objects exceeds the allowed number from the license file.
LicenseValidation.metaClassNotFound={0} is missing on the stand, the quota ''{1}'' is allocated to existing classes/types.
LicenseValidation.metaClassesNotFound={0} are missing on the stand, the quota ''{1}'' is allocated to existing classes/types.
LicenseValidation.missingQuotaAttributes=The license file is missing the required attributes: {0}.
LicenseValidation.moreThanOneQuotaForClass=In the license file one metaclass ''{0}'' is limited by several quotas: ''{1}'', ''{2}''.
LicenseValidation.nonUniqueQuotaCode=The license file contains several quotas with the same name.
#Mail Log
Mail.titleTemplate=Mail № {0} "{1}"
MailAttachmentDescription=Mail attachment; \nSender: {0} \n Subject: {1}
MailAttachmentDescription.emptySubject=not specified
MailAttachmentDescription2=Mail attachment, \nSubject: {1}, \n Sender: {0}
MailLogOperation.objectCannotBeCreated=Object of the class "Record of mail processing log" cannot be created.
MailLogOperation.objectCannotBeDeleted=Object of the class "Record of mail processing log" cannot be deleted.
MailLogOperation.objectCannotBeEdited=Record of mail processing log with UUID "{0}" cannot be changed.
MailLogRecord.connectionEvent=Connection to incoming mail server
MailLogRecord.processEvent=Mail processing
MailLogRecord.receiveEvent=Getting mails
MailLogRecord.reprocessEvent=Mail reprocessing
MailLogRecord.validationEvent=Mail validation
#ru.naumen.mailreader.server.processor.MailProcessHelper
MailProcessHelper.tooLongRecipientList=Mail message recipient list (''{0}'') is longer than {1} characters, so it will be truncated in mail log. Full recipient list: "{2}"
MailProcessorRule.cantDeleteHasTask=You cannot delete the rule of incoming mail processing, since it is referenced by the scheduler tasks ''{0}''
MailReader.ContentIDParseFailed=Content-ID could not be extracted. Mail content does not comply with RFC 2111
MailReaderSchedulerTask.title=Incoming mail processing
MailReaderSchedulerTaskBase.ErrorFailed=Mail processing error, id {0}: {1}
MailReaderSchedulerTaskBase.hasNoProcessor=For the task ''{0}'' the rule of incoming mail processing is not specified
MailReaderSchedulerTaskBase.hasNoServer=For the task "{0}" incoming mail server not specified
MailReaderSchedulerTaskBase.logDebug=Processing message queue item: {0}
MailReaderSchedulerTaskBase.mailProcessorDisabled=For the task "{0}" the rule of incoming mail processing is disabled
MailReaderSchedulerTaskBase.mailServerDisabled=For the task "{0}" incoming mail server is disabled
MailReceiver.dumpAttachError=Unable to attach message dump to log record: {0}
MailReceiver.messageSavingError=Unable to save message in folder {0}: {1}
MailServerAuthenticationFailed=authentication on outgoing mail server failed
MailServerSettingsIncorrect=incorrect settings of connection to outgoing mail server
MassEditFormNotSetupError=Mass editing is not configured for objects with {0} type
MassOperationTitle.delete=delete
MassOperationTitle.edit=edit
MessageState.attach=mail attached to the business object
MessageState.error=processing error
MessageState.new_bo=business object created
MessageState.outgoing=other
MessageState.reject=mail rejected
#ru.naumen.core.server.flex.spi.BeforeDeleteMetaClassAttributeListaner
MetaClass.deleteError=Existing attributes referring to it:
MetaClass.deleteError.attribute.case=\n"{0}" (type: "{1}" of the class "{2}")
MetaClass.deleteError.attribute.class=\n"{0}" (class: "{1}")
#ru.naumen.metainfo.server.spi.elements.MetaClassImpl
MetaClassImpl.addDeclaredAttribute.duplicateCode=Attribute with the code ''{0}'' cannot be added. Attribute code must be unique within {1}.
MetaClassImpl.addDeclaredAttribute.notAllowedAttrCode=Attribute cannot be added. Value {1} cannot be used as a code of custom attribute
MetaClassImpl.addDeclaredAttribute.ofClass=of class
MetaClassImpl.addDeclaredAttribute.ofType=of type
MetaClassImpl.addDeclaredAttributeGroup.duplicateCode=Tag «code» in the group attributes for the type/class ''{1}'' contains the value ''{0}'' already exists in the type/class ''{2}''. The code must be unique within the type/class.
MetaClassImpl.attribute.cantBeUnique=Attribute with the specified type cannot be unique
MetaClassImpl.attribute.manyToManyEqualsTable=The attribute cannot be added. The attribute of the "Set of BO links" type with the code ''{0}'' already exists. First 8 symbols at the beginning of ''{0}'' code should be unique within "Set of BO links" attribute type. Please change the attribute code.
MetaClassImpl.attribute.manyToManyEqualsTableCatalogs=The attribute cannot be added. The attribute of the ''Set of Catalog items'' type with the code "{0}" already exists. First 8 symbols of the attribute code must be unique within ''Set of Catalog items'' attribute type. Please change the attribute code.
MetaClassImpl.attribute.manyToManyEqualsTableCaseList=The attribute cannot be added. The attribute of the type ''Set of class types'' with the code ''{0}'' already exists. The first 8 characters of the code of the added attribute must be unique within the attribute type ''Link Set" to BO''/ ''Set of directory elements''/''Set of class types''. Change the attribute code.
MetaClassImpl.attributeNotFound=Attribute not found: {0}:{1}
MetaClassImpl.maxIdLengthExceeded=Length of {0} exceeds the maximum value {1}
MetaClassUsedinDefaultScListener.errorDelete=The type is selected as "Default request parameter" for the following classes/types: {0}.
MetaClassUsedinDefaultScListener.errorDeleteChild=The child type ''{0}'' is selected as "Default request parameter" for the following classes/types: {1}.
MetainfoClusterChange.error=The operation could not be completed because metadata editing is blocked, update is in progress. Please try again later.
MetainfoClusterChange.error.partition=The operation could not be completed because metadata editing is blocked, migration to a partitioned table is taking place. Please try again later.
MetainfoClusterChange.outOfSyncNode.error=Change metainfo on out of sync node is forbidden.
MetainfoUtils.clazz=class ''{0}''
MetainfoUtils.of.clazz=of class ''{0}''
MetainfoUtils.type=type ''{0}''
MimeDecoder.ErrorWhileConvertingHTML=HTML conversion errors
MimeDecoder.badEncodingContent=[invalid encoding]
MimeDecoder.file=File
MobileAttributeGroupTitle=Mobile Object card ({0})
MobileNavigation.addFormLinksChapter=Add
MobileNavigation.listLinksChapter=Available lists
#ru.naumen.metainfo.server.spi.dispatch.wf
ModifyWfProfileActionHandler.masterIsRemoved=Type of the master object is in archive: {0}.
ModifyWfProfileActionHandler.profileCantBeSwitchedOn=Profile of related workflows {0} cannot be enabled.
ModifyWfProfileActionHandler.slaveIsRemoved=Type of the slave object is in archive: {0}.
NamedLicenseExceeded=For named licenses {0} the maximum allowed number of users has been exceeded.
NavigationSettingsUIEventListener.contentUsedInLeftMenuElements=Content {0}/{1} cannot be deleted. It is used in the left menu settings in the elements: {2}
NavigationSettingsUIEventListener.contentUsedInTopMenuElements=Content {0}/{1} cannot be deleted. It is used in the top menu settings in the elements: {2}
NavigationSettingsUIEventListener.deleteProfileAttention=Attention.<ol><li>{0}</li></ol>Delete the profile?
NavigationSettingsUIEventListener.linkObjectUUIDNotOfClass=Object with UUID={0} is not an instance of class
NavigationSettingsUIEventListener.listTemplateUsedInHomePageElements=Template ''{0}'' cannot be deleted. The template is used in the settings of the home page: {1}
NavigationSettingsUIEventListener.listTemplateUsedInLeftMenuElements=The template cannot be deleted, as it is used in the menu item settings: {0}
NavigationSettingsUIEventListener.objectWithUUIDNotFoundMessage=Object with UUID={0} does not exist
NavigationSettingsUIEventListener.profileIsOneOfInHomePageElements=The profile has a home page defined. The profile will be excluded from the home page settings  if you delete the profile. The home page for users with this profile will change.
NavigationSettingsUIEventListener.profileIsOneOfInLeftMenuElements=The profile is used in the left menu settings for items: {0}. When you delete the profile, this items will be excluded from the settings in the left menu.
NavigationSettingsUIEventListener.profileIsSingleInLeftMenuElements=The profile is the only one in the visibility settings for left menu items: {0}. These menu items will be disabled if you delete the profile.
NavigationSettingsUIEventListener.profileUsedInHomePage=The profile must contain at least one absolute role, since it is used in the settings of the home page: {0}
NavigationSettingsUIEventListener.profileUsedInLeftMenuElements=The profile must contain at least one absolute role, since it is used in the settings of the left menu: {0}
NavigationSettingsUIEventListener.tabUsedInLeftMenuElements=Tab {0}/{1} cannot be deleted. It or the tabs nested in it are used in settings of the left menu: {2}
NavigationSettingsUIEventListener.tabUsedInTopMenuElements=Tab {0}/{1} cannot be deleted. It is used in the top menu settings in the elements: {2}
NavigationSettingsValidate.absentAddButtonValue=Property "list" in the tag "value" of the top menu item with the code "{0}" does not exist.
NavigationSettingsValidate.absentAddButtonValueFqn=Tag "id" of the top menu item with the code ''{0}'' does not exist.
NavigationSettingsValidate.absentChildrenInChapter=Tag "children" of the top menu item with the code ''{0}'' does not exist.
NavigationSettingsValidate.absentLeftMenuAddButtonValue=Property "fqns" in the tag "fqns" of the left menu item with the code "{0}" does not exist.
NavigationSettingsValidate.absentLeftMenuAddButtonValueFqn=Tag "id" of the left menu item with the code ''{0}'' does not exist.
NavigationSettingsValidate.absentMenuItemTitle=Tag "title" of the top menu item with the code ''{0}'' does not exist.
NavigationSettingsValidate.absentReferenceClassFqn=Property "fqn" in the tag "m:referenceValue" of the top menu item with the code "{0}" does not exist.
NavigationSettingsValidate.absentReferenceValue=Tag "m:referenceValue" of the menu item with the code ''{0}'' does not exist.
NavigationSettingsValidate.absentTab={0} menu item ''{1}'' ({2}) refers to the tab {3}, which does not exist in the system
NavigationSettingsValidate.absentTabs={0} menu item ''{1}'' ({2}) refers to tabs, which do not exist in the system
NavigationSettingsValidate.cardTemplateMissingWarning=Menu item ''{0}'' (''{1}'') refers to the template, which does not exist in the system: uuid = "{2}".
NavigationSettingsValidate.homePage.absentCustomLink=Tag "customLink" of the home page with UUID ''{0}'' does not exist.
NavigationSettingsValidate.homePage.absentProfiles=Tag "profiles" of the home page with UUID ''{0}'' does not exist.
NavigationSettingsValidate.homePage.absentReference=Tag "reference" of the home page ''{0}'' does not exist.
NavigationSettingsValidate.homePage.absentReferenceClassFqn=Property "fqn" in the tag "reference" of the home page ''{0}'' does not exist.
NavigationSettingsValidate.homePage.absentTab=Home page ''{0}'' refers to the tab ''{1}'', which does not exist in the system.
NavigationSettingsValidate.homePage.absentTabs=Home page ''{0}'' refers to tabs, which do not exist in the system.
NavigationSettingsValidate.homePage.absentTitle=Tag "title" of the home page with UUID ''{0}'' does not exist.
NavigationSettingsValidate.homePage.incorrectAttrCode=Home page ''{0}'' not loaded: Attribute with code ''{1}'', specified in home page settings was not found.
NavigationSettingsValidate.homePage.incorrectProfileCode=Home page ''{0}'' not loaded: Profile with code ''{1}'', specified in home page settings was not found.
NavigationSettingsValidate.homePage.incorrectReferenceClassFqn=Property "fqn" in the tag "reference" of the home page ''{0}'' contains invalid value ''{1}''. Property "fqn" must contain the metaclass code.
NavigationSettingsValidate.homePage.incorrectTitle=Tag "title" of the home page with UUID ''{0}'' contains invalid value ''{1}''. Value of the tag must be string with the length from 1 to 64 characters.
NavigationSettingsValidate.homePage.incorrectType=Tag "type" of the home page with UUID ''{0}'' contains invalid value or does not exist.
NavigationSettingsValidate.homePage.incorrectUiTemplateCode=Home page ''{0}'' not loaded: Template with UUID ''{1}'' specified in home page settings was not found.
NavigationSettingsValidate.homePage.nonUniqueHomePageItemCode=Property "UUID" of the home page contains invalid value ''{0}''. Value must be unique within all home page items.
NavigationSettingsValidate.incorrectAddButtonValueFqn=Tag "id" of the top menu item with the code ''{0}'' contains invalid value "{1}". Tag "id" must contain the metaclass code.
NavigationSettingsValidate.incorrectLeftMenuAddButtonValueFqn=Tag "id" of the left menu item with the code ''{0}'' contains invalid value "{1}". Tag "id" must contain the metaclass code.
NavigationSettingsValidate.incorrectListTemplateCode=Tag "listTemplate" of the left menu item "{0}" contains invalid value or does not exist.
NavigationSettingsValidate.incorrectMenuItemTitle=Tag "title" of the top menu item with the code ''{0}'' in localization "{1}" contains invalid value "{2}". Value of the tag must be string with the length from 1 to 64 characters.
NavigationSettingsValidate.incorrectMenuItemTitleLang=Attribute "lang" of the tag "title" of the top menu item with the code "{0}" must be filled in.
NavigationSettingsValidate.incorrectMenuItemType=Property "type" of the top menu item with the code "{0}" contains invalid value or does not exist.
NavigationSettingsValidate.incorrentReferenceClassFqn=Property "fqn" in the tag "m:referenceValue" of the top menu item with the code "{0}" contains invalid value "{1}". Property "fqn" must contain the metaclass code.
NavigationSettingsValidate.incorrentReferenceTabUUID=Property "tab-uuid" in the tag "m:referenceValue" of the top menu item with the code "{0}" contains invalid value "{1}". Property "tab-uuid" must contain uuid of the metaclass tab.
NavigationSettingsValidate.left=Left
NavigationSettingsValidate.nonUniqueMenuItemCode=Property "code" of the top menu item contains invalid value "{0}". Value must be unique within all top menu items.
NavigationSettingsValidate.potentialUnnestedTiles=Quick access panel has tiles for absent left menu items with codes ''{0}''
NavigationSettingsValidate.tab=tab
NavigationSettingsValidate.tabBar=tab bar
NavigationSettingsValidate.top=Top
NoMoreEmployeeLicensesForGroup=The limit of employees number for the license {0} is reached
NoMoreSuperPersonLicenses=The limit of superusers number is reached
Notification.allEmptyEmails=Among the recipients there is no employee with the filled e-mail address.
Notification.brokenFeedbackAddress=technical support e-mail address ({0}) is incorrect
Notification.brokenSenderAddress=sender address ({0}) is incorrect
Notification.currentRecipientTogetherMethodCc=Unable to use parameter "currentRecipient" at the same time with the method "notification.cc" or "notification.ccEmployee", "notification.bcc", "notification.bccEmployee".
Notification.emptyEmails=E-mail addresses not specified
Notification.noOne=to none
Notification.noRecipients=notification recipients not specified
ObjectActions.action=Action
ObjectActions.error.emptyEventAction=To enable a menu item, fill the "Action" field in the item's settings.
ObjectActions.error.relatedEventActionDisabled=To enable a menu, you must enable the associated event action.
ObjectActions.image=Image
ObjectActions.position=Position
ObjectQuota.expirationError=Creation of objects of ''{0}'' type is not available due to licensing restrictions. Contact your system administrator.\nDetails: The quota ''{1}'' has expired.
ObjectQuota.violationError[few]=Creation of objects of ''{0}'' type is not available due to licensing restrictions. Contact your system administrator.\nMore: The ''{0}'' type participates in the quota ''{1}'' with a limit {2} objects.
ObjectQuota.violationError[many]=Creation of objects of ''{0}'' type is not available due to licensing restrictions. Contact your system administrator.\nMore: The ''{0}'' type participates in the quota ''{1}'' with a limit {2} objects.
ObjectQuota.violationError[one]=Creation of objects of ''{0}'' type is not available due to licensing restrictions. Contact your system administrator.\nMore: The ''{0}'' type participates in the quota ''{1}'' with a limit {2} object.
OperationHelper.LinkAdded=linked to objects:
OperationHelper.LinkSevered=unlinked from objects:
OperationHelper.RestoreObjectSeparately=The object must be restored from the archive separately
OperationHelper.added={0} cannot be added for the following reasons:\n{2}
OperationHelper.added_f={0} cannot be added for the following reasons:\n{2}
OperationHelper.agreementArchived=Agreement ''{0}'' is in archive.
OperationHelper.changed={0} ''{1}'' cannot be changed for the following reasons:\n{2}
OperationHelper.changed_f={0} ''{1}'' cannot be changed for the following reasons:\n{2}
OperationHelper.clientArchived=Client ''{0}'' is in archive.
OperationHelper.commentsLimitReached=Comment cannot be added, because the limit of comments added to the object is reached
OperationHelper.copied={0} ''{1}'' cannot be copied for the following reasons:\n{2}
OperationHelper.deleted={0} ''{1}'' cannot be deleted for the following reasons:\n{2}
OperationHelper.deleted_f={0} ''{1}'' cannot be deleted for the following reasons:\n{2}
OperationHelper.error=The operation cannot be performed for the following reasons:\n{0}
#ru.naumen.bcp.server.operations.OperationHelper
OperationHelper.errorWithObject={0} ''{1}'' cannot be {2}.\n{3}
OperationHelper.etc=etc.
OperationHelper.loopError=Loop dependency between the attributes {1} detected
OperationHelper.removed={0} ''{1}'' cannot be archived for the following reasons:\n{2}
OperationHelper.removed_f={0} ''{1}'' cannot be archived for the following reasons:\n{2}
OperationHelper.replaced={0} ''{1}'' cannot be replaced for the following reasons:\n{2}
OperationHelper.replaced_f={0} ''{1}'' cannot be replaced for the following reasons:\n{2}
OperationHelper.restored={0} ''{1}'' cannot be restored from the archive for the following reasons:\n{2}
OperationHelper.restored_f={0} ''{1}'' cannot be restored from the archive for the following reasons:\n{2}
OperationHelper.serviceArchived=Service ''{0}'' is in archive.
OperationUtils.agreementIsNull=Request cannot be added for the following reasons: 1. Required attributes not filled in: SLA
OperationValidator.canCopyError=Objects of this type can not be copy.
OperationValidator.cantBeEmpty=Must be filled in
#ru.naumen.core.server.bo.bop.OperationValidator.checkCatalogItemCode(String, ClassFqn)
OperationValidator.catalogItemWithCodeAlreadyExist={0} with the code ''{1}'' already exists!
OperationValidator.changeServiceMasterMassProblemRestriction=Service cannot be changed. Object {0} is related to the slave objects
OperationValidator.changeServiceSlaveMassProblemRestriction=Service cannot be changed. Object {0} is related to the mass request
OperationValidator.changeTypeMasterMassProblemRestriction=Type cannot be changed. Object {0} is related to the slave objects
OperationValidator.changeTypeSlaveMassProblemRestriction=Type cannot be changed. Object {0} is related to the mass request
#ru.naumen.core.server.bo.bop.OperationValidator.checkUnique(T, String, Attribute, Object)
OperationValidator.checkAttrUnique.error=Attribute {0} must be unique. Object {1} with this value of the attribute already exists: {2}.
OperationValidator.checkChangeStateVisibleInState.error=The action is not set up in state ''{0}'' for the type of this object
#ru.naumen.core.server.bo.bop.OperationValidator.checkParent(P, C)
OperationValidator.checkParent.error.parentIsAChild={1} ''{0}'' cannot be moved. The object cannot be the parent to itself.
#ru.naumen.core.server.bo.bop.OperationValidator.checkParentIsNotDescendant(P, T)
OperationValidator.checkParentIsNotDescendant.error={1} ''{0}'' cannot be moved to the object ''{3}'' of the class {2}. The supposed parent is the child of the object to be moved.
#ru.naumen.core.server.bo.bop.OperationValidator.checkParentIsNotRemoved(P, T)
OperationValidator.checkParentIsNotRemoved.error={1} ''{0}'' cannot be moved to the object ''{3}'' of the class {2}. Parent object is in the archive. Restore parent object or select new location for the object.
OperationValidator.checkParentIsNotRemoved.folderUnArchiveError=The folder ''{0}'' cannot be restored from archive. Parent object is also in the archive. Restore parent object or select new location for the object.
OperationValidator.checkParentIsNotRemoved.unArchiveError={1} ''{0}'' cannot be restored from archive. Parent object is also in the archive. Restore parent object or select new location for the object.
OperationValidator.checkParentRelation.error.parentNotAllowed={1} "{0}" cannot be moved to the object ''{3}'' of the class {2}.
#ru.naumen.core.server.bo.bop.OperationValidator.checkParentRelation(P, T)
OperationValidator.checkParentRelation.error.parentNotNull={1} ''{0}'' cannot be moved to the hierarchy root.
#ru.naumen.core.server.bo.bop.OperationValidator.checkPermittedTypesRestriction()
OperationValidator.checkPermittedTypesRestriction.Multy.error=Values ''{0}'' do not meet type restrictions of the attribute ''{1}''
OperationValidator.checkPermittedTypesRestriction.Single.error=Value ''{0}'' do not meet type restrictions of the attribute ''{1}''
OperationValidator.checkToolPrecenseOnCards.error=The action is not set up for the type of this object
OperationValidator.exceedsMaxLength=The value length exceeds maximum size {0}
OperationValidator.illegalContains=Contains illegal characters
OperationValidator.postFillAttrsAreNotFilled={0}: on state ''{1}'' exit required attributes not filled in - {2}
OperationValidator.preFillAttrsAreNotFilled={0}: on state ''{1}'' entry required attributes not filled in - {2}
OperationValidator.reqCompAttrsIsNotSet=is required "{0}" is required. One of the following attributes should be filled in: {1}.
#ru.naumen.core.server.bo.bop.OperationValidator.checkAttrsRestrictions(T)
OperationValidator.requiredAttrsIsNotSet=The following required attributes not filled in: {1}.
#ru.naumen.core.server.bo.team.FinalTeamValidationOperation
OperationValidator.teamShouldContainItsLeader=Leader of the team must be its member
OperationValidator.validateBOAfterProcess.error.caseIsEmpty=Object type not specified
#ru.naumen.core.server.bo.bop.OperationValidator.validateBOAfterProcess()
OperationValidator.validateBOAfterProcess.error.caseIsRemoved=Object type deleted
OperationValidator.valueFiltered=''{0}'' cannot be the value of the attribute ''{1}''
PeriodicTrigger.interval.mandatory=Duration must be specified.
Priority.High=High
Priority.Normal=Normal
ProcessMailEvent.failure={0} was processed by the mail processing rule {1} with an error:
ProcessMailEvent.interrupt=Mail processing has interrupted due server stopped
ProcessMailEvent.issues={0} was processed by the mail processing rule {1} with the peculiarities:
ProcessMailEvent.success={0} was successfully processed by the mail processing rule {1}.
PropertiesFilesContainer=Configuration files (*.properties)
PropertiesXmlContainer=Configuration files (*.xml)
ProtocolAdapter.logDeletedMessageError=Message was deleted on server, skipping.
ProtocolAdapter.logError=Message with subject ''{0}'' has decoding error, skipping. See file {1}.
ProtocolAdapter.logFolderClosedOrSocketTimeoutError=Message with subject ''{0}'' could not be processed. Failed to access the socket or the folder with the message
ProtocolAdapter.logOutOfMemoryError=Message with subject ''{0}'' is very big. Out of memory error, skipping.
Push.errorInScript=error in script
PushMobile.authError=Authorization error in FCM
PushMobile.cert.check.error=error occurred when checking certificate
PushMobile.error=An error occurred on FCM service side
PushMobile.failedToReceiveAccessToken=Failed to get Firebase token
PushMobile.serviceAccountFileReadingError=Service account JSON file reading error
PushMobile.silentModeOn=silent mode is enabled
PushPosition.bottomLeft=Bottom left
PushPosition.bottomRight=Bottom right
PushPosition.system=System (wide on bottom of screen)
PushPosition.topLeft=Top left
PushPosition.topRight=Top right
PushPresentationType.alwaysInTheInterface=Always in the interface. Additionally, a browser notice if tab is inactive
PushPresentationType.interfaceAndBrowserNotices=The interface and browser notices
PushPresentationType.onlyExternalPush=Only browser notice
PushPresentationType.onlyInTheInterface=Only in the interface
ReceiveMailEvent.folder=folder {0} - {1}
ReceiveMailEvent.head=Scheduler task {0} has been successfully connected to the incoming mail server {1}. The number of detected messages:
ReceiveMailEvent.logErrorMailStorage=Unsuccessful attempt № {0} of sending the mail {1} for processing, there are {2} attempts left
ReceiveMailEvent.received=Number of letters sent to the processing - {0}
ReceiveMailTask=Incoming mail processing
RecipientAddressRejected=Recipient address rejected: mail is not deliverable.
RejectReason.badFormat=invalid message format
RejectReason.bigAttachment=attachment is too large
RejectReason.bigAttachment.oneFileMessage=Attachment "{0}" exceeded the permissible size - {1} MB
RejectReason.bigAttachment.sumFilesMessage=Total files size exceeded permissible value - {0} MB
RejectReason.bigAttachments=attachments are too large
RejectReason.blacklist=field ''{0}'' contains value that does not match filtering conditions specified in catalog ''{1}''
RejectReason.blacklist.field.body=Body
RejectReason.blacklist.field.sender=Sender
RejectReason.blacklist.field.subject=Subject
RejectReason.clientNotFound=failed to determine the client for request association
RejectReason.contactNotFound=failed to determine the contact person for request association
RejectReason.impossibleAttachment=illegal attachment
RejectReason.impossibleAttachment.message=Illegal attachment "{0}" type
RejectReason.invalidEmail=invalid e-mail
RejectReason.invalidEmail.message=Invalid sender address format - {0}
RejectReason.respondBodyIntro=Dear User!\nYour mail was rejected.\n\nThe reason the mail was rejected:
RejectReason.maliciousAttachment=attachment considered to be malicious
RejectReason.other=failed to process message
#ru.naumen.metainfo.server.spi.dispatch.RemoveClassMetainfoActionHandler
RemoveClassMetainfoActionHandler.metaclassUsedInWfProfile={0} cannot be archived. {0} is referenced by profiles of related workflows: {1}
RemoveTimerDefinitionActionHandler.error=Timer ''{0}'' cannot be archived. Timer is referenced by the additional attributes of classes and types: {1}.
ReportContentRename.export=Export report
ReportContentRename.refresh=Refresh
ReportContentRename.save=Save…
ReprocessMailEvent.failure={0} was re-processed by the mail processing rule {1} with an error:
ReprocessMailEvent.issues={0} was re-processed by the mail processing rule {1} with the peculiarities:
ReprocessMailEvent.success={0} was successfully re-processed with the mail processing rule {1}.
ReprocessMessage.notProcessed=Message ''{0}'' has not been processed yet, and therefore it cannot be reprocessed.
ResetUIActionHandler.cannotReset=Card settings cannot be reset for the following reasons:
RestApi.createLink=Add
RestApi.deleteLink=Delete
RestApi.editLink=Edit
RestServiceController.attrNotEditable=attribute "{0}" is not editable
RestServiceController.attrNotEditableInCurrentState=current object state ("{0}") does not allow attribute "{1}" value changing
RestServiceController.attrNotEditableInTransition=object workflow transition from state "{0}" to state "{1}" does not allow attribute "{2}" value changing
#ru.naumen.core.server.rest.RestServiceController
RestServiceController.attributeWarning=Call {0}: {1}
#ru.naumen.core.server.rest.accesskeys.AccessKeyDaoImpl
RestServiceController.emptyAccessKey=You must specify the authorization key in the accessKey parameter
RestServiceController.exec.error.script.param=Script reading error.
RestServiceController.execScriptLegacyPattern=[а-яА-ЯёЁA-Za-z0-9,\\[\\]$''\\"_\\s\\*\\-%@&\\\\]*
RestServiceController.getEmployeeByAccessKey.unavailable=You have no permission to execute the method or the integration option is disabled.
RuntimeContainer=Information about the virtual machine
SaveBreadCrumbActionHandler.validateExistAttributes=The "breadcrumbs" element cannot be added. The attribute "{0}" does not exist in metaclass "{1}".
SaveBreadCrumbActionHandler.validateUniqueCrumb=The "breadcrumbs" element cannot be added. Metaclass "{0}" may be present only in one "breadcrumbs" element.
SaveEmbeddedApplicationActionHandler.applicationUsagePointWithCodeAlreadyExist=Usage place application with this code ''{0}'' already exists.
SaveEmbeddedApplicationActionHandler.applicationWithCodeAlreadyExist=Application with this code ''{0}'' already exists.
SaveEmbeddedApplicationActionHandler.invalidCode=Application has invalid code ''{0}''. The "Code" field must include at least one character, but not more than {1}, begin with the latin alphabet character and consist only of latin alphabet characters and numbers.
SaveReportTemplateActionHandler.templateFileNotSpecified=File for the template with the code ''{0}'' not specified
SaveReportTemplateActionHandler.templateFileWrong=The specified template file is not the report template or contains errors
SaveReportTemplateActionHandler.templateWithCodeAlreadyExist=Template with this code ''{0}'' already exists
#ru.naumen.metainfo.server.spi.dispatch.scheduler.SaveTriggerActionHandler
SaveTriggerActionHandler.concreteDateTrigger.error=The date/time of the task execution cannot be less than the current one.
SaveTriggerActionHandler.concreteDateTrigger.null.error=You must specify date/time of the task execution.
SaveTriggerActionHandler.periodicTrigger.error=Number of months less than 12 must be specified.
#ru.naumen.metainfo.server.spi.dispatch.SaveUIActionHandlerBase
SaveUIActionHandlerBase.changeCaseForm=change case form
SaveUIActionHandlerBase.changeResponsibleForm=change responsible form
SaveUIActionHandlerBase.contentCodeCantBeUnique=The content with the code "{0}" can not be added. The code must be unique among contents of {1} of class/type.
SaveUIActionHandlerBase.contentCodeCantBeUnique.tab=The tab with the code "{0}" can not be added. The code must be unique among tabs of the customizable tab bar.
SaveUIActionHandlerBase.editForm=edit form
SaveUIActionHandlerBase.newEntryForm=add form
SaveUIActionHandlerBase.objectCard=object card
SaveUIActionHandlerBase.quickAddAndEditForm.codeMustBeUnique=Form with the code "{0}" can not be added. The code must be unique within the class.
SchedulerTaskAlreadyExists=Scheduler task with the code ''{0}'' can not be added. The code must be unique.
SchedulerTaskNotExists=Scheduler task with ''{0}'' code does not exist.
SchemaRegistrationService.init.info=Registration scheme from {0}
Script.NonEdit=Script with the code "{0}" can not be edit. Updated the list.
ScriptApi.call.error=An error occurred when calling the method {0} with parameters {1}
#ru.naumen.bcp.server.validation.ScriptFilteredValueValidator
ScriptFilteredValueValidator.parameterError=Value ''{0}'' does not meet filter restrictions of the parameter ''{1}''
ScriptFilteredValueValidator.valueError=Value ''{0}'' does not meet filter restrictions of the attribute ''{1}''
#ru.naumen.core.server.script.ScriptHelper
ScriptHelper.wrongChecksum=Checksum of the module "{0}" is incorrect.
ScriptService.MaxLength=maximum text or script size in the notification is exceeded (65535 characters)
ScriptService.MaxNoticeScriptLength=maximum size of the script exceeds used in the notice (65535 characters)
ScriptService.MaxScriptLength=maximum script size used in the notification is exceeded (65535 characters)
ScriptServiceException.errorCompilingModule=Compilation error in module {0}. {1}
ScriptServiceException.errorCompilingModules=Compilation error in modules. {0}
ScriptServiceException.invalidAnnotations=Invalid annotation usage in module {0}. Look for details in the application log.
#ru.naumen.core.server.script.ScriptServiceException
ScriptServiceException.transactionTimeout=Script transaction is canceled, probably, execution is timed out.
#ru.naumen.core.server.script.spi.ScriptUtils
ScriptUtils.attributeNotExists=Editable object does not contain attributes ''{0}''.
ScriptUtils.attributeTypeMustBeLinkBO=Link attribute type must be - ''Link to BO''
ScriptUtils.endTimeMustBeAfterStartTime=The end time of the event must be later than the start time
ScriptUtils.fileNotFoundInExternalStorage=File with id ''{0}'' not found in external storage
ScriptUtils.fileNotFoundOrDirectory=File {0} not found, or it is a directory
ScriptUtils.incorrectDate=Event time must be between 02.01.1900 and 01.01.3000
ScriptUtils.notFilledRequiredAttrs=Required attributes not filled in - {0}
ScriptUtils.notSupportAttachFromExternal=File not attached. Active storage must have type ''groovy'', change settings in file-storage.xml
ScriptUtils.objecUUIDtNotExists=Object with the specified identifier ''{0}'' does not exist.
ScriptUtils.wrongEditValue=The value ''{0}'' does not correspond to the type of the editable attribute ''{1}''.
ScriptUtils.wrongFileParamType=Object with the given identifier does not match File type.
ScriptUtils.wrongLinkedClass=Building a complete tree of identifiers of the business object hierarchy is not possible: the passed FQN of the class or the string with its FQN - ''{0}'' does not match the object type ''{1}'' referenced by the Link attribute ''{2}'' in the class ''{0}''.
ScriptUtils.wrongStorageType=It is available for disk storage. Check your storage type
#ru.naumen.fts.server.actionhandlerstub
Search.ModuleIsMissing=Search module is missing.
SecSettings.groupExists=The user group with the code "{0}" cannot be added. The user group code must be unique.
SecSettings.groupNotExists=Group doesn''t exists:
SecSettings.systemGroupExists=Group cannot be added. Value "{0}" cannot be used as a code of custom group.
#ru.naumen.core.server.bo.bop.SetAgreementsOperation
SetAggrementsOperation.removedAgreementsInDefaultSCParams=Service within the SLA "{0}" is used as the default request parameter for the following classes/types: {1}
#ru.naumen.bcp.server.operations.SetAttrValueOperationCreateOnly.validate()
SetAttrValueOperationCreateOnly.AttrValueChangeIsDenied=Change of the value of the attribute ''{0}'' is not permitted!
SetAttrValueOperationCreateOnly.CreationDateChangeIsDenied=Value of the attribute "Creation date" (creationDate) of the object ''{0}'' ({1}) cannot be changed.
#ru.naumen.core.server.bo.bop.SetCallCasesOperation
SetCallCasesOperation.removedCallCasesInDefaultSCParams=Service and request type "{0}" is used as the default request parameter for the following classes/types: {1}
SetEmployeeLicenseOperation.employeeResponsibleForStates={0} is automatically set as responsible for the objects in states: {1}
#ru.naumen.core.server.bo.employee.SetEmployeeLicenseOperation
SetEmployeeLicenseOperation.error=License type cannot be changed. \r\n {0}
SetEmployeeLicenseOperation.lastPerformerInTeamResponsibleForStates={0} is the only performer in the team ''{2}'', automatically set as responsible for the objects in states: {1}
SetEmployeeLicenseOperation.responsibleForOpenedObj={0} is responsible for objects: {1}
SetEmployeeLicenseOperation.teamResponsibleForStates={0} is automatically set as responsible for the objects in states: {1}
SetEmployeeLoginOperation.employeeIsRemoved=The attribute "{1}" of employee "{0}" has not been changed, because the employee is in archive.
#ru.naumen.core.server.bo.bop.SetEmployeeRemovedOperation
SetEmployeeRemovedOperation.lastPerfOfRespTeam=Employee ''{2}'' is the only performer in the team ''{0}'', responsible for the objects: {1}
SetEmployeeRemovedOperation.respWithinTeamForObj=Within team ''{0}'' employee ''{2}'' is responsible for the objects: {1}
SetInboundMessageStatusBusinessAction.logWarn=Attempt to change the state of non-existent InboundMailQueueItem; uuid = {0}
#ru.naumen.core.server.catalog.SetItemParentOperation.validate()
SetItemParentOperation.ItemMustNotBeAFolderParent=Catalog item cannot be parent of the catalog folder.
SetMasterMassProblemOperation.disabledStatus={0} is in the ''{1}'' state, where is not permitted to add link.
SetMasterMassProblemOperation.hasNoMassProblemFlag=Master object ''{0}'' has no massProblem flag
SetMasterMassProblemOperation.hasNoSuitableWfProfile=Appropriate profile of related workflows to link object {0} to the object {1} does not exist
SetMasterMassProblemOperation.wfProfileNotFound=Profile of related workflows with the code ''{0}'' not found
SetMetaClassOperation.Validate.addObject.isRemoved=Object cannot be added for the following reasons: The type ''{0}'' is in the archive.
#ru.naumen.core.server.bo.bop.OperationValidator
SetMetaClassOperation.Validate.isRemoved=Type cannot be changed. Type ''{0}'' is in archive
SetMetaClassOperation.Validate.isSystemCase=Type cannot be changed. Type ''{0}'' is system.
#ru.naumen.core.server.bo.bop.SetMetaClassOperation
SetMetaClassOperation.requiredAttrsIsNotSet={0}: required attributes not filled in - {1}
SetRemovalDateOperation.dateIsNull=Archive date cannot be set for the archive object
SetRemovalDateOperation.dateIsntNull=Archive date cannot be set for the non-archive object
#ru.naumen.bcp.server.operations.SetRichTextOperation
SetRichTextOperation.imageInCommentIsNotAvailable=The lifetime of the images uploaded in the comment text has expired. Please upload them again.
SetRichTextOperation.imageInAttributeIsNotAvailable=The lifetime of the images uploaded in the attribute "{0}" has expired. Please upload them again.
#ru.naumen.core.server.catalog.servicetime.SetServiceTimeExclusionsOperation
SetServiceTimeExclusionsOperation.checkExclusionDateUnique=Exclusion cannot be added. Non-unique date value specified
SetServiceTimeStateOperation.checkActiveCopy=You cannot approve the service class because it does not have active version
#ru.naumen.core.server.bo.bop.SetServicesOperation
SetServicesOperation.removedServicesInDefaultSCParams=Service "{0}" is used as the default request parameter for the following classes/types: {1}
SetStateOperation.changeState=change state from ''{0}'' to ''{1}'' in class/type ''{2}''
#ru.naumen.core.server.bo.bop.SetStateOperation.equalOldNewValues(String)
SetStateOperation.objAlreadyHasNewState=The object is already in the new state
SetStateOperation.stateIsNotDeclared=State with the code ''{0}'' not defined
SetStateOperation.transitionDisabledError=Transition is not permitted in accordance with the workflow settings
#ru.naumen.bcp.server.operations.SetValueMapItemAttrsOperation.validate()
SetValueMapItemAttrsOperation.AttrValueChangeIsDenied=An error in correspondence table with the code ''{0}''. The set of defining attributes is not editable.
SilentMode=Silent mode
SilentMode.ipsIsIncorrect=Incorrect IP format
SilentModeOrMailServerDisabled=outgoing mail disabled or silentMode enabled
StringColumnDBRestriction=Attribute ''{0}'' ({1}) of String type of ''{2}'' ({3}) metaclass can not contain more than {4} symbols.
StringContainsXSSVulnerability=The field ''{0}'' contains invalid characters. Please enter a valid value.
StringContainsXSSVulnerabilityClient=The field contains invalid characters.
#ru.naumen.sec.server.superuser.SuperUserDetailsService
SuperUserDetailsService.userNotFound=user with the login ''{0}'' not found in the system
SwitchLeftMenuItemActionHandler.badItem=The object specified in the element settings was not found. To enable the item, change its settings.
SystemPropertiesContainer=Application settings (system)
#ru.naumen.core.server.script.api.TeamApi
TeamApi.UUIDsNotExists=Objects with the specified identifiers do not exist: {0}
Timer.Status.ACTIVE=Active
Timer.Status.EXCEED=Expired
Timer.Status.NOTSTARTED=Awaiting
Timer.Status.PAUSED=Paused
Timer.Status.STOPED=Stopped
TimerDefinition.AddWithDuplicateCode=Timer with the code ''{0}'' cannot be added. The code must be unique.
ToolTitle.add=Add
ToolTitle.addComment=Add comment
ToolTitle.addDeleteObjButton=Add/Remove links
ToolTitle.addFile=Add file
ToolTitle.addObject=Add object
ToolTitle.addRelation=Add link
ToolTitle.addSc=Add request
ToolTitle.calcStatus=Check
ToolTitle.changeAssociation=Change association
ToolTitle.changePassword=Change password
ToolTitle.changeState=Change state
ToolTitle.changeType=Change type
ToolTitle.copy=Copy
ToolTitle.copyLinkToList=Copy link to the list
ToolTitle.createNewReportWithCurrentParameters=Add report using current parameters
ToolTitle.delete=Delete
ToolTitle.deleteLink=Delete link
ToolTitle.download=Download
ToolTitle.edit=Edit
ToolTitle.editResponsible=Change responsible
ToolTitle.exportAdvlist=Export list
ToolTitle.exportReportButton=Save...
ToolTitle.extendedSearchParams=Search parameters
ToolTitle.filtration=Filtration
ToolTitle.massDelete=delete
ToolTitle.massEdit=mass edit
ToolTitle.more=Show more
ToolTitle.move=Move
ToolTitle.openMassServiceCallForm=Mass operations
ToolTitle.openObjectCard=Open object card
ToolTitle.openPreview=Open preview
ToolTitle.parameters=Parameters...
ToolTitle.print=Print
ToolTitle.rebuildReport=Rebuild
ToolTitle.refresh=Refresh
ToolTitle.refreshList=Refresh list
ToolTitle.remove=Archive
ToolTitle.resetGlobalDefaultSettings=Reset view
ToolTitle.restore=Restore from archive
ToolTitle.savePresentation=Save view
ToolTitle.savePrsOld=Save presentation
ToolTitle.share=Share
ToolTitle.showRelated=Show history of nested objects
ToolTitle.showRemoved=Show archive
ToolTitle.showUnRemoved=Exit from archive
ToolTitle.sort=Sort
ToolTitle.testMetric=Check
UIValidatorBean.applicationIsMissing=There is no application with code ''{0}'' that is used in settings of the interface of classes and types: {1}
UIValidatorBean.attrGroupUsedAtObjectList=Attribute group cannot be deleted. The group is used in settings of the list of objects ''{1}''
UIValidatorBean.attrUseAtObjectList={0} is used in settings of the interface of classes and types: ''{1}''.
UIValidatorBean.codeIsEmpty=Code is empty for content "{0}" in class "{1}"
UIValidatorBean.linkToContent.attrGroupUsedAtObjectList=Attribute group ''{3}'' cannot be deleted, is used in items for left menu: ''{2}''
UIValidatorBean.linkToContent.attrUseAtObjectList=Attribute ''{3}'' cannot be deleted, is used in items for left menu: ''{2}''.
UIValidatorBean.linkToContent.useAtMenuHierarchyGrid=Structure with code ''{3}'' used in menu item settings ''{2}'' does not exist.
UIValidatorBean.linkToContent.useAtObjectList=Class/type ''{3}'' cannot be deleted, is used in items for left menu: ''{2}''.
UIValidatorBean.metaclassHasNoWorkflow=No life cycle associated with class ''{0}''
UIValidatorBean.tabBarNoTabsError=Must be at least one tab
UIValidatorBean.tabLayoutError=Card settings error
UIValidatorBean.useAtAddFileTool=Attribute group cannot be deleted. The group is used in settings of the tool ''{1}''
UIValidatorBean.useAtCustomForm=Attribute group cannot be deleted. The group is used in settings of the custom form ''{1}''
UIValidatorBean.useAtObjectGraphMC=Metaclass is used to configure list of types in net or links schemes ''{1}''
UIValidatorBean.useAtObjectList={0} is used in settings of the interface of classes and types: ''{1}''.
UIValidatorBean.useAtPropertyList=Attribute group cannot be deleted. The group is used in settings of the object card ''{1}''
UIValidatorBean.useAtRelObjPropertyListAttr=Attribute is used to configure parameters of the related object in settings of the interface in the metaclass ''{1}''
UIValidatorBean.useAtRelObjPropertyListGrp=Attribute group is used to configure parameters of the related object in settings of the interface in the metaclass ''{1}''
UIValidatorBean.useAtRelObjPropertyListMC=Metaclass is used to configure parameters of the related object in settings of the interface in the metaclass ''{1}''
UIValidatorBean.useAtSelectParent=Attribute "{3}" is used in the settings of parent selection parameters in the interface settings in the metaclass "{1}"
UIValidatorBean.useAttrRelObjectListAttrLink=Content cannot be saved with contained parameters. Attribute ''{3}'' does not exist in the metaclass ''{1}''
UIValidatorBean.validatorIsNotSet=Validator for the content {0} of the metaclass {1} not specified
UIValidatorBean.windowCaptionAttrNotExists=The setting cannot be saved. The attribute "{0}" does not exist in metaclass "{1}".
UniqueAttributeIndexUpdater.cantCreateIndex=There are objects with the non-unique attribute value.\n Attribute ''{1}'' cannot be unique.
UniqueAttributeIndexUpdater.cantCreateIndexExtededMessage=The values of the attribute ''{0}''({1}) in class ''{2}''({3}) must be unique..
UnknownMailServerHost=unable to connect to outgoing mail server
UnlicensedRuleValidator.absentActionsForUnlicensedCodes=There is no code in one of ''unlicensed action'' blocks.
UnlicensedRuleValidator.absentEventAction=Code of event action not specified by unlicensed block action with code {0}.
UnlicensedRuleValidator.absentEventActionOrScript=In the unlicensed action blocks: {0}, there is no script code and/or action code.
UnlicensedRuleValidator.absentScript=Code of script not specified by unlicensed block action with code {0}.
UnlicensedRuleValidator.hasNotUniqueActionsForUnlicensedCodes=Licence file contains two or more unlicensed action blocks with equal codes.
UnlicensedRuleValidator.objectsForCalcCheckSumNotFound=Warning! Objects with codes: {0} not found.<br>These actions will not be available to unlicensed users.
UnlicensedRuleValidator.wrongCheckSum=Warning! Blocks of actions for unlicensed users: {0} have one or several actions with incorrect checksum.<br>These actions will not be available to unlicensed users.
UploadServiceImpl.errorFileItemNotFound=Item with uuid={0} not found.
#ru.naumen.core.server.upload.spi.UploadServiceImpl;
UploadServiceImpl.errorNoUUIDTransferred=File not uploaded. Possible causes: the file size exceeds maximum value ({0} MB), server''s file system is not available for recording files or it''s a malicious file
#ru.naumen.core.server.license.CheckLicenseSessionControlStrategy
#UsersMaxCountExceededTryLater=The maximum number of users exceeded. Please, try again later.
#UnableToLoginNoLicense=The maximum number of personal licenses exceeded. Please, contact the system administrator.
UserSessionsMaxCountExceeded=The maximum number of sessions of this user exceeded.
ValidateDeleteCatalogEvent.message=Catalog ''{0}'' specified in parameter ''Interface pages'' of lite administrator interface.
ValidateDeleteSecurityGroupEvent.ManyGroupDelAndOneUsedInAdvListSettings.message=The group ''{0}'' is used in the settings of advanced lists in the "Shared views can be created by" option. When a group is deleted, the employees of the group will not be able to create shared views.
ValidateDeleteSecurityGroupEvent.ManyGroupUsedInAdvListSettings.message=Groups ''{0}'' are used in the settings of advanced lists in the "Shared views can be created by" option. When deleting groups, the employees of the group will not be able to create shared views.
ValidateDeleteSecurityGroupEvent.OneGroupUsedInAdvListSettings.message=The group is used in the settings of advanced lists in the "Shared views can be created by" option. When a group is deleted, the employees of the group will not be able to create shared views.
ValidateDeleteSecurityGroupEvent.message=User group ''{0}'' specified in parameter ''Available for user groups'' of lite administrator interface.
ValidateMailEvent.failure=The following error occurred during mail validation: {0}
#ru.naumen.metainfo.server.spi.dispatch.wf.ValidateWorkflowActionHadler
ValidateWorkflowActionHadler.stateUsedInWfProfile=State ''{0}'' cannot be deleted. It is referenced by profiles of related workflows: {1}
#ru.naumen.core.server.catalog.valuemap.ValueMapHelper
ValueMapRow.singleRowNotFound=Unable to find single row using specified defining attributes (found: {0}).
VersionContainer=Version information
VoiceCreationHelper.exceptionInScript=An error occurred while executing the parameter processing script. Report it to responsible for system setup so that he can fix it.
#ru.naumen.core.server.script.api.WebApi
WebApi.employeeIsArchived=The employee is archived
WebApi.employeeLoginIsNull=Login of the employee is null
WebApi.employeeNotFound=No such employee was found
WebApi.fqnCollectionIsEmpty=Collection of object types must not be empty
WebApi.fqnCollectionMustConsistOfFqnsWithSameClassId=Collection of object types must consist of types of the same metaclass
WebApi.fqnCollectionMustIncludeFqnsWithCases=Collection of object types must include at least one id of object type
WebApi.fqnIsNull=Object type must be filled in
WebApi.methodIsDisabled=Method is disabled in configuration file
WebApi.notUUID=String {0} is not uuid
WebApi.objectIsNotSuperuserOrEmployee=The transferred object is not an employee or superuser
WebApi.superuserIsLinkedToAccount=The superuser account must not be linked to a user account
WebApi.wrongAttrSearchSettings=Search is not configured for the attribute {0} or the attribute is not found. Please, contact your administrator.
WebApi.wrongAttrValues=Object attribute values must be of ''String'', ''Object'' type or list of ''String'', ''Object'', other types of attributes can be ''Number'', ''Boolean'', ''Date'', ''Map of String, Number'', ''dtInterval'' or ''Hyperlink''.
absoluteRoles=Absolute roles
abstractBO.domain.1=Show object attributes
abstractBO.domain.10=Delete object
abstractBO.domain.11=Show object card
abstractBO.domain.12=Change object type
abstractBO.domain.13=Archive object
abstractBO.domain.14=Restore object from archive
abstractBO.domain.15=Move object
abstractBO.domain.16=Copy object
abstractBO.domain.17=Add comments
abstractBO.domain.18=Add private comments
abstractBO.domain.19=Delete comments
abstractBO.domain.2=Edit object attributes
abstractBO.domain.20=Edit comments
abstractBO.domain.21=Show comments
abstractBO.domain.22=Show private comments
abstractBO.domain.23=Show object history
abstractBO.domain.24=Show file
abstractBO.domain.25=Add file
abstractBO.domain.26=Delete file
abstractBO.domain.27=File author
abstractBO.domain.28=Comment author
abstractBO.domain.29=Show in the search results list
abstractBO.domain.3=Comments
abstractBO.domain.4=Attached files
abstractBO.domain.5=Other permissions
abstractBO.domain.6=Attributes, always available to read
abstractBO.domain.7=Other attributes
abstractBO.domain.9=Add object
accessDeniedError=You don''t have permissions to the system settings.
actions=Actions
activeFileStorageAreNotS3Type=The method is not available because the active storage does not work using the S3 protocol.
activeUsers=Active users
add=Add
addCommentInStateError=Comment cannot be added to the object in the state "{0}"
addCommentInlineForm=Add comment inline
addExclusionValidationFailed=The exclusion period {0} cannot be added to service classes {1}. Non-unique date value or invalid value of the period start/end is specified.
addingLinkFormCaption=Title of add link form
adminArea=Fixed area of the quick access panel
adminLiteInterface=Lite administrator interface
adminLiteValueMapItem=Correspondence table ''{0}''
administration=Administration
administrationExportImport=Download / Upload
advImport.ColumnNotEmptyFilter=Required column {0} of the item ''{1}'' does not exist
advListExportProcess.Exist.Error=Export of a list with these settings is already scheduled in the queue.
advListProperties=Advanced lists
advListRichTextViewMode=Clean text styles in fields of "Text in RTF format" type to save browser resources
advListRichTextViewMode.RemoveStyles=Yes, for all representations (recommended)
advListRichTextViewMode.RemoveStylesOnlyFromSafe=Yes, except for representations with the "unsafe" mark
advListRichTextViewMode.ShowAsIs=No (may increase resource usage of the browser)
advListViewSave.validation.userCanNotEditError=You have no permissions to edit the view {0}
advListViewSave.validation.userCanNotSaveViewError=User ''{0}'' does not have permissions for saving shared views
advimport=Synchronization
#ru.naumen.advimport.server.dispatch.SaveAdvImportConnectionActionHandler
advimport.AdvImportConfigurationExists=Configuration with the code ''{0}'' cannot be added. The code must be unique.
advimport.AdvImportConnectionExist=Connection with the code ''{0}'' already exists.
advimport.AdvImportConnectionNotExistOrOtherType=Connection with the code ''{0}'' does not exist or of the other type.
advimport.AdvImportHelper.notExist=Import with such identifier does not exist.
advimport.AdvImportHelper.notRunning=Import is not run.
advimport.AdvImportHelper.stopError=Import ''{0}'' cannot be stopped. {1}
advimport.AdvimportApi.noParameter=Parameters ''{0}'' is not set
advimport.AdvimportApi.readParameterError=Failed to read value of parameter ''{0}''
advimport.AdvimportApi.wrongParameter=Invalid value for parameters ''{0}''
advimport.RemoveCustomizer.noRootProvided=Root object for archiving not specified
advimport.classEngine.end=Import ''{0}'' executed in {1} sec. Rows processed: {2}. Objects imported: {3}. Updated: {4}. Skipped: {5}. Errors occurred: {6}.
advimport.classEngine.start=Run import of objects ''{0}''
advimport.classEngine.stop=Import ''{0}'' stopped, {1} sec. Rows processed: {2}. Objects imported: {3}. Updated: {4}. Skipped: {5}. Errors occurred: {6}.
advimport.classEngine.stopNameSpaceSearching=Namespace lookup completed successfully.
advimport.columnDoesNotExists=Value for column ''{0}'' is not found in the data source.
advimport.configEmpty=Import configuration must be filled in
advimport.configNotValid=XML-representation of the configuration is incorrect.\n {0}
advimport.configNotValidUnableToSave=Unable to save the configuration. XML-representation of the configuration is incorrect.\n {0}
advimport.configNotValidXSD=Unable to save the configuration. XML-representation of the configuration is incorrect (line: {0}, character: {1}).
advimport.csv.noFileProvided=File for import not selected
advimport.engine.end=Import executed in {0} sec.
advimport.engine.start=Run import
advimport.skipEnabled=Import mode is ''{0}''. Creating and editing objects disabled.
advimport.titleEmpty=Configuration title must be filled in
advimport.validation.attributeNameEmpty=In the class of import ''{0}'' the title of attribute (name) not specified
advimport.validation.attributeNotExistsError=In the class/type with identifier "{1}" the attribute with non-existent code: "{2}" is specified
advimport.validation.backTimerCustomizerAllowanceColumnError=In the class of import ''{0}'' for the reverse timer customizer (backtimer-customizer) the time float column (allowance-column): "{1}" is specified incorrectly
advimport.validation.backTimerCustomizerDeadLineColumnError=In the class of import ''{0}'' for the reverse timer customizer (backtimer-customizer) the deadline column (deadline-column): "{1}" is specified incorrectly
advimport.validation.byColumnMetaClassResolverColumnError=In the class of import ''{0}'' for the objects type determination rule (by-column-metaclass-resolver) the source column not specified
advimport.validation.byColumnMetaClassResolverMetaClassError=In the class of import ''{0}'' for the objects type determination rule (by-column-metaclass-resolver) the class of objects (metaclass) not specified
advimport.validation.classThreadNumber=In the class of import ''{0}'' incorrect number of threads specified: {1}. Number of threads should be positive integer value
advimport.validation.columnMetaClassResolverColumnError=In the class of import ''{0}'' for the objects type determination rule (column-metaclass-resolver) the source column not specified
advimport.validation.columnNotEmptyFilterError=In the class of import ''{0}'' the filtered column (column) in the filter (column-notempty-filter) is specified incorrectly
advimport.validation.complexConverterEmpty=In the class of import ''{0}'' for the complex conversion rule of values to object (complex-object-converter) the nested rule not specified.
advimport.validation.constantMetaClassResolverColumnError=In the class of import ''{0}'' for the objects type determination rule (constant-metaclass-resolver) the type of objects (metaclass) not specified
advimport.validation.csvDataSourceDelimiterError=In the class of import ''{0}'' for the data source the incorrect delimiter (delimiter) is specified: "{1}". Delimiter must consists of one character.
advimport.validation.csvDataSourceTextDelimiterError=In the class of import ''{0}'' for the data source the incorrect delimiter (text-delimiter) is specified: "{1}". Delimiter must consists of one character.
advimport.validation.dataSourceColumnNameError=In the class of import ''{0}'' for the data source the title of column not specified
advimport.validation.dataSourceFileError=In the class of import ''{0}'' for the data source the file name (file-name) not specified
advimport.validation.dateTimeConverterFormatError=In the class of import ''{0}'' for the conversion rule of values to "Date/time" type (datetime-converter) the format (format) not specified.
advimport.validation.hierarchicalFilterIdColumnError=In the class of import ''{0}'' the column (id-column) for objects is specified incorrectly
advimport.validation.metaClassNotExistsError=Class/type with the non-existent identifier: ''{1}'' is specified
advimport.validation.noClasses=In the configuration the class of import (class) not specified
advimport.validation.noDataSource=In the class of import ''{0}'' data source (data-source) not specified
advimport.validation.noMetaClassResolver=In the class of import ''{0}'' objects type determination rule (constant-metaclass-resolver or by-column-metaclass-resolver) not specified
advimport.validation.noMode=In the class of import ''{0}'' import mode (mode) not specified
advimport.validation.noObjectSearcher=In the class of import ''{0}'' objects search rule (object-searcher or complex-object-searcher) not specified
advimport.validation.objectConverterAttributeError=In the class of import ''{0}'' for the simple conversion rule of values to object (object-converter) the search attribute (attr) not specified
advimport.validation.objectConverterMetaClassError=In the class of import ''{0}'' for the simple conversion rule of values to object (object-converter) the type of objects (metaclass) not specified
advimport.validation.scriptConverterError=In the class of import ''{0}'' for the random conversion rule of values (script-converter) the script not specified
advimport.validation.scriptFilterError=In the class of import ''{0}'' for the random filter (script-filter) the script not specified
advimport.validation.scriptMetaClassResolver=In the class of import ''{0}'' for the objects type determination rule  (script-metaclass-resolver) the script not specified
advimport.validation.threadNumber=In the configuration incorrect number of threads (threads-number) specified: {0}. Number of threads should be positive integer value
advimport.validation.timerCustomizerColumnError=In the class of import ''{0}'' for the timer customizer (timer-customizer) the column (column): "{1}" is specified incorrectly
advimport.validation.wrongConnectionTimeout=Unable to save the configuration. An invalid value for connection-timeout-min parameter is specified.
advimport.xml.uselessHierarchicalFilter=Node <hierarchical-filter> cannot be used while fast parsing mode is active.
advimportConfigHasErrors=Configuration ''{0}'' cannot be run. Configuration contains errors.
afterHierarchy=The hierarchy relates to objects list by the attribute
agreement=Agreement
agreement.domain.AgreementRecipient=SLA consumer
agreement.domain.AgreementServiceResponsible=Curator of the service, related to the SLA
#agrement.domain.xml
agreement.domain.AgreementSupplier=SLA provider
agreementServiceCaption=SLA/Service
agreementServiceEditPrs=Representation to edit
agreementServiceSetting="SLA/Service" field value
agreements=SLA''s
#metainfo export
allAttributes=Attributes list
allSettings=All settings
allowedForUnlicensedActionsAttentionMsgService.eventActionCardWarn=Action is available for unlicensed users. Any change will result in its inaccessibility
allowedForUnlicensedActionsAttentionMsgService.eventActionCardWarnIsExpired=Action is not available for unlicensed users because its time expired in license file
allowedForUnlicensedActionsAttentionMsgService.eventActionCardWarnIsWrongCheckSum=Action, associated script or module has been changed, that''s why event action is not available for unlicensed users
allowedForUnlicensedActionsAttentionMsgService.scriptCardWarn=Changing the script will block the action(-s) {0} for unlicensed users
allowedForUnlicensedActionsAttentionMsgService.scriptModuleCardWarn=Changing the module will block the action(-s) {0} for unlicensed users
allowedForUnlicensedMarkerValidator.actionsNotAllowed=Action(-s) {0} not available to unlicensed users
anotherSessionStarted=This account is used on another computer. Enter your login and password to log in.
any=Any
api.mq.startFatalError.jms=JMS-integrations not started, because an error occurred on initializing.
api.mq.startFatalError.kafka=Integrations with Kafka not started, because an error occurred on initializing.
application.loading=Loading, please wait.
application.loading.indicator=application loading indicator
#operator/public/index.jsp
applicationLog=System logs
associatedSuperUser=You cannot log in. Superuser account is associated with the user account. Dissociate accounts or contact your administrator.
associationValidationOperation.badContract=Client ''{0}'' is not related to the SLA ''{1}''
associationValidationOperation.cantChangeAttrValue=Unable to edit the value of attribute {0}
#ru.naumen.core.server.bo.bop.AssociationValidationOperation
associationValidationOperation.clientNotSet=Following required attributes not filled in: Client
associationValidationOperation.manyClients=Several association clients specified
attention=Attention!
attrChain=Link to current user
attrGroupAllredyExists=The attribute group with the code ''{0}'' already exist in class/type ''{1}''. The code must be unique within the type/class.
attrUsedInTimerDefinitions=Attribute is used in timers settings: {0}
#ru.naumen.core.server.catalog.valuemap.AttributeDeleteListener
attrUsedInValueMapCatalogItems=Attribute is used in the tables of catalog items ''{0}'': {1}
attrValue=Attribute value
attribute=Attribute
attribute.creation.fail=Attribute cannot be added.
attribute.edit.fail=Attribute cannot be edited.
attribute.noOne=No one
attributeGroup=Attribute group
attributeType.boLinksType=Set of BO links
attributeType.catalogItemSetType=Set of catalog items
attributes=Attributes
attributesGroups=Groups of attributes
authenticationFailure=You cannot log in. Incorrect login and/or password. Please, retry to log in.
authorization.error=Authorization error.
authorization.errorEmployeeForIntegration=Authorization error. Employee for integration.
authorization.errorEmployeeForIntegration.loginForm=You cannot log in. This is service account, contact your administrator.
availableForGroupOfUsers=Available to groups of users
backLink=Back link
bcp.DeleteMailOperation.cantDeleteMailInProgress=The e-mail ''{0}'' can not be deleted while being processed.
beforeHierarchy=Build a hierarchy of objects (downward), starting with the object
blueOrange=Blue-orange
blueViolet=Blue-violet
boLink=Link to BO
boLinks=Set of BO links
body=Body
bool=Boolean
branded=Branded
breakLink=Unlink
cannotLoadExclusionsFileWrongFormat=An error occurred. Settings from the file cannot be uploaded. The file format is invalid.
cannotLoadInputmaskExtensionsFileWrongFormat=An error occurred. Input field with mask extension file cannot be uploaded. Invalid file format selected. {0}
cantBeEmpty=The field must be filled in.
catalog=Catalog
catalogAttributes=Catalog attributes
catalogFolder=Catalogs
catalogItems=Catalog items
#admin light
catalogs=Catalogs
categories=Categories
category=Category
certificateErrorLoading=Error when loading certificate.
changeMainAssociation=Change request association
changeStatusNotAllowedWf=Transition is not permitted in accordance with the workflow settings.
changeTrackingSettings=Change tracking in real time
checkStateConditionsOperation.error1={0} cannot be transited to the state ''{1}'': {2}
checkStateConditionsOperation.error2={0} ''{1}'' cannot be transited to the state ''{2}'': {3}
checkStateConditionsOperation.postConditionError1=an error occurred during script ''{0}'' execution of the condition on exit from the state "{1}". \nError text: {2}
checkStateConditionsOperation.preConditionError1=an error occurred during script ''{0}'' execution of the condition on entry to the state "{1}". \nError text: {2}
childObjectList=List of nested objects
class=Class
classFastSearch=Search
classMetainfoServiceImpl.actionBar.delete=Delete
classMetainfoServiceImpl.actionBar.edit=Edit
classMetainfoServiceImpl.actionBar.move=Move
classMetainfoServiceImpl.actionBar.remove=Archive
classMetainfoServiceImpl.actionBar.unremove=Restore from archive
classMetainfoServiceImpl.changeCaseFormDefaultCaption=Change case form
classMetainfoServiceImpl.changeResponsibleFormDefaultCaption=Change responsible form
classMetainfoServiceImpl.defaultCaseTitle=Type
classMetainfoServiceImpl.editFormDefaultCaption=Edit form
classMetainfoServiceImpl.massEditFormsCaption=Mass edit forms
classMetainfoServiceImpl.newEntryFormDefaultCaption=Add form
classMetainfoServiceImpl.quickFormsCaption=Quick add and edit forms
classMetainfoServiceImpl.systemGroupName=System attributes
classMetainfoServiceImpl.validationError=Metainformation validation error. The type to be deleted is probably used in the settings of other types.
#ru.naumen.core.server.script.spi.ApiUtils
classOfObjectFailure=Object ''{0}'' is not the object of the class ''{1}''.
classSettings=Class settings
classes=Classes
clientLang=Client language
clusterApi.cantReload=Synchronization is already in progress or automatic synchronization has started.
clusterCacheRegion.error=An error occurred while resetting the cache.
clusterCacheRegion.success=The cache was reset successfully.
clusterCacheRegion.type.error=Clearing the cache for the specified type of metainformation is not supported.
clusterService.sendMessageError=An error occurred while performing the operation. Try to repeat the action later or contact the system administrator.
code=Code
comment.title=Comment ''{2}'' from {1}, author ''{0}''.
commentAuthor=Comment author
commentText=Text of comment
commentView=Show comments
commentsSettings=Comments
commonMentionSettings.fastLinkRightsEnabled=Use permissions check to the objects available for mention
commonSearchSettings.analyzerIsUsed.casePattern=types "{0}" (class "{1}")
commonSearchSettings.analyzerIsUsed.classPattern=class "{0}"
commonSearchSettings.analyzers=Types of analyzers available in the system
commonSearchSettings.handlerDoesNotExists=Handler not found for field ''{0}''!
commonSearchSettings.incorrectValue=Incorrect value for field ''{0}''!
commonSearchSettings.indexedFormats=File types which content will be indexed
commonSearchSettings.maxSearchResults=Maximum number of objects in search results
commonSearchSettings.useAdvancedSearch=Use search language
commonSearchSettings.useRightsInLists=Use permissions check to the items of the search results list
communicationError=An error occurred during exchange with server. Please, try to refresh the page and contact your system administrator to fix the problem.
complexEmptyAttrGroups=Disable "Advanced relation editing" or select an attribute group for one or more form lists.
complexTeamEmptyAttrGroup=Select an attribute group for the class {0}. On the advanced edit form you can select an object of the {1} class only within an object of the {0} class.
compressionRatio=Compression ratio of images in attributes "Text in RTF format"
configNotValid=Configuration error {0}. XML-presentation of configuration is not correct.\n {1}
configNotValidXSD=Configuration error {0}. XML-presentation of configuration is not correct (line: {1}, symbol: {2}).
configurations=Configurations
connectTo=Connection to ''{0}''
connection=Connection
connection.validation.appId=Application ID
connection.validation.authentication.error=Authentication error. Check parameters {0}.
connection.validation.authentication.error.flag=SMTP authentication required. Set checkbox "SMTP authentication" and specify correct login and password.
connection.validation.clientId=Directory ID
connection.validation.clientSecret=Client secret
connection.validation.invalid.folders=The following folders are missing in the mailbox: {0}. Root folder is called "INBOX". Folders are separated by "," or ";". Components of folder names are separated by "/" or "\\".
connection.validation.invalid.folders.ews=There is incorrect mail in the mailbox or the following folders to check are missing: {0}. Root folder is called "INBOX". Folders are separated by "," or ";". Components of folder names are separated by "/" or "\\\\".
connection.validation.invalidCorrectMailFolder=Folder for correct mails {0} is missing in the mailbox. Components of folder name are separated by "/" or "\\".
connection.validation.invalidIncorrectMailFolder=Folder for incorrect mails {0} is missing in the mailbox. Components of folder name are separated by "/" or "\\".
connection.validation.login=Login
connection.validation.outgoing.protocol.error=The server (port) is not the outgoing mail server. Check parameters "Server", "Port". Well known ports are: 25, 465, 587.
connection.validation.outgoing.unknown.host=Could not connect to the server. Check parameters "Server", "Port", "Encryption protocol". Well known ports are: 25, 465, 587.
connection.validation.password=Password
connection.validation.protocol.error=The specified protocol is not supported by the server (port) or the server is not the incoming mail server. Check parameters "Server", "Port", "SSL connection". Well known ports are: 143 (IMAP4), 993 (IMAP4, SSL), 110 (POP3), 995 (POP3, SSL).
connection.validation.sharedMailbox=Shared mailbox
connection.validation.silentmode.is.on=Could not connect to the server because of silent mode is enabled
connection.validation.ssl=SSL connection
connection.validation.unknown.host=Could not connect to the server. Check parameters "Server", "Port", "SSL connection". Well known ports are: 143 (IMAP4), 993 (IMAP4, SSL), 110 (POP3), 995 (POP3, SSL).
connection.validation.unknown.url=Could not connect to the server. Check parameter "Server".
connection.validation.wrong.encryption.protocol=The specified encryption protocol is not supported by the server (port). Check parameter "Encryption protocol".
connection.validation.wrong.encryption.skip.verification=The certificate failed verification.
connection.validation.wrong.encryption.ssl=SSL connection is not supported by the server (port). Unset "SSL connection" flag.
contacts=Contact persons
content.PropertyLists.visible=The content "{0}" is hidden because its settings in the "Attribute" parameter use the "{1}" that is not linked to this object by the "Attachment"/"Link to BO" link"
contentTemplates=Content templates
contentType=Content type
contents=Contents
copy=Copy
copyForType=Copy for type ''{0}''_{1}
coreOUInitializer.ouTitle=Department
coreRootInitializer.rootTitle=Company
csrf.chatLicenseRestriction=Access is restricted. The license file does not contain the Chat module.
cti=CTI
currentUser=Current user
customLoginPage.errorDurringApplyCustomLoginForm=An error occurred while applying custom login page. Reason: {0}
customLoginPage.errorDurringApplyDefaultLoginForm=An error occurred while applying default login page. Reason: {0}
customLoginPage.errorDurringApplyTestLoginForm=An error occurred while applying test login page. Reason: {0}
customLoginPage.failedToApplyCustomLoginPage=Failed to apply a custom login page. The system login page was used.
customLoginPage.mandatorryVariablesDuplicates=Template text can contain only one copy of the required system parameters: {0}
customLoginPage.mandatorryVariablesNotFound=Template text does not contain required system parameters: {0}
customLoginPage.wrongVariables=Template text contains wrong parameters: {0}
customObjectActions.noRightsToEditAttribute=You don''t have permissions to edit attribute "{0}" in the class/type {1}
customObjectActions.notEditableAttribute=Action can not be executed because attribute "{0}" ({1}) is not editable
databaseAcquireLockTimeoutError=The operation could not be performed. Failed to get a lock in the database to modify the scheme. Please try again after a while.
databaseConnectionError=Database connection error. Please contact your administrator.
date=Date
dateTime=Date/time
dateTimeInterval.DAY={0} days
dateTimeInterval.DAYForms=day,days,days,days
dateTimeInterval.HOUR={0} hours
dateTimeInterval.HOURForms=hour,hours,hours,hours
dateTimeInterval.MINUTE={0} minutes
dateTimeInterval.MINUTEForms=minute,minutes,minutes,minutes
dateTimeInterval.MONTH={0} months
dateTimeInterval.MONTHForms=month,months,months,months
dateTimeInterval.SECOND={0} seconds
dateTimeInterval.SECONDForms=second,seconds,seconds,seconds
dateTimeInterval.WEEK={0} weeks
dateTimeInterval.WEEKForms=week,weeks,weeks,weeks
dateTimeInterval.short_DAY={0} d
dateTimeInterval.short_HOUR={0} h
dateTimeInterval.short_MINUTE={0} min
dateTimeRestrictions.after=The value cannot be in the past.
dateTimeRestrictions.afterAttribute=The field "{0}" can not be less than field "{1}".
dateTimeRestrictions.before=The value cannot be in the future.
dateTimeRestrictions.beforeAttribute=The field "{0}" can not be more than field "{1}".
dateYearValidationError=The field must contain a date in the range of {0} to {1}
#ru.naumen.advimport.server.dispatch.DelConfigurationActionHandler
delConfigurationActionHandler.error=Configuration "{0}" with code "{1}" cannot be deleted for the following reasons:\n - Configuration is used in settings of the scheduler tasks: {2}.
delete=Delete
deleteCaption=Confirm delete
deleteMessage=Do you really want to delete {0} ''{1}''?
descendantsClassesSettings=Settings of nested classes
descendantsTypesSettings=Settings of nested types
description=Description
design.YYDesign.HelpString=The structure '{'YY'}' will be replaced by the system with the last 2 digits of the year
deutschLang=German
domain.RestAttributesNoun=Other attributes
#ru.naumen.core.server.catalog.valuemap.SetValueMapItemRowSetOperation.validate()
double=Real number
doubleValueMapRulesBySourceAttributeSet=Correspondence table contains several rules for the same set of defining attributes
download=Download
dropDownLists=Drop-down lists
dtInterval=Time interval
edit=Edit
editCommentInStateError=Comment cannot be edited in the object in the state "{0}"
editing=Editing
email=e-mail:{0}
embeddedAppParametersNotShow=The configuration file of parameters built in application "{0}" contains an error. Built in application parameters are not displayed.
embeddedAppParametersParseError=An error occurred during calculating content options (v. system log at level WARN).
embeddedApplication.NotFound=Application not found.
embeddedApplications=Applications
employee=Employee
#employee.domain.xml
employee.domain.ChangePasswd=Change password
employee.domain.currentUser=Current employee
employee.domain.editRestAttributes=Other attributes
employee.domain.userHead=Direct head of employee
employee.domain.userOuEmployee=Employee of the department of employee
employee.domain.userTeamEmployee=Member of the team of employee
employee.domain.userTeamHead=Leader of the team of employee
employee.domain.userUpperOuEmployee=Employee of upper department
employee.domain.userUpperOuHead=Head of upper department
#employee.window.xml
employee.window.ChangePassword=Change password
employee.window.EmployeeCard=Employee card
employee.window.Teams=Teams
employeeNotFoundFailure=Unable to login. Account not found, contact your administrator.
empty=empty
#ru.naumen.core.server.catalog.valuemap.SetValueMapItemRowSetOperation
emptyValueMapTargetAttrValue=In the correspondence table values of the definable attributes: ''{0}'' must be specified.
englishLang=English
enterPassword=Enter the password to log in the admin interface
errorGetStructureElement=No structure elements found matching the class: {0}
errorInvalidXmlChar=Error. Tag <{0}> contains the illegal value ''{1}'', which can''t be saved in XML.
errorMoreOneStructureElement=Found more than one struct element for a class: {0}. Object searching would be fail
errorStructureItemNotFound=No structureItem found matching the code: {0}
errorStructureNotFound=No structure found matching the code: {0}
escalation=Escalation
escalationActions=Actions
escalationSchemes=Escalation schemes
escalationValueMaps=Conversion tables
etc=etc.
#EventAction
eventAction=event action
eventActionCard=Event action card
eventActionSettings=Event actions
eventActionType.add=Add object
eventActionType.addComment=Add comment to object
eventActionType.addFile=Attach file to object
eventActionType.alertActivated=Alert activated
eventActionType.alertChanged=Alert changed
eventActionType.alertDeactivated=Alert deactivated
eventActionType.arriveMessageOnQueue=Message arrival in the queue
eventActionType.bindMassSlave=Link to slave object
eventActionType.bindToMassMaster=Link to mass object
eventActionType.changeMassAttr=Mass attribute change
eventActionType.changeResponsible=Change responsible
eventActionType.changeState=Change state
eventActionType.delete=Delete object
eventActionType.edit=Edit object
eventActionType.editComment=Edit comment
eventActionType.escalation=Escalation
eventActionType.insertMention=Mention within selected objects
eventActionType.loginSuccessful=Login successful
eventActionType.logout=Logout
eventActionType.mail=Mail receipt
eventActionType.ndapMessage=NDAP message
eventActionType.onsetTimeOfAttr=Attribute time occurrence
eventActionType.openEditForm=Open edit form
eventActionType.userEvent=[User event]
eventActionUsagePlace=Event action "{0}"
eventActionUsagePlaceType=type "{0}"
eventActions=Event Action List
eventCleanerJob=Parameters of the event log cleaning task
eventCleanerJobSchedulerEnd=The {0} cleaning task has been successfully completed. From tbl_event {1} records were deleted
eventCleanerJobSchedulerError=An error occurred while executing the {0} cleaning task
eventCleanerJobSchedulerStart=The {0} cleaning task is in progress
eventCleanerJobSettings=Event log management
eventCleanerJobSettingsEdit=Editing the cleaning task parameters
eventCleanerJobSettingsToggleRule=Enabling/disabling the event log storage rule
eventCleanerJobSettingsView=Viewing the cleaning task parameters
eventCleanerJobSettingsViewRule=Viewing the event log storage rules
eventService.actionConditionsError=Condition ''{0}'' for action ''{1}'' is not fulfilled: {2}
eventService.actionConditionsFailed=An error occurred during script execution of the condition ''{0}'' for action ''{1}'': {2}
eventService.actionEventFailed=Error of event action processing ''{0}'': {1}
eventService.add=Object ''{0}'' created
eventService.addMassProblemSlave=Link to slave object added: {0}
eventService.attach_file=File ''{0}'' attached
eventService.attach_file_to_attr=The object "{0}" changed: {1}: File "{2}" added
eventService.changeCase=Object type changed: {0} --> {1}
eventService.changeLicense=User ''{0}'' assigned the license ''{1}''
eventService.changePasswd=User ''{0}'' changed the password.
eventService.changeResponsible=Responsible changed: {0} --> {1}
eventService.changeStatus=Object ''{0}'' changed the state to {1}
eventService.commentAdd=Comment ''{2}'' from {1} added, author ''{0}''.
eventService.commentDel=Comment ''{2}'' from {1} deleted, author ''{0}''.
eventService.commentEdit=Comment from {1} by author ''{0}'' changed: \n\t{2}
eventService.del={0} ''{1}''. Object deleted.
eventService.delError=The object ''{0}'' not deleted for the following reasons: \n{1}
eventService.delete_file=File ''{0}'' deleted
eventService.edit=Object ''{0}'' changed:\n\t{1}
eventService.edit_file=Object file ''{0}'' changed:\n\t{1}
eventService.embeddedApplicationEventInitiated=User event ''{0}'' ({1}) has been started by embedded application ''{2}''. Location: {3}.
eventService.escalationChanged=Set of escalation schemes changed: {0}
eventService.escalationLevel=Level №{0} from the scheme ''{1}'' activated, action ''{2}''
eventService.escalationTime=Escalation levels time changed ''{0}'': \n{1}
eventService.eventCategory.actionConditionsError=Condition for action execution is not fulfilled
eventService.eventCategory.actionConditionsFailed=An error occurred during script execution of the condition for action execution
eventService.eventCategory.actionEventFailed=Event action error
eventService.eventCategory.add=Add object
eventService.eventCategory.addMassProblemSlave=Add link to slave object
eventService.eventCategory.attach_file=Add file
eventService.eventCategory.attach_file_to_attr=Add file
eventService.eventCategory.changeCase=Change object type
eventService.eventCategory.changeLicense=Change user license
eventService.eventCategory.changePasswd=Change user password
eventService.eventCategory.changeResponsible=Change responsible
eventService.eventCategory.changeStatus=Archive object
eventService.eventCategory.commentAdd=Add comment
eventService.eventCategory.commentDel=Delete comment
eventService.eventCategory.commentEdit=Edit comment
eventService.eventCategory.del=Delete object
eventService.eventCategory.delError=Delete object error
eventService.eventCategory.delete_file=Delete file
eventService.eventCategory.edit=Edit object
eventService.eventCategory.edit_file=Edit file
eventService.eventCategory.embeddedApplicationEventInitiated=Event initiated by embedded application
eventService.eventCategory.escalationChanged=Change escalation scheme
eventService.eventCategory.escalationLevel=Escalation
eventService.eventCategory.escalationTime=Escalation levels change
eventService.eventCategory.fastEdit=Fast edit object
eventService.eventCategory.getLocationFailed=Location fetch failed
eventService.eventCategory.loginFailure=Attempt to log in
eventService.eventCategory.loginSuccessful=Log in
eventService.eventCategory.loginSuccessfulFromMobile=Login via mobile app
eventService.eventCategory.logout=Log out
eventService.eventCategory.logoutFromMobile=Logout via mobile app
eventService.eventCategory.messageSendFailed=Send change tracking message error
eventService.eventCategory.moveIn=Move object (from)
eventService.eventCategory.moveOut=Move object (from)
eventService.eventCategory.ndapAlertActivated=Alert activated
eventService.eventCategory.ndapAlertChanged=Alert changed
eventService.eventCategory.ndapAlertDeactivated=Alert deactivated
eventService.eventCategory.notificationAttemptFailed=Error sending first notification
eventService.eventCategory.notificationInvalidEmails=Notification error (invalid e-mail)
eventService.eventCategory.notificationQueuedSuccessful=Queued for sending
eventService.eventCategory.notificationSendFailed=Notification error
eventService.eventCategory.notificationSendFailedEmailNotExists=Notification error (no e-mail)
eventService.eventCategory.notificationSendFailedPartially=Notification error (partial send)
eventService.eventCategory.notificationSendFailedSystemEmail=Notification error (system e-mail)
eventService.eventCategory.notificationSendSuccessful=Send notification
eventService.eventCategory.pushMobileSendFailed=Send notification in mobile application error
eventService.eventCategory.pushMobileSendFailedPartially=Send notification in mobile application error (FCM service error)
eventService.eventCategory.pushMobileSendSuccessful=Send notification in mobile application
eventService.eventCategory.pushPortalSendFailed=Send notice in Portal error
eventService.eventCategory.pushPortalSendFailedPartially=Send notice in Portal error (user is not in the system)
eventService.eventCategory.pushPortalSendSuccessful=Send notice in Portal
eventService.eventCategory.pushSendFailed=Send notice in interface error
eventService.eventCategory.pushSendFailedPartially=Send notice in interface error (user is not in the system)
eventService.eventCategory.pushSendSuccessful=Send notice in interface
eventService.eventCategory.removeError=Object archiving error
eventService.eventCategory.removeMassProblemMaster=Delete link to mass object
eventService.eventCategory.removeMassProblemSlave=Delete link to slave object
eventService.eventCategory.removeMassProblemStatus=Mass attribute unset
eventService.eventCategory.removeTransitionsFromMarker=Transition deleted from marker of ''Change state'' group
eventService.eventCategory.responsibleError=Change responsible error
eventService.eventCategory.setMassProblemMaster=Add link to mass object
eventService.eventCategory.setMassProblemStatus=Mass attribute set
eventService.eventCategory.stateActionFailed=State action error
eventService.eventCategory.switchToMainMode=Enter main mode
eventService.eventCategory.switchToPlanningMode=Enter planning mode
eventService.eventCategory.userEvent=Event record from script
eventService.eventCategory.userEventInitiated=Pushing on user control
eventService.eventCategory.wfChangeStatus=Change state of object with workflow
eventService.fastEdit=Object ''{0}'' changed:\n\t{1}
eventService.getLocationFailed=Mobile client: failed to get device location data. {0}
eventService.groovySyntaxError=detected a syntax error at notification field ''{0}''
eventService.groovyTemplateSyntaxError=detected a syntax error in the template associated with the notice
eventService.levelTime=Level {0}: {1} {2};
eventService.loginFailure=User ''{0}'' failed to log in: {1}
eventService.loginSuccessful=User ''{0}'' logged in
eventService.loginSuccessfulFromMobile=User ''{0}'' logged in via mobile app
eventService.logout=User ''{0}'' logged out
eventService.logoutFromMobile=User ''{0}'' logged out via mobile app
eventService.messageGroovySyntaxError=detected a syntax error in the ''{0}''
eventService.messageGroovyTemplateSyntaxError=detected a syntax error in the template associated with the message
eventService.messageSendFailed=Change tracking message was not sent ({0} - {1}{3}), as the {2}.{4}
eventService.moveIn=Object ''{0}'' moved from ''{1}'' into ''{2}''
eventService.moveOut=Object ''{0}'' moved from ''{1}'' into ''{2}''
eventService.notificationAttemptFailed=A fault occurred during notification sending ({1} - {2}{3}) from {0} because {4}. The next notification sending attempt will be performed after {5} seconds ({6} attempts available){7}
eventService.notificationInvalidEmails=Failed to send notification ({0} - {1}) to the following invalid e-mail addresses: {2}.{3}
eventService.notificationQueuedSuccessful=Notification ({0} - {1} - [{2}]) queued for sending from {3} to {4}.{5}
eventService.notificationSendFailed=Failed to send notification ({1} - {2}) from ''{0}'', because {3}.{4}
eventService.notificationSendFailedEmailNotExists=Failed to send notification ({0} - {1}) to the following employees: {2} because {3}.{4}
eventService.notificationSendFailedPartially=Failed to send notification ({0} - {1}): to next addresses {2} because {3}{4}
eventService.notificationSendFailedSystemEmail=Failed to send notification ({0} - {1}) to the following e-mail addresses: {2}, because these addresses are specified in connection parameters to incoming mail servers.{3}
eventService.notificationSendSuccessful=Notification sent from {0} ({1} - {2}{4})\nInformed: {3}.{5}
eventService.pushGroovySyntaxError=detected a syntax error in the ''{0}''
eventService.pushGroovyTemplateSyntaxError=detected a syntax error in the template associated with the notice
eventService.pushMobileSendFailed=Notification in mobile application was not sent ({0} - {1}{3}) because the {2}.{4}
eventService.pushMobileSendFailedPartially=Notification in mobile application ({0} - {1}) was not sent to the following users: {2} because {3}.{4}
eventService.pushMobileSendSuccessful=Notification in mobile application was sent ({0} - {1}{3}).\nInformed: {2}.{4}
eventService.pushPortalSendFailed=Notice in Portal was not sent ({0} - {1}{3}), as the {2}.{4}
eventService.pushPortalSendFailedPartially=Notice in Portal ({0} - {1}) was not sent to the following users: {2}, as users at the moment are not in the system.{3}
eventService.pushPortalSendSuccessful=Notice in Portal was sent ({0} - {1}{3})\nInformed: {2}.{4}
eventService.pushSendFailed=Notice in interface was not sent ({0} - {1}{3}), as the {2}.{4}
eventService.pushSendFailedPartially=Notice in interface ({0} - {1}) was not sent to the following users: {2}, as users at the moment are not in the system.{3}
eventService.pushSendSuccessful=Notice in interface was sent ({0} - {1}{3})\nInformed: {2}.{4}
eventService.removeError=The object ''{0}'' not removed for the following reasons: \n{1}
eventService.removeMassProblemMaster=Link to mass object removed: {0}
eventService.removeMassProblemSlave=Link to slave object removed: {0}
eventService.removeMassProblemStatus=Mass attribute unset
eventService.removeTransitionsFromMarker=Transition ''{0}'' - ''{1}'' deleted from permission marker ''{2}'' of ''Edit state'' group of class/type {3}
eventService.responsibleError=Responsible change error: {0}
eventService.setMassProblemMaster=Link to mass object added: {0}
eventService.setMassProblemStatus=Mass attribute set
eventService.stateActionFailed=Error of state action processing ''{0}'': {1}
eventService.switchToMainMode=User ''{0}'' entered main mode
eventService.switchToPlanningMode=User ''{0}'' entered planning mode {1}
#ru.naumen.core.server.events.impl.EventServiceBean
eventService.userEvent=Script: {0}
eventService.userEventInitiated=User event ''{0}'' ({1}) has been started by pressing the control ''{2}''. Location: {3}.
eventService.wfChangeStatus=Object state changed: {0} --> {1}
eventStorageRule=Event log storage rules
eventStorageRule.eventGroup.alert=Alert
eventStorageRule.eventGroup.comments=Comments
eventStorageRule.eventGroup.escalation=Escalations
eventStorageRule.eventGroup.eventAction=Event actions
eventStorageRule.eventGroup.files=Files
eventStorageRule.eventGroup.mass=Mass character
eventStorageRule.eventGroup.notification=Notifications
eventStorageRule.eventGroup.objects=Actions with an object
eventStorageRule.eventGroup.plannedVersion=Planned versioning
eventStorageRule.eventGroup.popular=Popular events
eventStorageRule.eventGroup.pushNotification=Push-notifications
eventStorageRule.eventGroup.pushNotificationError=Push-notification errors
eventStorageRule.eventGroup.users=Users
eventStorageRule.eventGroup.workflow=Workflow
eventStorageRule.unableToAddNonUniqueCode=Event log storage rule with code "{0}" cannot be added. The event log storage rule code must be unique.
executeStateActionsOperation.postActionError1=an error occurred during script ''{0}'' execution of the action on exit from the state "{1}". \nError text: {2}
executeStateActionsOperation.preActionError1=an error occurred during script ''{0}'' execution of the action on entry to the state "{1}". \nError text: {2}
export=Export
exportLicense=Downloading a license
external.employeeNotFound=The employee with the specified attributes was not found. Check the settings of the authentication provider
external.esia.chooseSubjectPage.loginAs=Login as
external.esia.subjects.organization=Organisation
external.esia.subjects.physicalPerson=Private person
external.exception.emptyPostLogoutRedirectUri=Warning! Standard user redirection logic is triggered at logout because the postLogoutRedirectUri parameter contains an invalid value (empty string).
external.moreThenOneEmployee=More than one employee with the given attributes was found. Check Authentication Provider Settings or External Authenticator Configuration
external.wrongProtocolCount=The {0} file can be configured for either the OIDC protocol or SAML2. You need to check configuration file for the presence of only one protocol.
failedToDeleteObjects[few]=Failed to delete {0} objects:
failedToDeleteObjects[many]=Failed to delete {0} objects:
failedToDeleteObjects[one]=Failed to delete {0} object:
favicon.convert.error=Failed to convert source file. Choose a different image.
favicon.validation.format=Invalid file format. Select a file in one of the following formats: {0}.
favicon.validation.image=Icon image has incorrect binary format. Please select another image.
favicon.validation.png=The size of PNG-file is invalid. Please select PNG-file with the size {0}x{0} or {1}х{1} pixels.
file=File
file.icon.size.error=Width and height of loaded image should not exceed 16px.
file.preview.unavailable=File can not be viewed, as it was removed
file.upload.error=File cannot be uploaded: {0}
fileAuthor=File author
fileContent=File content
fileDescription=File description
fileDoesNotExistInStorage=File or directory "{0}" not found
fileFromRTFCanNotBeCopy=Images from RTF cannot be copied.
#ru.naumen.admin.preview
filePreviewSettings=File preview
filePreviewSettings.props.docxActivePreviewLimit=Maximum number of files in the ".docx" format that can be simultaneously opened in the preview mode
filePreviewSettings.props.docxMaxFileSize=Maximum size of ".docx" files available for preview (in MB)
filePreviewSettings.props.enabled=On
filePreviewSettings.props.fileTypes=File types, available for preview
filePreviewSettings.props.fileTypesForNewWindow=File types available for view in a separate browser tab
filePreviewSettings.props.imageActivePreviewLimit=Maximum number of images that can be simultaneously opened in preview mode
filePreviewSettings.props.imageMaxFileSize=Maximum size of images available for preview (in MB)
filePreviewSettings.props.maxFileSize=Maximum file size, available for preview (in MB)
filePreviewSettings.props.otherFormatsActivePreviewLimit=Maximum number of files of any other format that can be simultaneously opened in preview mode
filePreviewSettings.props.otherFormatsMaxFileSize=Maximum size for any other formats available for preview (in MB)
filePreviewSettings.props.pdfActivePreviewLimit=Maximum number of files in the ".pdf" format that can be simultaneously opened in the preview mode
filePreviewSettings.props.pdfMaxFileSize=Maximum size of ".pdf" files available for preview (in MB)
filePreviewSettings.props.xlsxActivePreviewLimit=Maximum number of files in the ".xlsx" format that can be simultaneously opened in the preview mode
filePreviewSettings.props.xlsxMaxFileSize=Maximum size of ".xlsx" files available for preview (in MB)
fileStorage.activeStorageAbsence=There is no active file storage
fileStorage.compressing=Compress
fileStorage.deduplicationActivatedError=File storage expanding operation is not allowed. The parameter ''deduplication'' is active in the current file storage configuration.
fileStorage.error=File storage error
fileStorage.fileAttachmentError=An error occurred on adding the file "{0}".
fileStorage.fileBlockDownloading=Downloading "{0}" is prohibited. The file is only available in view mode.
fileStorage.fileNotFound=File or directory not found: {0}.
fileStorage.fileUtils.base64.fileNotFound=The requested file is not found or file storage is not available.
fileStorage.fileUtils.base64.fileSizeExceedLimit=File''s size exceeds the limit set by your system administrator: {0} Kb.
fileStorage.maliciousFilesAttach=An error occurred while attaching the file: ''{0}''. This file was considered to be malicious.
fileStorage.maliciousFilesUpload=File wasn''t uploaded. It is considered malicious.
fileStorage.maliciousFilesUploadWithDetails=File wasn''t uploaded. It is considered malicious:
fileStorage.moving=Move
fileStorage.notExists=File storage ''{0}'' does not exist
fileStorage.storageInUse=of the files not executed: at this time with the file storage "{0}" executed files move or compress operations. Please, try again later.
fileStorageDoesNotExists=File storage "{0}" not found
fileTitle=File name
fileUuidCanNotBeEmpty=UUID cannot be empty.
filterAgreements=Filter SLA during edit
filterCases=Filter types during edit
filterRestrictionStrategy.DEFAULT=Without restrictions
filterRestrictionStrategy.LIST=Restriction on list contents
filterRestrictionStrategy.SCRIPT=Script restriction
filterServices=Filter services during edit
folder=Folder
folders=Folders
formProperties=Form properties
fts.parseError=No matched found. Search request is incorrect
ftsChzechAnalyzer=Chzech
ftsDefaultAnalyzer=Default analyzer
ftsDeutschAnalyzer=Deutsch
ftsEnglishAnalyzer=English
ftsFrenchAnalyzer=French
ftsNoAnalyzer=Exact
ftsNoMorphNoStrictAnalyzer=Inexact, without morphology
ftsPolishAnalyzer=Polish
ftsRussianAnalyzer=Russian
ftsUkrainianAnalyzer=Ukrainian
gatewayRedirectionDisabled=Gateway redirection is disabled
#ru.naumen.core.server.dispatch.GetLogsListActionHandler
get.log.files.error=No access to the application''s logs directory. Check the user''s access rights of which the application is running.
goToWorkInSystem=Proceed to work in the system
hasReferencedEscalationSchemes={1} is referenced by the escalation schemes: {0}
#ru.naumen.core.server.catalog.valuemap.DelMetaClassEventListener
hasReferencedValueMapCatalogItems={2} is referenced by the catalog items ''{0}'': {1}
#ru.naumen.core.server.wf.event.WfProfileDelMetaClassEventListener
hasReferencedWfProfiles={1} is referenced by the profiles of related workflows: {0}
#hasState.template.xml
hasState.editState=Other transitions
helpNotFound=Information for this section is not available. Contact your administrator
hierarchyClass=Object class of the hierarchy
hierarchyGrid=Hierarchy tree
homePage=Home page
imageNotFound=Image not found
importMetainfo.DeletingAdminProfile=['{}'/'{}'] Deleting an administration profile '{}'
importMetainfo.DeletingAdminProfilesDone=Administration profiles have been deleted: '{}'
importMetainfo.DeletingAdvImportConfig=['{}'/'{}'] Deleting a synchronization configuration '{}'
importMetainfo.DeletingAdvImportConfigsDone=Synchronization configurations deleted: '{}'
importMetainfo.DeletingCatalog=['{}'/'{}'] Deleting catalog '{}'
importMetainfo.DeletingCatalogItem=['{}'/'{}'] Deleting the element '{}' of the catalog '{}'
importMetainfo.DeletingCatalogItemsDone=Catalog items '{}' deleted: '{}'
importMetainfo.DeletingCatalogsDone=Catalogs deleted: '{}'
importMetainfo.DeletingContentTemplate=['{}'/'{}'] Deleting content template '{}'
importMetainfo.DeletingContentTemplatesDone=Content templates deleted: '{}'
importMetainfo.DeletingCustomForm=['{}'/'{}'] Deleting user form '{}'
importMetainfo.DeletingCustomFormsDone=Custom forms removed: '{}'
importMetainfo.DeletingCustomJs=['{}'/'{}'] Deleting customization file '{}'
importMetainfo.DeletingCustomJssDone=Customization files removed: '{}'
importMetainfo.DeletingEmbeddedApplication=['{}'/'{}'] Deleting embedded application '{}'
importMetainfo.DeletingEmbeddedApplicationsDone=Embedded applications deleted '{}'
importMetainfo.DeletingEventAction=['{}'/'{}'] Deleting event actions '{}'
importMetainfo.DeletingEventActionsDone=Event actions deleted: '{}'
importMetainfo.DeletingEventMetaClass=Deleting Event for metaclass '{}'
importMetainfo.DeletingFastLinkSetting=['{}'/'{}'] Deleting object mention setting '{}'
importMetainfo.DeletingFastLinkSettingsDone=Object mention settings deleted: '{}'
importMetainfo.DeletingFolderCatalog=Deleting catalog '{}' for metaclass
importMetainfo.DeletingListTemplate=['{}'/'{}'] Deleting list template '{}'
importMetainfo.DeletingListTemplatesDone=List templates deleted: '{}'
importMetainfo.DeletingMailProcessorRule=['{}'/'{}'] Deleting mail processing rule '{}'
importMetainfo.DeletingMailProcessorRulesDone=Mail processing rules deleted: '{}'
importMetainfo.DeletingMetaClass=['{}'/'{}'] Deleting metaclass '{}'
importMetainfo.DeletingQueue=['{}'/'{}'] Deleting queue '{}'
importMetainfo.DeletingQueuesDone=Queues deleted: '{}'
importMetainfo.DeletingReportTemplate=['{}'/'{}'] Deleting report template '{}'
importMetainfo.DeletingReportTemplatesDone=Report templates deleted: '{}'
importMetainfo.DeletingRole=['{}'/'{}'] Deleting role '{}'
importMetainfo.DeletingRolessDone=Roles deleted: '{}'
importMetainfo.DeletingSchedulerTask=['{}'/'{}'] Deleting scheduler task '{}'
importMetainfo.DeletingSchedulerTasksDone=Scheduler tasks deleted: '{}'
importMetainfo.DeletingSchemeEscalation=['{}'/'{}'] Deleting escalation scheme '{}'
importMetainfo.DeletingSchemeEscalationsDone=Escalation schemes deleted: '{}'
importMetainfo.DeletingScript=['{}'/'{}'] Deleting script '{}'
importMetainfo.DeletingScriptModule=['{}'/'{}'] Deleting script module '{}'
importMetainfo.DeletingScriptModulesDone=Script modules deleted: '{}'
importMetainfo.DeletingScriptsDone=Scripts deleted: '{}'
importMetainfo.DeletingSecGroupsDone=User group settings removed: '{}'
importMetainfo.DeletingSecProfilesDone=Profiles deleted: '{}'
importMetainfo.DeletingSecurityDomain=['{}'/'{}'] Deleting access rights settings (security-domain tag) '{}'
importMetainfo.DeletingSecurityGroup=['{}'/'{}'] Deleting user group '{}'
importMetainfo.DeletingSecurityProfile=['{}'/'{}'] Delete profile '{}'
importMetainfo.DeletingSet=['{}'/'{}'] Removing the set '{}'
importMetainfo.DeletingSetsDone=Sets removed: '{}'
importMetainfo.DeletingStructureObjectsView=['{}'/'{}'] Deleting structure '{}'
importMetainfo.DeletingStructureObjectsViewsDone=Structures deleted: '{}’
importMetainfo.DeletingStyleTemplate=['{}'/'{}'] Delete style template '{}'
importMetainfo.DeletingStyleTemplatesDone=Style templates deleted: '{}'
importMetainfo.DeletingTag=['{}'/'{}'] Deleting tag '{}'
importMetainfo.DeletingTagsDone=Tags removed: '{}'
importMetainfo.DeletingTimeDefinition=['{}'/'{}'] Deleting timer definition '{}'
importMetainfo.DeletingTimeDefinitionsDone=Timer definitions removed: '{}'
importMetainfo.DeletingUserAction=['{}'/'{}'] Removing user event '{}'
importMetainfo.DeletingUserEventsDone=Custom events deleted: '{}'
importMetainfo.DeletingValueMapCatalogItem=['{}'/'{}'] Deleting correspondence table '{}'
importMetainfo.DeletingValueMapCatalogItemInEscalation=['{}'/'{}'] Deleting the correspondence table for escalation scheme '{}'
importMetainfo.DeletingValueMapCatalogItemsDone=Correspondence table deleted: '{}'
importMetainfo.DeletingValueMapCatalogItemsInEscalationDone=Correspondence table for escalation scheme deleted: '{}'
importMetainfo.done=Metainformation upload process executed in '{}' sec\nWarnings: '{}'
importMetainfo.entityExistsError=System entity with code ''{0}'' already exists.
importMetainfo.error=Metainformation cannot be uploaded. {0}
importMetainfo.error.crumbValidation=The "breadcrumbs" element cannot be loaded. {0}
importMetainfo.error.eventStorageRuleEventsMustBeFilled=events must be filled
importMetainfo.error.loadingEventStorageRule=An error occurred while loading the rule ''{0}'' ({1}): {2}
importMetainfo.error.missingParent=No parent setting with type ''{0}'' and code ''{1}'' for other settings in metainformation.
importMetainfo.error.settingsSetNotFound=File settings cannot be loaded. The setting "{0}" ({1}) refers to the set "{2}" which doesn''t exist in the system.
importMetainfo.error.statesInheritance=File settings cannot be loaded. There is a status {0} in the system, for which inheritance was interrupted and which is absent in the loaded meta information in class {1}.
importMetainfo.error.systemQueueCannotBeDelete=The file is missing system queue {0}.
importMetainfo.eventActionJMSQueueDoesNotExists=When loading metainformation for the event action "{0}", the value of the "Action processing queue" field was changed to "{1}" ({2}), because the queue "{3}" was not found.
importMetainfo.eventActionJMSQueueNotCorrectActionType=When loading metainformation for the event action "{0}", the value of the "Action processing queue" field was changed to "{1}" ({2}), because other types of event actions are processed in the queue with the code "{3}".
importMetainfo.eventActionTemplateDoesNotExists=Parameter "'{}'" in the event action "'{}'" ('{}') refers to the style template, which does not exist in the system: uuid = "'{}'"
importMetainfo.fileInformation=Metainformation file\nUpload mode: '{}'\nMetainformation file version:'{}'\nExport date: '{}'
importMetainfo.iconDoesNotExists=The parameter "'{}'" of the menu of actions with object, located in '{}', refers to the icon, which does not exist in the system: uuid = "'{}'". The value of the parameter had been set to the default value - the icon "Additional actions""
importMetainfo.ignoringHiddenMetaClass=Ignore import hidden metaClass '''{}'''
importMetainfo.ignoringHiddenMetaClassDomain=['{}'/'{}'] Ignore import hidden metaClass '''{}''' domain
importMetainfo.importFastLinkSettings=['{}'/'{}'] Loading of fast link settings: '{}'
importMetainfo.importFastLinkSettings.skip=['{}'/'{}'] Skip fast link settings: '{}'
importMetainfo.importFastLinkSettingsDone=Fast link settings has been loaded: '{}'
importMetainfo.importFastLinkSettingsStart=Start of loading fast link settings
importMetainfo.importMetaclass=Import '{}'
importMetainfo.importObjectError=Object import error
importMetainfo.initFrom=Initializing from '{}'
importMetainfo.initializingCatalog=Initializing catalog '''{}'''
importMetainfo.jmsQueueMoreMaxThread=Queues loaded successfully. The number of user queues loaded "{0}" is greater than the allowed number - {1}
importMetainfo.jmsQueueMoreMaxUserQueues=User queues loaded successfully. The number of user queues loaded is greater than the allowed number - {0}
importMetainfo.loading=Initializing metaclass '{}'
importMetainfo.loadingAdminLiteSettingsDone=Lite interface settings has been loaded: '{}'
importMetainfo.loadingAdminLiteSettingsStart=Start loading lite interface settings
importMetainfo.loadingAdminProfilesStart=Start loading administration profiles
importMetainfo.loadingAdminProfile=['{}'/'{}'] Loading the administration profile settings: '{}'
importMetainfo.loadingAdminProfilesDone=Administration profile settings uploaded: '{}'
importMetainfo.loadingAdvListSettingsDone=AdvList settings has been loaded
importMetainfo.loadingAdvListSettingsStart=Start loading AdvList settings
importMetainfo.loadingAttrDefaultValue=['{}'/'{}'] Default value for attribute: '''{}'''
importMetainfo.loadingAttrDefaultValuesDone=Settings of default attributes values have been loaded: '{}'
importMetainfo.loadingAttrDefaultValuesStart=Start of loading attributes default values
importMetainfo.loadingCatalog=['{}'/'{}'] Loading catalog '''{}'''
importMetainfo.loadingCatalogsDone=Catalogs settings has been loaded: '{}'
importMetainfo.loadingCatalogsStart=Start loading catalogs
importMetainfo.loadingChangeTrackingSettingsDone=Change tracking settings has been loaded
importMetainfo.loadingChangeTrackingSettingsStart=Start loading change tracking settings
importMetainfo.loadingCommentsSettingsDone=Comments settings has been loaded
importMetainfo.loadingCommentsSettingsStart=Start loading comments settings
importMetainfo.loadingCommonSearchSettingsDone=Common search settings has been loaded: '{}'
importMetainfo.loadingCommonSearchSettingsStart=Start loading common search settings
importMetainfo.loadingContentTemplate=['{}'/'{}'] Loading content template setting: '''{}'''
importMetainfo.loadingContentTemplatesDone=Content templates settings has been loaded: '{}'
importMetainfo.loadingContentTemplatesStart=Start loading content templates
importMetainfo.loadingCustomForms=['{}'/'{}'] Loading custom form: '''{}'''
importMetainfo.loadingCustomFormsDone=Custom forms settings has been loaded: '{}'
importMetainfo.loadingCustomFormsStart=Start loading custom forms
importMetainfo.loadingCustomJsElements=['{}'/'{}'] Loading JS element: '''{}'''
importMetainfo.loadingCustomJsElementsDone=Custom JS elements settings has been loaded: '{}'
importMetainfo.loadingCustomJsElementsStart=Start loading custom JS elements
importMetainfo.loadingDomain=['{}'/'{}'] Loading domain '''{}'''
importMetainfo.loadingDropDownSettingsDone=Drop down settings has been loaded: '{}'
importMetainfo.loadingDropDownSettingsStart=Start loading drop down settings
importMetainfo.loadingEmbeddedApp=['{}'/'{}'] Loading embedded application '''{}':'{}'''
importMetainfo.loadingEmbeddedAppContent=['{}'/'{}'] Loading embedded application content '''{}':'{}'''
importMetainfo.loadingEmbeddedAppContentDone=Embedded applications contents has been loaded: '{}'
importMetainfo.loadingEmbeddedAppContentStart=Start loading embedded applications contents
importMetainfo.loadingEmbeddedAppDone=Embedded applications settings has been loaded: '{}'
importMetainfo.loadingEmbeddedAppStart=Start loading embedded applications
importMetainfo.loadingEscalationDone=Escalation settings has been loaded: '{}'
importMetainfo.loadingEscalationStart=Start loading escalation settings
importMetainfo.loadingEventAction=['{}'/'{}'] Loading event action '''{}':'{}'''
importMetainfo.loadingEventActionsDone=Event actions settings has been loaded: '{}'
importMetainfo.loadingEventActionsStart=Start loading event actions
importMetainfo.loadingFilePreviewSettingsDone=File preview settings has been loaded
importMetainfo.loadingFilePreviewSettingsStart=Start loading file preview settings
importMetainfo.loadingImportConfiguration=Loading import configuration '''{}':'{}'''
importMetainfo.loadingImportConfigurationDone=Import configurations settings has been loaded: '{}'
importMetainfo.loadingImportConfigurationError=The settings from the file cannot be loaded. The sync configuration code {0}({1}) matches the code of the sync configuration to be loaded.
importMetainfo.loadingImportConfigurationStart=Loading import configurations
importMetainfo.loadingInputmaskExtensionsDone=Inputmask extensions settings has been loaded: '{}'
importMetainfo.loadingInputmaskExtensionsStart=Start of loading inputmask extensions
importMetainfo.loadingListTemplate=['{}'/'{}'] Loading list template setting: '''{}'''
importMetainfo.loadingListTemplatesDone=List templates settings has been loaded: '{}'
importMetainfo.loadingListTemplatesStart=Start loading list templates
importMetainfo.loadingMailProcessRulesDone=Mail process rules settings has been loaded: '{}'
importMetainfo.loadingMailProcessRulesStart=Start loading mail process rules
importMetainfo.loadingMailRule=['{}'/'{}'] Loading mail processor rule '''{}':'{}'''
importMetainfo.loadingMetaClass=Loading metaClass '{}'
importMetainfo.loadingMetaClassDone=metaClasses has been loaded: '{}'
importMetainfo.loadingMetaClassStart=Start loading metaClasses
importMetainfo.loadingMobileSettingsDone=Mobile settings has been loaded: '{}'
importMetainfo.loadingMobileSettingsStart=Start loading mobile settings
importMetainfo.loadingNavSettingsDone=Navigation settings has been loaded
importMetainfo.loadingNavSettingsStart=Start loading navigation settings
importMetainfo.loadingObjectGroupsDone=Object groups settings has been loaded: '{}'
importMetainfo.loadingObjectGroupsStart=Start loading object groups
importMetainfo.loadingOtherAdminOptionsDone=Loading Other Admin Settings done
importMetainfo.loadingOtherAdminOptionsStart=Loading Other Admin Settings start
importMetainfo.loadingPermittedTypesDone=Permitted types settings has been loaded: '{}'
importMetainfo.loadingPermittedTypesStart=Start loading permitted types
importMetainfo.loadingPlannedVersionsSettings=Start loading planned versions settings
importMetainfo.loadingPlannedVersionsSettingsDone=Planned versions settings has been loaded
importMetainfo.loadingProfile=['{}'/'{}'] Loading profile '''{}':'{}'''
importMetainfo.loadingReportTemplatesDone=Report templates settings has been loaded: '{}'
importMetainfo.loadingReportTemplatesStart=Start loading report templates
importMetainfo.loadingResponsibleTransfers=['{}'/'{}'] loading responsible transfers: '{}'
importMetainfo.loadingResponsibleTransfersDone=Responsible transfers settings has been loaded: '{}'
importMetainfo.loadingResponsibleTransfersStart=Start loading responsible transfers
importMetainfo.loadingSchedulerTask=['{}'/'{}'] Loading scheduler task '''{}':'{}'''
importMetainfo.loadingSchedulerTaskDone=Scheduler tasks settings has been loaded: '{}'
importMetainfo.loadingSchedulerTaskStart=Loading scheduler tasks
importMetainfo.loadingScript=['{}'/'{}'] Loading script: '''{}'''
importMetainfo.loadingScriptModulesDone=Script modules rules settings has been loaded: '{}'
importMetainfo.loadingScriptModulesStart=Start loading script modules rules
importMetainfo.loadingScriptsDone=Scripts settings has been loaded: '{}'
importMetainfo.loadingScriptsStart=Start loading scripts
importMetainfo.loadingSecDomainsDone=Security domains settings has been loaded: '{}'
importMetainfo.loadingSecDomainsStart=Start loading security domains
importMetainfo.loadingSecGroup=['{}'/'{}'] Loading group '''{}':'{}'''
importMetainfo.loadingSecGroupsDone=Security groups settings has been loaded: '{}'
importMetainfo.loadingSecGroupsStart=Start loading security groups
importMetainfo.loadingSecPolicySettingsDone=Security policy settings has been loaded
importMetainfo.loadingSecPolicySettingsStart=Start loading security policy settings
importMetainfo.loadingSecProfilesDone=Security profiles settings has been loaded: '{}'
importMetainfo.loadingSecProfilesStart=Start loading security profiles
importMetainfo.loadingSet=['{}'/'{}'] Loading set settings: '{}'
importMetainfo.loadingSetsDone=Sets settings has been loaded: '{}'
importMetainfo.loadingSetsStart=Start loading sets
importMetainfo.loadingSettingsDone=Query parameters and other settings has been loaded
importMetainfo.loadingSettingsStart=Start loading query parameters and other settings
importMetainfo.loadingStructuredObjectsView=['{}'/'{}'] Loading structure: '''{}'''
importMetainfo.loadingStructuredObjectsViewsDone=Structures has been loaded: '{}'
importMetainfo.loadingStructuredObjectsViewsStart=Start loading structures
importMetainfo.loadingStyleTemplate=['{}'/'{}'] Loading style template setting: '''{}'''
importMetainfo.loadingStyleTemplatesDone=Style templates settings has been loaded: '{}'
importMetainfo.loadingStyleTemplatesStart=Start loading style templates
importMetainfo.loadingSystemJMSQueue=['{}'/'{}'] Upload system queue '''{}':'{}'''
importMetainfo.loadingSystemJMSQueuesDone=System queues loaded: '{}'
importMetainfo.loadingSystemJMSQueuesStart=Start loading system queues
importMetainfo.loadingTag=['{}'/'{}'] loading tag: '{}'
importMetainfo.loadingTagsDone=Tags settings has been loaded: '{}'
importMetainfo.loadingTagsStart=Start loading tags
importMetainfo.loadingThemesDone=Themes settings has been loaded: '{}'
importMetainfo.loadingThemesStart=Start loading themes
importMetainfo.loadingTimerDefinition=['{}'/'{}'] Loading timer definition '''{}':'{}'''
importMetainfo.loadingTimerDefinitionsDone=Timer definitions settings has been loaded: '{}'
importMetainfo.loadingTimerDefinitionsStart=Start loading timer definitions
importMetainfo.loadingUi=['{}'/'{}'] Loading ui '''{}':'{}'''
importMetainfo.loadingUiDone=UI settings has been loaded: '{}'
importMetainfo.loadingUiStart=Start loading UI
importMetainfo.loadingUserEvent=['{}'/'{}'] Loading user event settings: '''{}'''
importMetainfo.loadingUserEventsDone=User events settings has been loaded: '{}'
importMetainfo.loadingUserEventsStart=Start loading user events
importMetainfo.loadingUserJMSQueue=['{}'/'{}'] Upload user queue '''{}':'{}'''
importMetainfo.loadingUserJMSQueuesDone=User queues loaded: '{}'
importMetainfo.loadingUserJMSQueuesStart=Start loading user queues
importMetainfo.loadingWorkflowProfileFolder=['{}'/'{}'] Loading workflow profile folder '{}'
importMetainfo.loadingWpProfileFoldersDone=Wp profile folders settings has been loaded: '{}'
importMetainfo.loadingWpProfileFoldersStart=Start loading wp profile folders
importMetainfo.parameterDoesNotExists=The parameter "'{}'" of the menu of actions with object, located in '{}', is not defined in the downloaded file. The value of the parameter had been set to the default value - "LEFT"
importMetainfo.parameterHasUnSupportedValue=The parameter "'{}'" of the menu of actions with object, located in '{}', refers to the value, which does not support: value = "'{}'". The value of the parameter had been set to the default value - "LEFT"
importMetainfo.parameterMenuItemActionDoesNotExists=The parameter "'{}'" of the item "'{}'" of menu of actions with object, located in '{}', refers to the action, which does not exist in the system: uuid = "'{}'". The value of the parameter had been set to the default value - "[not specified]".
importMetainfo.reloadingCache=Reloading cache
importMetainfo.reloadingCacheDone=Done('{}'): Reloading cache
importMetainfo.removeDeclaredAttribute=The attribute {0} was removed in the metaclass: {1}
importMetainfo.removeDeclaredAttributeGroup=The attribute group {0} was removed in the metaclass {1}
importMetainfo.restoreStateUserEvent=Restoring the inclusion of a users event: '{}'
importMetainfo.start=Start importing metainformation\nTarget application version: '{}'\nExport type: '{}'\nAuthor: '{}'\nExport date: '{}'
importMetainfo.tableExistsError=System entity with table name ''{0}'' already exists.
importMetainfo.type.full=full
importMetainfo.type.fullReloadMetainfo=with full replacement
importMetainfo.type.partial=partial
importMetainfo.type.partialSets=by package
importMetainfo.type.withoutAdminLite=withoutAdminLite
importMetainfo.type.withoutFullReloadMetainfo=no full replacement
importMetainfo.typeOfExport=Method of loading: '{}'
importMetainfo.warn.AdminLiteNotAvalible=The full metadata has been downloaded due to admin-lite interface is disabled or the admin-lite module is missing in the license file.
importMetainfo.warn.AdminProfileMissingSets=When uploading meta information in the administration profile ''{0}'' (''{1}'') The links to the sets with the codes: {2} have been removed, since they are missing both in the uploaded meta information and on the stand.
importMetainfo.warn.attrGroupUsedInFastLinkSettings=During metainformation loading the attribute {0} was removed, but was used in the mention settings {1}. You need to edit or delete the mention.
importMetainfo.warn.attrUsedInBrowserTabSettings=During metainformation loading parameter "Override browser tab title for the object card" value for the class ''{0}'' was changed to false, since the attribute with the code ''{1}'' was not found.
importMetainfo.warn.attrUsedInEventActions=During metainformation loading the attribute {0} was removed, but was used in the event action settings {1}. You need to edit or delete the event action.
importMetainfo.warn.attrUsedInFastLinkSettings=During metainformation loading the attribute {0} was removed, but was used in the mention settings {1}. You need to edit or delete the mention.
importMetainfo.warn.attrUsedInValueMapCatalog=During metainformation loading the attribute {0} was removed, but was used in the settings of the item of the correspondence table {1}. You need to delete the correspondence table.
importMetainfo.warn.eventStorageRuleClassNotExists=class/type ''{0}'' was not found
importMetainfo.warn.eventStorageRuleStateNotExists=status ''{1}'' was not found in class/type ''{0}''
importMetainfo.warn.immutableTagsCantBeChanged=Tags {0} are licensed and can not be changed
importMetainfo.warn.SetMissingAdminProfiles=When uploading meta information in the package ''{0}'' (''{1}'') In the "Profiles" property, links to administration profiles with codes: {2} have been removed, since they are missing both in the uploaded meta information and on the stand.
importMetainfo.warn.unlinkedStructuredObjectsViewAttributeUsagePoint=The link between the structure ''{0}'' and objects of the class ''{1}'' has been broken for the attribute(s) {2}.\nPlease check the correctness of the "Structure" parameter configuration for the specified attributes.
importModules.error=An error occurred. Modules cannot be uploaded. {0}
inputmaskExtensions=Input field with mask extensions
inputmaskExtensionsLoaded=Input field with mask extension file is loaded
integer=Integer
interface=Interface
interfaceAndNavigation=Interface and navigation
internalServerError=Internal application error. Please, contact the system administrator
invalidAccessKey=Failed to log in using the link, because the passed authorization key does not work (it is expired or access is restricted). Please log in with your account to continue.
isAsynchronousCountObjectsInTab=Count objects asynchronously in tabs
isCompleteSetOfLicensesNotRequired=Login with an incomplete set of licenses
isUiCssTransitionsEnabled=Animation of interface elements
jmsQueue.notFound=Queue not found.
jmsQueue.sendMessageToUserJMSQueue.error=An error occurred while sending a message to the queue.
jmsQueue.setRoutingType.error=An error occurred while changing the mode, or the user queue was not found.
jmsQueue.setRoutingType.success=The mode has been successfully changed.
jmsQueue.system.action.title.ChangeTrackingEventAction=Change Tracking
jmsQueue.system.action.title.IntegrationEventAction=Send to external queue
jmsQueue.system.action.title.NotificationEventAction=Notification
jmsQueue.system.action.title.PushEventAction=Notice in interface
jmsQueue.system.action.title.PushMobileEventAction=Notice in mobile client
jmsQueue.system.action.title.PushPortalEventAction=Notice in portal
jmsQueue.system.action.title.ScriptEventAction=Script
jmsQueue.system.description.Queue.EventAction=The event actions of the "Script" type are processed in the queue.
jmsQueue.system.description.Queue.EventAction.Escalations=The event actions, for which the "Escalation" event is selected, are processed in the queue.
jmsQueue.system.description.Queue.EventAction.Notifications=The event actions of the "Notification" type are processed in the queue.
jmsQueue.system.description.Queue.EventAction.Pushes=The event actions of the "Notice in interface", "Notice in portal", "Notice in mobile client" types (if these modules are enabled) are processed in the queue.
jmsQueue.system.description.Queue.EventAction.WebSocketMessages=The event actions sending WebSocket messages are processed in queue.
jmsQueue.system.description.Queue.External.EventAction=The event actions of the "Script" type, for which the "External interaction" indication is set, are processed in the queue.
jmsQueue.system.description.Queue.PlannedEvent=The event actions, for which the "Attribute time occurrence" event is selected, are processed in the queue.
jmsQueue.system.description.Queue.UserEventAction=The event actions, for which the "User event" event is selected, are processed in the queue.
jmsQueue.system.description.Queue.bridgeNdapAlertIn=The event actions, for which the NDAP event ("Alarm activation", "Alarm deactivation", "NDAP message") is selected, are processed in the queue.
jmsQueue.system.title.Queue.EventAction=System script queue
jmsQueue.system.title.Queue.EventAction.Escalations=System escalation queue
jmsQueue.system.title.Queue.EventAction.Notifications=System notification queue
jmsQueue.system.title.Queue.EventAction.Pushes=System push notification queue
jmsQueue.system.title.Queue.EventAction.WebSocketMessages=System WebSocket message queue
jmsQueue.system.title.Queue.External.EventAction=System queue of event actions with the "External interaction" indication
jmsQueue.system.title.Queue.PlannedEvent=System queue of event actions "Attribute time occurrence"
jmsQueue.system.title.Queue.UserEventAction=System queue of user event actions
jmsQueue.system.title.Queue.bridgeNdapAlertIn=System queue of event actions NDAP
jmsQueue.unableToAddNonUniqueCode=The custom queue with code ''{0}'' could not be added. The code of user queue must be unique. The queue with this code already exists or is pending deletion.
jmsQueues=Queues
keystore.notfound=Certificate not found.
language=Language
languageHistory=Language change history object
learningProcess=Learning process
leftMenu=Left menu
leftMenu.BrowsingHistoryLeftMenuItemValue.defaultTitle=History
leftMenu.CompanyLeftMenuItemValue.defaultTitle=Company
leftMenu.FavoritesLeftMenuItemValue.defaultTitle=Favorites
leftMenu.LeftMenuRootValue.defaultTitle=Menu
leftMenuSettings=Left menu settings
leftMenu.validation.emptyTabs=The card tab specified in the element settings was not found. To enable the item, change its settings.
leftMenu.validation.incorrectReferenceFqn=Incorrect link to the card in the element settings is specified. To enable the element, change the settings.
library.validation.emptyHash=File checksum is not specified
library.validation.invalidExpirationDate=The file cannot be uploaded. The file has expired
library.validation.invalidExpirationDateFormat=The file cannot be uploaded. The license expiration date has an incorrect format. The date should be in the format DD.MM.YYYY.
library.validation.invalidHash=File checksum is incorrect
library.validation.invalidJarFile=The file {0} is not a jar archive
library.validation.invalidSign=The file {0} cannot be downloaded because its signature is not authentic
library.validation.licenseExpired=The file has expired
library.validation.maliciousFile=An error occurred while downloading the file: "{0}". The file is malicious.
library.validation.mustHaveAtLeastOneClass=File {0} must contain at least one .class file
library.validation.notAZip=File {0} is not a jar archive
licenseServiceImpl.licenseVerificationFailedLogin=The license file is invalid.
licenseSeviceImpl.absentAuthor=Author who created the file is not specified.
licenseSeviceImpl.absentCreationDate=Create date of file is not specified.
licenseSeviceImpl.cannotLoadLicenseFile=Licence file cannot be uploaded.\n{0}
licenseSeviceImpl.containsIncorrectModules=The licence file cannot be uploaded. The file contains incorrect module codes: {0}.
licenseSeviceImpl.existLicensesIsAbsent=There are no such license groups: {0}, which are assigned to current users.
licenseSeviceImpl.hasNoCorrectCode=The value of parameter "code" has invalid format in following license groups: {0}.
licenseSeviceImpl.hasNotExistAllowedClassesCodes=Warning! The license groups {0} of the file contains values of parameter {1} that does not match existing classes: {2}.
licenseSeviceImpl.hasNotLicensedCode=Code of license group cannot be ''{0}''
licenseSeviceImpl.hasNotUniqueLicenseCodes=The licence file contains two or more licence groups with equal codes.
licenseSeviceImpl.incorrectMaxSessionsValueBoth=The value of parameter "maxSessions" has invalid format in following license groups: {0} . The value of parameter "maxSessionsForUnlicensedUsers" has invalid format.
licenseSeviceImpl.incorrectMaxSessionsValueGroup=The value of parameter "maxSessions" has invalid format in following license groups: {0} .
licenseSeviceImpl.incorrectMaxSessionsValueUnlic=The value of parameter "maxSessionsForUnlicensedUsers" has invalid format.
licenseSeviceImpl.incorrectPermissionsSetForUnlicensedUsersValue=The file contains incorrect code of the access permissions set: {0} .
licenseSeviceImpl.incorrectUnlicMaxInactiveIntervalValue=The value of parameter "unlicensedMaxInactiveInterval" has invalid format.
licenseSeviceImpl.licenseSignVerificationFailed=The license file cannot be uploaded. File format is invalid: signature verification error.
licenseSeviceImpl.licenseUploadComplete=License file is uploaded.<br>
licenseSeviceImpl.licenseVerificationFailed=The license file cannot be uploaded. File format is invalid: {0}
licenseSeviceImpl.namedLicensesCountLessThanExist=The number of personal licenses of the type {0} is less than used at present.
lilac=Lilac
lineBreak=[line break]
linkObject=Link object
linkObjectUUID=Link object UUID
linkToBo=Link to BO
listAttrChain.dynamicApplyAttrChain=The list display parameters are incorrect (link attribute not found). Please contact your administrator to resolve the problem.
listAttrChain.errorStructure=Invalid attribute link configuration format. Please contact your administrator to resolve the problem.
listAttrChain.incorrectAttr=The list parameters are incorrect (specified object class and object class in the link attribute do not match). Please contact your administrator to resolve the problem.
listAttrChain.incorrectType=The list parameters are incorrect (type of the specified link attribute is not valid). Please contact your administrator to resolve the problem.
listPageTitle=Content page title
listTemplate.applyFailMessage=Error occurred while copying groups of settings to the following lists:<br>{0}.<br>Links between template and these groups of settings were deleted.
listTemplate.applyMessage=Applying settings from template "{0}" completed.<br>Were copied: {1}.
listTemplate.defaultPrs=default presentation
listTemplate.dynamicApplyError=The list display parameters are incorrect (the list view does not match). Please contact your administrator to resolve the problem.
listTemplate.dynamicApplyErrorClass=The list display parameters are incorrect (the class or types of list objects do not match the class or types of the template). Please contact your administrator to resolve the problem.
listTemplate.dynamicApplyErrorLinkObject=The list display parameters are incorrect (object to link with list objects not found). Please contact your administrator to resolve the problem.
listTemplate.dynamicApplyErrorTemplate=The list display parameters are incorrect (the attribute group in the list does not match the template attribute group). Please contact your administrator to resolve the problem.
listTemplate.filtration=restrictions filtration
listTemplate.hasReferenced={1} is referenced by the list templates: {0}.
listTemplate.hasReferencedOnAttributeGroup=Attribute group {1} is referenced by the list templates: {0}.
listTemplate.massToolPanel=mass tool panel
listTemplate.objectFilter=restriction list contents
listTemplate.objectsActions=objects actions
listTemplate.othersTemplates=Others templates
listTemplate.parametersList=List parameters
listTemplate.removed=The list display parameters are incorrect (list template not found). Please contact your administrator to resolve the problem.
listTemplate.settingsDefaultPrs=settings default presentation
listTemplate.settingsFiltration=settings restrictions filtration
listTemplate.settingsMassToolPanel=settings mass tool panel
listTemplate.settingsObjectFilter=settings restriction list contents
listTemplate.settingsObjectsActions=settings objects actions
listTemplate.settingsParametersList=list parameters settings
listTemplate.settingsToolPanel=settings tool panel
listTemplate.suitableTemplates=Suitable templates
listTemplate.toolPanel=tool panel
listTemplate.unableToAddNonUniqueCode=The list template with the code "{0}" cannot be added. The code must be unique.
listTemplates=Template
listTemplates.newTemplate=[add new template]
loadStatusExceptionMessage=In the system objects have the status of which there aren''t in the metainformation.
localization=(locale: "{0}")
location=Location
login=Login
logo.fileIsNotImage=Content of the file "{0}" is not an image.
logos=logos system
mail=Mail
mail.server.message=mail server responded:
mailConnectionParameters=Mail connection parameters
mailLog=Incoming mail log
main=Main
maintenanceMode=Blocking the entrance during maintenance
massSCOperations=Mass operations
maxOpenedBrowserTabsPerUser=The maximum number of simultaneously open tabs for one user
#ru.naumen.core.server.common.FormattersImpl
memory.gb.title=Gb
memory.kb.title=Kb
memory.mb.title=Mb
#ru.naumen.core.server.dispatch.CreateMultipleObjectsHandler
metaClassAttrNotFound=No metaclass specified in object
metaClassSearchSetting=Settings of classes for fast search
metainfo.AddAttributeActionHandler.aggregateAttrError=Classes "Department" and/or "Team" must be selected to aggregate.
metainfo.AddMetaClassActionHandler.errorBadCase=Identifier mismatch of added Type and Class
metainfo.AddMetaClassActionHandler.errorCaseDisallowed=Type/class cannot be added. Value ''{0}'' cannot be used as a code
metainfo.AddMetaClassActionHandler.errorCaseToAbstract=Type ''{0}'' cannot be added. Class of type is abstract.
metainfo.AddMetaClassActionHandler.errorCaseToSysObj=Can not use service metaclasses.
metainfo.AddMetaClassActionHandler.errorCatalogItemCaseDisallowed=Catalog cannot be added. Value ''{0}'' cannot be used as a code
metainfo.AddMetaClassActionHandler.errorForbidUserClasses=In the class ''{0}'' forbidden to create metaclasses.
metainfo.AddMetaClassActionHandler.errorRelCase=Class can not be embedded in a case.
metainfo.AddMetaClassActionHandler.parent=Parent
metainfo.AddStateActionHandler.stateCodeExist=State with the code ''{0}'' cannot be added. State code must be unique within the type
metainfo.ChildMetaClass=Child type ''{0}''
metainfo.ClassIsNotSpecified=Class of object is not specified.
metainfo.CopyMetaClassActionHandler.cantCopyClassManyToManyAttributeTableExist=Class cannot be copied. The constraint about uniqueness the first eight symbols of class code, type code and attribute code for attributes of the type ''{0}'' is violated, because the class copied contains attribute with code ''{1}''. Change the class code.
metainfo.CopyMetaClassActionHandler.cantCopyHardcoded=Only custom metaclasses can be copied
metainfo.CopyMetaClassActionHandler.cantCopyTypeManyToManyAttributeTableExist=Type cannot be copied. The constraint about uniqueness the first eight symbols of class code, type code and attribute code for attributes of the type ''{0}'' is violated, because the type copied contains attribute with code ''{1}''. Change the type code.
metainfo.CopyMetaClassActionHandler.copyWarning=For the "{0}" class {1} are not specified. In the "{0}" class the following settings of the "{2}" class do not exist: {3}"
metainfo.CopyMetaClassActionHandler.determiner="Determination rule" for attributes: {0}
metainfo.CopyMetaClassActionHandler.determiners=determination rules {0}
metainfo.CopyMetaClassActionHandler.errorBadCase=Identifier mismatch of added Type and Class
metainfo.CopyMetaClassActionHandler.mustBeClass=Created metaclass must be a Class
metainfo.CopyMetaClassActionHandler.role=permissions for profiles: {0}
metainfo.CopyMetaClassActionHandler.roles=roles {0}
metainfo.CopyMetaClassActionHandler.timerDefinition="Timer" for attributes: {0}
metainfo.CopyMetaClassActionHandler.timerDefinitions=timers {0}
metainfo.CreateCatalogActionHandler.badCode=Incorrect code value of the catalog ''{0}''
metainfo.CreateCatalogActionHandler.catalogItemPrefix=Catalog ''{0}'' item
metainfo.CreateCatalogActionHandler.emptyTitle=Catalog title not specified
metainfo.DelAttributeActionHandler.aggregateAttrError=Attribute ''{0}'' cannot be deleted. Attribute ''{0}'' is aggregated for the attribute ''{1}''.
metainfo.DelAttributeActionHandler.attrDeclaredAtParent=Attribute is defined in the parent class or type.
metainfo.DelAttributeActionHandler.attrInEventActionsUse=Attribute is used in event action "{0}": {1}
metainfo.DelAttributeActionHandler.attrIsSystem=This is the system attribute.
metainfo.DelAttributeActionHandler.attrRelatedToAttributeOfRelatedObject=Attribute is related to other attributes of type ''Attribute of related object'': {0}
metainfo.DelAttributeActionHandler.attrRelatedToBackLink=Attribute is related to other attributes of type ''Back link'': {0}
metainfo.DelAttributeActionHandler.attrUsedAtCommentListContent=Attribute is used in the ''Comment list'' contents: {0}
metainfo.DelAttributeActionHandler.attrUsedAtFileListContent=Attribute is used in the ''File list'' contents: {0}
metainfo.DelAttributeActionHandler.attrUsedAtGroup=Attribute is in following attribute groups: {0}
metainfo.DelAttributeActionHandler.attrUsedAtRelObjectContent=Attribute is used in the ''List of related objects'' contents: {0}
metainfo.DelAttributeActionHandler.attrUsedAtRelationSchemaContent=Attribute is used in: {0}
metainfo.DelAttributeActionHandler.attrUsedInBreadCrumb=The attribute is used in settings of the "breadcrumbs".
metainfo.DelAttributeActionHandler.attrUsedInDateTimeRestriction=Attribute is related to attribute: ''{0}''.
metainfo.DelAttributeActionHandler.attrUsedInHomePage=Attribute ''{0}'' cannot be deleted. The attribute is used in the settings of the home page: {1}
metainfo.DelAttributeActionHandler.attrUsedInMSC=The attribute is used in parameter ''Search relevance criterion'' of class ''{0}''
metainfo.DelAttributeActionHandler.attrUsedInMenu=The attribute ''{0}'' cannot be deleted because it is used in setting by menu items: {1}
metainfo.DelAttributeActionHandler.attrUsedInWindowCaption=The attribute is used in settings of the object caption: {0}
metainfo.DelAttributeActionHandler.cantDelAttr=Attribute {0} cannot be deleted.
metainfo.DelAttributeActionHandler.usedInExportNdap=The attribute is selected as available from the monitoring system in the attribute "{0}" (type ''{1}'' class ''{2}'').
metainfo.DelAttributeActionHandler.usedInExportNdapClassOnly=The attribute is selected as available from the monitoring system in the attribute "{0}" (class ''{1}'').
metainfo.DelAttributeActionHandler.usedInTool=Attribute is used in settings of the action tool {0}.
metainfo.DelAttributeActionHandler.usedInTools=Attribute is used in settings of the action tools {0}.
metainfo.DelAttributeActionHandler.wfProfileError=Attribute is used in profile of related workflows: {1}
metainfo.DelAttributeActionHandler.wfProfilesError=Attribute is used in profiles of related workflows: {1}
metainfo.DelSecurityGroupActionHandler.cantDeleteGroup=User group "{0}" cannot be deleted for the following reasons:
metainfo.DelSecurityGroupActionHandler.cantDeleteSystemGroup=Can not delete system group "{0}"
metainfo.DelStateActionHandler.StateUsedInEventStorageRule=Status is used in an event log storage rule: {0}.
metainfo.DelStateActionHandler.StateUsedInTimer=State is used in timers: {0}.
metainfo.DelStateActionHandler.cantDeleteHardcoded=System state <strong>{0}</strong> can''t be deleted
metainfo.DelStateActionHandler.cantDeleteInherited=Parent type state <strong>{0}</strong> can''t be deleted
metainfo.DelStateActionHandler.cantDeleteState=State ''{0}'' cannot be deleted.
metainfo.DeleteContentActionHandler.cantDelete=Tab ''{0}'' cannot be deleted.
metainfo.DeleteScriptModuleActionHandler.cantDeleteModule=The script module {0} cannot be removed for the following reasons:
metainfo.DeleteScriptModuleActionHandler.cantDeleteModules=The script modules {0} cannot be removed for the following reasons:
metainfo.EditMetaClassActionHandler.addWorkflowError=The workflow was not added to the class. Cause: {0}.
metainfo.EditMetaClassActionHandler.changeStateButtonMissingForCase="{0}" button is not placed on action bar of object card of type {1} (class {2}), as the inheritance of settings is broken.
metainfo.EditMetaClassActionHandler.changeStateButtonMissingForClass="{0}" button is not placed on the action bar of object card of class {1}, as the inheritance of settings is broken.
metainfo.EditMetaClassActionHandler.editNestedToOtherError=The nesting of class can be edited only if objects of class nested into to object of the same class or nesting not specified.
metainfo.EditSecurityGroupMembersActionHandler.cantEditDisabledGroup=The system group "{0}" can not be changed, because it is disabled.
metainfo.FixEmptyFieldsInSearchSettingsResult=Empty fields in search settings have been replaced successfully.
metainfo.GetMetaClassActionHandler.metaClassNotFound=Type/class not found: fqn={0}
metainfo.MetaClassDeleteListener.CatalogReference=Metaclass ''{0}'' cannot be deleted because it is referenced by the catalog ''{1}''
metainfo.MetaClassIsNotSpecified=Object type is not specified.
metainfo.MetainfoServiceBean.EscalationNotFound=Escalation scheme with code {0} not found or synchronization is not over yet!
metainfo.MetainfoServiceBean.catalog=catalog
metainfo.MetainfoServiceBean.catalogExists=Catalog with the code ''{0}'' already exists.
metainfo.MetainfoServiceBean.catalogNotExists=Catalog with the code ''{0}'' does not exist!
metainfo.MetainfoServiceBean.clazz=class
metainfo.MetainfoServiceBean.entityExistsError=cannot be added. Value ''{0}'' cannot be used as code of user class.
metainfo.MetainfoServiceBean.exists=cannot be added. {0} with the code ''{1}'' already exists.
metainfo.MetainfoServiceBean.metaClassNotFound=MetaClass not found: fqn={0}
metainfo.MetainfoServiceBean.type=type
metainfo.MetainfoServiceBean.typeExists=cannot be added. Type with the code ''{1}'' already exists in the class''{0}''.
metainfo.RestoreClassMetainfoActionHandler.errorRemovedParent=Type ''{0}'' cannot be restored from the archive, the parent {1} is in the archive
metainfo.SameThemeCodeError=Interface theme with the code ''{0}'' cannot be added. The code must be unique.
metainfo.SecGroupIsRequiredValue=User group is related to the required attribute of the following objects: {0}
metainfo.SecGroupUsedAtNextProfiles=User group is used in the following profiles: {0}
metainfo.SetLocaleError=An error occurred while setting the locale. Please verify the locale code. Available options: {0}.
metainfo.SetThemeError=An error occurred while setting the theme. Please verify the theme code. Available options: {0}.
metainfo.SetThemeError.WrongModule=An error occurred while setting the theme. Please verify the module code. Available options: {0}.
metainfo.SetTimeZoneError=An error occurred while setting the time zone. Please verify the time zone identifier.
metainfo.SetTimeZoneForRelatedEmployees=Time zones for employees of the department have been installed successfully.
metainfo.SwitchThemeError=The interface theme ''{0}'' cannot be forbidden for user selection, because it is used as the default theme for operator interface.
metainfo.actionBarUsage=action bar
metainfo.agreementUndefined=Agreement is undefined.
metainfo.bulkOperationsBarUsage=bulk operations bar
metainfo.cannotLoadMetainfoFileWrongFormat=An error occurred. Metainformation cannot be uploaded. The file format is invalid. {0}
metainfo.catalog=Catalog ''{0}''
metainfo.catalog.deleteMesage=Catalog ''{0}'' cannot be deleted.
metainfo.clazz=Class ''{0}''
metainfo.clazzLower=class ''{0}''
metainfo.content=content
metainfo.defaultTheme=Default ({0})
metainfo.domain.roleCodeMustBeUnic=Role with the code ''{0}'' cannot be added. Role code must be unique.
metainfo.elementType.metaClass=Class/type
metainfo.emptyTitle=Title is empty.
metainfo.errorDefObjsRemove=Any of objects as default parameters are removed.
metainfo.errorDefaultScType=Metaclass ''{0}'' is not exists or is not type of ServiceCall, or is removed.
metainfo.errorDefineAgrServ=Agreement and service is not defined.
metainfo.errorRelationDefaultAgrServ=Agreement ''{0}'' and service ''{1}'' is not related.
metainfo.errorServiceHaveNotScTypes=Service ''{0}'' is not related with at least one type of ServiceCall.
metainfo.errorServiceScTypeRelation=Service ''{0}'' is not related with type of ServiceCall ''{1}''.
metainfo.inMobileApp=in the mobile app
metainfo.incorrectExportMode=Metainformation cannot be uploaded. The specified export mode is incorrect.
metainfo.inlineCommentUsage=comment input field
metainfo.menuOfActionsWithObjectUsage=menu of actions with object
metainfo.menuOfMobileCardUsage=action menu of the object card with code ''{0}'' in the mobile application
metainfo.nextObjectsHaveTheState=The following objects: {1} are in the state ''{0}''
metainfo.objectCard=object card
metainfo.objectCardWithCode=object card with a code
metainfo.security.employeeForIntegration.error=The employee {0} cannot be changed for the following reasons:\n\t 1. The attribute "Employee for integration" cannot be changed because the employee is related to the admin.
metainfo.security.login.employee.error=Unable to log in as employee. Employee''s account is for service.
metainfo.security.profileCodeMustBeUnic=The profile with the code ''{0}'' can not be added. The code must be unique.
metainfo.security.profileNotFound=Profile with the code "{0}" not found in class "{1}"
metainfo.security.roleWithCodeDoesNotExist=Role with the code ''{0}'' does not exist
metainfo.security.superuser.add.error=The admin cannot be added for the following reasons:\n\t 1. The account of the employee is for service.
metainfo.security.superuser.alreadyDeleted=Superuser password cannot be changed because this superuser deleted.
metainfo.security.superuser.edit.error=The admin {0} cannot be changed for the following reasons:\n\t 1. The account of the employee is for service.
metainfo.security.superuserExists=Superuser cannot be added. The superuser with this login already exists.
metainfo.security.superuserLoginNonAscii=Superuser cannot be added. Superuser login must contain only latin alphabet characters.
metainfo.security.superuserEditLoginNonAscii=Superuser cannot be edited. Superuser login must contain only latin alphabet characters.
metainfo.security.superuserEditLoginExists=Superuser cannot be edited. The superuser with this login already exists.
metainfo.security.superusers.limit.exceeded=Superuser cannot be added. The maximum number of superuser licenses exceeded.
metainfo.security.userExists=Superuser cannot be added. The employee with this login already exists: ''{0}''.
metainfo.tab=tab
metainfo.template=list template
metainfo.themeStyleAbsentValueError=Parameter ''{0}'' does not specified or not exists in uploaded file. The default value ''{1}'' is set as the parameter value.
metainfo.themeStyleBadValueError=Parameter ''{0}'' specified an invalid value. The default value ''{1}'' is set as the parameter value.
metainfo.tooLongTitle=Length of title ''{0}'' is more then {1} symbols.
metainfo.toolUsage=, tool ''{0}''
metainfo.toolsUsage=tool ''{0}''
metainfo.type=Type ''{0}''
metainfo.typeLower=type ''{0}''
metainfoValidation.actionToolActionError=Tag "action" of control with the code ''{2}'' for the type/class ''{0}'' does not exist.
metainfoValidation.actionToolCaptionEmptyError=Tag "caption" of control with the code ''{2}'' for the type/class ''{0}'' does not exist.
metainfoValidation.actionToolCaptionError=Tag "caption" of control with the code ''{2}'' for the type/class ''{0}'' contains invalid value ''{3}''. Value of the tag must be string with the length from 1 to 64 characters.
metainfoValidation.actionToolPresentationTypeError=Tag "presentationType" of control with the code ''{2}'' for the type/class ''{0}'' contains invalid value ''{3}''. The tag can possess values: "default", "push", "link".
metainfoValidation.aggregateAggregateError=Tag "type/property" with the XML-attribute ''attributes'' in attribute with the code ''{1}'' for the type/class ''{0}'' contains invalid value ''{2}''. Attribute with the code ''{3}'' in the type/class ''{0}'' does not exist.
metainfoValidation.aggregateAggregateParentError=Tag "type/property" with the XML-attribute ''attributes'' in attribute with the code ''{1}'' for the type/class ''{0}'' contains invalid value ''{2}''. Attribute with the code ''{3}'' does not exist in the class referenced by the attribute with the code ''{1}''.
metainfoValidation.aggregateAggregateTypeError=Tag "type/property" with the XML-attribute ''attributes'' in attribute with the code ''{1}'' for the type/class ''{0}'' contains invalid value ''{2}''. Attribute with the code ''{3}'' in type/class ''{0}'' cannot be aggregated.
metainfoValidation.aggregateError=Tag "type/property" with the XML-attribute ''attributes'' in attribute with the code ''{1}'' for the type/class ''{0}'' does not exist.
metainfoValidation.applicationCannotBeDeleted=Application {0} cannot be deleted for the following reasons:
metainfoValidation.applicationCannotBeDeleted.few=Applications {0} cannot be deleted for the following reasons:
metainfoValidation.applicationCannotBeDisabled=Application {0} cannot be disabled for the following reasons:
metainfoValidation.applicationCannotBeDisabled.few=Applications {0} cannot be disabled for the following reasons:
metainfoValidation.applicationUsedInMKReason=Contents: {0} in object card ''{1}''.
metainfoValidation.applicationUsedInMKReasonHeader=It is used in mobile application settings.
metainfoValidation.applicationUsedInNavigationMenuMKReason=Navigation menu items: {0}.
metainfoValidation.applicationUsedInOtherSettingsSecurityBlock=tab "Other", block "Security".\n
metainfoValidation.applicationUsedInUIReason=It is used in interface settings of the interface of classes and types: {0}.
metainfoValidation.attrGroupNotCorrectInStructuredObjectsViewItem=Invalid attribute group "{0}" use in structure "{1}", in item "{2}"
metainfoValidation.attrGroupUsedAtObjectList=Tag "attributeGroup" in content with the code ''{2}'' in object card of the type/class ''{0}'' contains invalid value ''{3}''. Value of the tag must be code of the existing attribute group.
metainfoValidation.attrUseAtObjectList=The value ''{3}'' is used in card for the type/class ''{0}'' in the content ''{2}'', but does not exist
metainfoValidation.attributeAccessError=There is no attribute {1} in class/type {0}
metainfoValidation.attributeAccessorError=Tag "accessor" in attribute with the code ''{1}'' for the type/class ''{0}'' contains invalid value ''{2}''. The tag can possess values: {3}.
metainfoValidation.attributeAliasError=Tag "searchAlias" in attribute with the code ''{1}'' for the type/class ''{0}'' contains invalid value ''{2}''. Value of the tag must be string with the length from 0 to 64 characters.
metainfoValidation.attributeAnalizerError=Tag "searchAnalizer" in attribute with the code ''{1}'' for the type/class ''{0}'' contains invalid value ''{2}''. The tag can possess values: {3}.
metainfoValidation.attributeBoostError=Tag "searchBoost" in attribute with the code ''{1}'' for the type/class ''{0}'' contains invalid value ''{2}''. The tag can possess values: "0.2", "0.5", "1.0", "1.5", "2.0".
metainfoValidation.attributeCodeAddFormError=The "Code" field must include at least one character, but not more than 14, begin with the latin alphabet character and consist only of latin alphabet characters and numbers.
metainfoValidation.attributeCodeEmptyError=Tag "code" in attribute for the type/class ''{0}'' does not exist.
metainfoValidation.attributeCodeError=Tag "code" in attribute for the type/class ''{0}'' contains invalid value ''{1}''. Value of the tag must be string with the length from 1 to 14 characters of latin alphabet characters and numbers, starting with a character.
metainfoValidation.attributeCodeIsSameWithMetaclassCode=The attribute could not be added. The attribute code of the type "Set of links to BO" or "Set of directory elements" cannot have a code identical to the metaclass code. Change the attribute code
metainfoValidation.attributeComputableError=Tag "script" in attribute with the code ''{1}'' for the type/class ''{0}'' does not exist.
metainfoValidation.attributeGroupError=Tag "attribute" in attribute group with the code ''{1}'' for the type/class ''{0}'' contains invalid value ''{2}''. Attribute with the code ''{2}'' in metaclass does not exist.
metainfoValidation.attributeGroupTitleEmptyError=Tag "title" in attribute group with the code ''{1}'' for the type/class ''{0}'' does not exist.
metainfoValidation.attributeGroupTitleError=Tag "title" in attribute group with the code ''{1}'' for the type/class ''{0}'' contains invalid value ''{2}''. Value of the tag must be string with the length from 1 to 256 characters.
metainfoValidation.attributeOverrideNotExistsError=Tag "code" in override of the attribute for the type/class ''{0}'' contains invalid value ''{1}''. Attribute with this code must exist in metaclass.
metainfoValidation.attributePresentationError=Tag "{3}Presentation/code" in attribute with the code ''{1}'' for the type/class ''{0}'' contains invalid value ''{2}''. For attributes of this type the tag can possess values: {4}.
metainfoValidation.attributeTitleEmptyError=Tag "title" in attribute with the code ''{1}'' for the type/class ''{0}'' does not exist.
metainfoValidation.attributeTitleError=Tag "title" in attribute with the code ''{1}'' for the type/class ''{0}'' contains invalid value ''{2}''. Value of the tag must be string with the length from 1 to 64 characters.
metainfoValidation.attributeTypeEmptyError=Tag "type" in attribute with the code ''{1}'' for the type/class ''{0}'' does not exist.
metainfoValidation.attributeTypeError=Tag "type/code" in attribute with the code ''{1}'' for the type/class ''{0}'' contains invalid value ''{2}''.
metainfoValidation.attributeUsagePlace="{0}" (class "{1}"{2}, tab "{3}" of object card, content "{4}", attribute "{5}")
metainfoValidation.attributeUsagePlaceOthers=and others.
metainfoValidation.attributeUsagePlaceType=, type "{0}"
metainfoValidation.catalogAttributeCollision=The imported attribute`s reference is different with existing attribute reference in class "{1}". Attribute code: "{0}", imported attribute reference: "{2}", existing attribute reference: "{3}"
metainfoValidation.checkLicenseGroupsError=The license file does not contain default license groups for class/types: {0}.
metainfoValidation.commentListDetailedAttrGroupInvalidCode=Tag "detailedAttrGroup" in content with the code ''{2}'' in object card of the type/class ''{0}'' contains invalid value ''{3}''. Value of the tag must be code of existing attribute group.
metainfoValidation.commentListRelationAttrInvalidCode=Tag "relationAttrCode" in content with the code ''{2}'' in object card of the type/class ''{0}'' contains invalid value ''{3}''. Value of the tag must be code of existing attribute with type "Link to BO".
metainfoValidation.commentShowAttributeAttachedInvalidCode=Tag "showAttributeAttachedFiles" in content with the code ''{2}'' in object card of the type/class ''{0}'' contains invalid value ''{3}''. Value of the tag must be code of existing attribute with type "File".
metainfoValidation.contentCaptionEmptyError=Tag "caption" in content with the code ''{2}'' in object card of the type/class ''{0}'' does not exist.
metainfoValidation.contentCaptionError=Tag "caption" in content with the code ''{2}'' in object card of the type/class ''{0}'' contains invalid value ''{3}''. Value of the tag must be string with the length from 1 to 255 characters.
metainfoValidation.fileListRelationAttrInvalidCode=Tag "attrChain" in content with the code ''{2}'' in object card of the type/class ''{0}'' contains invalid value ''{3}''. Value of the tag must be code of existing attribute with type "Link to BO".
metainfoValidation.formLayoutError=Tag "layout" in form of the type/class ''{0}'' does not exist.
metainfoValidation.homePage.useContentInHomePageError=It or its nested tabs are used in the settings of the home page: {0}
metainfoValidation.importCatalogs.deleteCatalogForExistedReference=Metainformation cannot be loaded. There are objects in the system associated with the directory element "{0}", which is not in the metainformation.
metainfoValidation.importEventActions.arriveMessageOnQueue.errorLoad=During metainformation loading the event action ''{0}'' cannot be uploaded because the queue already has an event action of the "Message arrival in the queue" type or the type of messages being processed in the queue is not available for this type of event, or queue was not found.
metainfoValidation.importEventActions.eventActionIdDoesNotSyncWithUserEventId=Metainformation cannot be uploaded. The identifier of the user event {0} does not match the identifier of the associated event action {1}
metainfoValidation.importMetaClasses.deleteMetaClassForExistedObjects=Metainformation cannot be loaded. There are objects of the "{0}" metaclass in the system, which is not in the metainformation.
metainfoValidation.invalidImportMode=Metainformation cannot be loaded. Partial metainformation cannot be loaded with full replacement of the settings.
metainfoValidation.licenseGroup=license group: {0}
metainfoValidation.linkAttributeCollision=The imported attribute`s object type reference is different with existing attribute reference in class "{1}". Attribute code: "{0}", imported attribute reference: "{2}", existing attribute reference: "{3}"
metainfoValidation.linkToContent.attrGroupUsedAtObjectList=Tag "attributeGroup" in item for left menu ''{2}'' contains invalid value ''{3}''. Value of the tag must be code of the existing attribute group.
metainfoValidation.linkToContent.attrUseAtObjectList=Attribute ''{3}'' is used in item for left menu ''{2}'', but does not exist or contains invalid value.
metainfoValidation.linkToContent.contentCaptionError=Tag "caption" in item for left menu ''{2}'' contains invalid value ''{3}''. Value of the tag must be string with the length from 1 to 255 characters.
metainfoValidation.linkToContent.objectListBasePresentationError=The "presentation" tag in the settings of the left menu item ''{2}'' contains an incorrect value ''{3}''. The tag can take the following values: "default", "advlist".
metainfoValidation.linkToContent.useAtMenuHierarchyGrid=Structure with code ''{3}'' used in menu item settings ''{2}'' does not exist.
metainfoValidation.linkToContent.useAtObjectList=Class/type ''{3}'' is used in item for left menu ''{2}'', but does not exist
metainfoValidation.metaclassHasNoWorkflow=No life cycle associated with class ''{0}''
metainfoValidation.notAllowChangeEndStateProperty=Settings from the file cannot be applied. The value of the "Final status" parameter for the status "{0}" in the type / class "{1}" in the system cannot be changed from true to false.
metainfoValidation.nullLicenseAttrubuteMessage=License attribute or attribute default value is NULL. MetaClass: "'{}'"
metainfoValidation.objectAttributeTypeAttrChainError=Tag "type/property" with the XML-attribute ''attrChain'' in attribute with the code ''{1}'' for the type/class ''{0}'' contains invalid value ''{3}''. Attribute with the code ''{3}'' in the type/class ''{2}'' does not exist.
metainfoValidation.objectAttributeTypeBackLinkError=Tag "type/property" with the XML-attribute ''backLinkAttr'' in attribute with the code ''{1}'' for the type/class ''{0}'' contains invalid value ''{3}''. Attribute with the code ''{3}'' in the type/class ''{2}'' does not exist.
metainfoValidation.objectAttributeTypeError=Tag "type/property" with the XML-attribute ''metaClassFqn'' in attribute with the code ''{1}'' for the type/class ''{0}'' does not exist.
metainfoValidation.objectAttributeTypeMetaClassError=Tag "type/property" with the XML-attribute ''metaClassFqn'' in attribute with the code ''{1}'' for the type/class ''{0}'' contains invalid value ''{2}''. Type/class ''{2}'' does not exist.
metainfoValidation.objectAttributeTypeRelatedAttributeError=Tag "type/property" with the XML-attribute ''relatedObjectAttribute'' in attribute with the code ''{1}'' for the type/class ''{0}'' contains invalid value ''{3}''. Attribute with the code ''{3}'' in the type/class ''{2}'' does not exist.
metainfoValidation.objectListBasePresentationError=Tag "presentation" in content with the code ''{2}'' in object card of the type/class ''{0}'' contains invalid value ''{3}''. The tag can possess values: "default", "advlist".
metainfoValidation.parametersNotCorrectInStructuredObjectsViewItem=Structure item ''{0}'' (''{1}'') of structure ''{2}'' (''{3}'') contains parameters, which does not exist in the system: code = ''{4}''. The value of the parameter had been set to the default value - "[not specified]"
metainfoValidation.plannedVersion.allowedClassesConflict=Metainformation cannot be uploaded. The "Allow creation of planned versions" property is enabled in classes for which versioning is prohibited by the license file settings: {0}
metainfoValidation.propertyListAttributeGroupEmpty=Tag "attributeGroup" in content with the code ''{2}'' in object card of the type/class ''{0}'' does not exist.
metainfoValidation.relAttrNotCorrectInStructuredObjectsViewItem=Invalid relation attribute "{0}" use in structure "{1}", in item "{2}"
metainfoValidation.scriptModuleCodeExists=Module ''{0}'' could not be added. A module with this code already exists.
metainfoValidation.scriptsCodeIncorrect=Tag "code" in script "{0}" has invalid value "{1}". The "Code" field must include at least one character, but not more than 64, begin with the latin alphabet character and consist only of latin alphabet characters and numbers.
metainfoValidation.scriptsNotExists=Scripts with following codes do not exist: {0}.
metainfoValidation.scriptsTitleCantBeEmpty=Script name ''{0}'' must be filled in.
metainfoValidation.scriptsTitleExceedsMaxLength=Script name ''{0}'' is longer than max. size {1}.
metainfoValidation.structureCodeError=Tag "code" for structure / structure element ''{0}'' contains an invalid value. The tag value must be a string of Latin letters and numbers from 1 to 255 characters long, starting with a letter.
metainfoValidation.structuredObjectsViewInAttributeNotExist=Structure "{0}" refers to the attribute "{1}" in of the type/class "{2}", does not exist.
metainfoValidation.structuredObjectsViewInContentNotExist=Structure "{0}" refers to the content with uuid "{1}" does not exist.
metainfoValidation.structuredObjectsViewInParameterNotExist=Structure "{0}" refers to the parameter "{1}" in event action "{2}" ("{3}"), does not exist.
metainfoValidation.tabBarNoTabsError=Tag "tab" in content with the code ''{2}'' in object card of the type/class ''{0}'' does not exist.
metainfoValidation.tabCaptionEmptyError=Tag "caption" in tab with the code ''{2}'' for the type/class ''{0}'' does not exist.
metainfoValidation.tabCaptionError=Tag "caption" in tab with the code ''{2}'' for the type/class ''{0}'' contains invalid value ''{3}''. Value of the tag must be string with the length from 1 to 64 characters.
metainfoValidation.tabLayoutError=Tag "layout" in tab with the code ''{2}'' for the type/class ''{0}'' does not exist.
metainfoValidation.timerAttributeDefinitionError=Tag "type/property" with the XML-attribute ''definition'' in attribute with the code ''{1}'' for the type/class ''{0}'' contains invalid value ''{2}''. Timer configuration ''{2}'' does not exist.
metainfoValidation.timerAttributeError=Tag "type/property" with the XML-attribute ''definition'' in attribute with the code ''{1}'' for the type/class ''{0}'' does not exist.
metainfoValidation.timerAttributePossibleError=Tag "type/property" with the XML-attribute ''definition'' in attribute with the code ''{1}'' for the type/class ''{0}'' contains invalid value ''{2}''. Timer ''{2}'' refers to another type.
metainfoValidation.uiChangeCaseForFqnAlreadySet=The user form for change object type is already set for type ''{0}'' (''{1}''). You cannot set more than one form for type.
metainfoValidation.uiChangeResponsibleForFqnAlreadySet=The user form for change object responsible is already set for type ''{0}'' (''{1}''). You cannot set more than one form for type.
metainfoValidation.uiCodeEmptyError=Tag "code" in description of the user interface for the type/class ''{0}'' does not exist.
metainfoValidation.uiCodeError=Tag "code" in description of the user interface for the type/class ''{0}'' contains invalid value ''{1}''.
metainfoValidation.uiContentEmptyError=Tags "window" and "form" in description of the type/class interface ''{0}'' do not exist.
metainfoValidation.uiFqnEmptyError=Tag "fqn" in description of the type/class interface does not exist.
metainfoValidation.uiFqnError=Tag "fqn" in description of the type/class interface contains invalid value ''{0}''. Type/class ''{0}'' not specified.
metainfoValidation.usagePointInAttributeInStructuredObjectsView=Structure "{0}" in list "Usage point", refers to the attribute, which does not exist in the system: fqn = "{1}".
metainfoValidation.usagePointInContentInStructuredObjectsView=Structure "{0}" in list "Usage point", refers to the content, which does not exist in the system: uuid = "{1}".
metainfoValidation.usagePointInParameterInStructuredObjectsView=Structure "{0}" in list "Usage point", refers to the parameter code "{1}" in event action "{2}", which does not exist in the system.
metainfoValidation.useAtAddFileTool=Tag "attributeGroupForAddFile" in tool ''{2}'' in card/form of the type/class ''{1}'' contains invalid value ''{3}''. Value of the tag must be code of the existing attribute group.
metainfoValidation.useAtCustomForm=Tag "attributeGroup" in content with the code ''{2}'' in custom form of the type ''{0}'' contains invalid value ''{3}''. Value of the tag must be code of the existing attribute group.
metainfoValidation.useAtObjectGraphMC=Metaclass with the code ''{3}'' is used in card for the type/class ''{1}'' in the content ''{2}'', but does not exist
metainfoValidation.useAtObjectList=The value ''{3}'' is used in card for the type/class ''{0}'' in the content ''{2}'', but does not exist
metainfoValidation.useAtPropertyList=Tag "attributeGroup" in content with the code ''{2}'' in object card of the type/class ''{0}'' contains invalid value ''{3}''. Value of the tag must be code of the existing attribute group.
metainfoValidation.useAtRelObjPropertyListAttr=Tag "attrCode" in content with the code ''{2}'' in object card of the type/class ''{0}'' contains invalid value ''{4}''. Value of the tag must be string with the length from 1 to 14 characters of latin alphabet characters and numbers, starting with a character.
metainfoValidation.useAtRelObjPropertyListGrp=The attribute group ''{3}'' is used in card for type/class ''{0}'' in the content ''{2}'', but does not exist
metainfoValidation.useAtRelObjPropertyListMC=The value ''{3}'' is used in card for the type/class ''{0}'' in the content ''{2}'', but does not exist
metainfoValidation.useAtSelectParent=Attribute "{3}" is used in the settings of parent selection parameters in the interface settings in the metaclass "{1}"
metainfoValidation.useAttrRelObjectListAttrLink=Attribute ''{3}'' does not exist in the metaclass ''{1}''
metainfoValidation.wfProfile.noCode=Code of the profile of related workflow ''{0}'' not specified
metainfoValidation.wfProfile.noDisconnectingState=State to unlink for the profile of related workflow ''{0}'' not specified
metainfoValidation.wfProfile.noMaster=Master class for the profile of related workflow ''{0}'' not specified
metainfoValidation.wfProfile.noSlave=Slave class for the profile of related workflow ''{0}'' not specified
metainfoValidation.wfProfile.noTitle=Title of the profile of related workflow ''{0}'' for localization ''{1}'' not specified
metainfoValidation.wfProfile.noTitles=Title of the profile of related workflow ''{0}'' not specified
metainfoValidation.wfProfileFolder.noFolder=Folder code not specified for profiles of related workflows: {0}
metainfoValidation.windowTabBarError=Tag "tabBar" in card of the type/class ''{0}'' does not exist.
metainfoValidation.youHaveDeletedAttributesWithButtons=You have deleted attribute(s) "{0}" from attribute group "{1}".\nIf you confirm the action, with attributes will be deleted next buttons:\n{2}.
metainfoValidation.eventAction.missingMetaclass=Metadata cannot be loaded. The "fqn/id" tag for the event with the fqn code {0} contains an incorrect value: {1}. The type/class {1} is missing.
metainfoValidation.eventAction.missingAttribute=Metadata cannot be loaded. The "action/attributes" tag with the XML attribute "code" for the event with the fqn code {0} contains an incorrect value: {1}. The type/class {2} does not contain an attribute with the code {1}.
metainfoValidation.eventAction.missingContextAttribute=Metadata cannot be loaded. The "action/context-attributes" tag with the XML attribute "code" for the event with the fqn code {0} contains an incorrect value: {1}. The type/class {2} does not contain an attribute with the code {1}.
metainfoValidation.eventAction.missingCustomFormMetaclass=Metadata cannot be loaded. The type/property tag of the parameter {1} from the event with the fqn code {0} contains an incorrect value in the XML attribute metaClassFqn. The class/type {2} is missing.
metainfoValidation.eventAction.missingCustomFormCatalog=Metadata cannot be loaded. The type/property tag of the parameter {1} from the event with the fqn code {0} contains an incorrect value in the XML attribute metaClassFqn. The directory with the code {2} is missing.
metainfoValidation.ntkAttr.missingMetaclass=Meta information could not be loaded. The tag "type/property" with XML attribute 'metaClassId' in the attribute with code {0} for the type/class {1} contains an invalid value {2}. The type/class {2} is missing.
metric=Metric
mobile.addButton=Add
mobile.addFormNotFound=Requested add form "{0}" was not found in mobile application settings.\nSelect existing add form on mobile application settings page, or enter the correct address in browser.
mobile.addFormNotFoundByParams=You can not add object of this type from the mobile application.
mobile.addFormObjectCantBeCreated=You can not add object through the selected form.
mobile.addFormObjectCreated=The object "{0}" was successfully added
mobile.addFormObjectCreatedWithoutTitle=The object was successfully added
mobile.addForms=Add forms
mobile.attrNotFound=Attribute with code "{0}" was not found in class/types.
mobile.attrNotFoundInCard=Card attribute cannot be edited, because it was not found in card.
mobile.attrNotFoundInList=List attribute cannot be edited, because it was not found in list.
mobile.canOpenInMobileApp=This link can be opened<br>in mobile app
mobile.cardNotFound=Requested card "{0}" was not found in mobile application settings.\nSelect existing card on mobile application settings page, or enter the correct address in browser.
mobile.commentWasDeleted=Comment can not be viewed, as it was removed.
mobile.contentDetails="{0}" on object card with the code "{1}"
mobile.contents.actions.sort=Sort
mobile.contents.actions.filtration=Filtration
mobile.contents.information.caption=Information
mobile.currentType=Current type
mobile.deleteContents.addForms=Add forms: {0}.
mobile.deleteContents.cards=Object cards: {0}.
mobile.deleteContents.contentCards=Contents: {0}.
mobile.deleteContents.editForms=Edit forms: {0}.
mobile.deleteContents.lists=Object lists: {0}.
mobile.duplicateContentUuidError=Metainformation cannot be uploaded. The uploaded file contains the same code ''{1}'' for several mobile application settings ({0}).
mobile.editFormNotFound=Requested edit form "{0}" was not found in mobile application settings.\nSelect existing edit form on mobile application settings page, or enter the correct address in browser.
mobile.editForms=Edit forms
mobile.error.attributeFqnIncorrectFormat=Attribute "{0}" is represented in the incorrect format.
mobile.error.couldNotCreateObjectForClass=Objects creation for classes is not allowed. A proper type needs to be specified.
mobile.error.couldNotCreateObjectOfSelectedCase=You can not add object of specified type through the selected form.
mobile.error.couldNotDeleteAddForm=Object''s add form in mobile application ''{0}'' ({1}) can not be deleted.
mobile.error.couldNotDeleteAddFormOnCards=The "[''{0}'']" element cannot be deleted because it is used on cards in content [{1}]
mobile.error.couldNotDeleteContent=The element "{0}" cannot be removed because it is used in the navigation menu.
mobile.error.couldNotDeleteEditForm=The element "{0}" cannot be removed because it is used in the "Edit" action:\n
mobile.error.couldNotDeleteEditFormOnCards=On cards in the menu of the object [{0}]
mobile.error.couldNotDeleteEditFormOnContents=On cards in the "Object Parameters" content [{0}].
mobile.error.couldNotDeleteMobileList=Object list "{0}" ({1}) cannot be deleted.
mobile.error.couldNotDeleteMobileListAttribute=List is used in settings of the attribute "{0}" ({1}) located in the object card with code "{2}".
mobile.error.couldNotDeleteUsedContent=Object list "{0}" ({1}) cannot be deleted. List is used in settings of content "{2}" ({3}) located in the object card with code "{4}".
mobile.error.couldNotHideContent=The element "{0}" cannot be hidden because it is used in the navigation menu.
mobile.error.couldNotPerformAction=Failed to perform the action.
mobile.error.createRasterIcon=When converting the file ''{0}'' into PNG format, an error occurred: {1}.
mobile.error.defaultServiceCallIsRemoved=You can not add object through the selected form. Case ''{0}'' is in archive.
mobile.error.noDefaultServiceCallAssociation=Default association is not defined for you. Contact your system administrator.
mobile.fileWasDeleted=File can not be viewed, as it was removed.
mobile.formHasChildren=This form is the parent for {0}.
mobile.haveSettingsForAttr=Attribute is used in mobile application settings.
mobile.haveSettingsForCase=Type is used in mobile application settings.
mobile.haveSettingsForClass=Class is used to configure lists, cards or edit forms of mobile application.
mobile.listNotFound=Requested list "{0}" was not found in mobile application settings.\nSelect existing list on mobile application settings page, or enter the correct address in browser.
mobile.loginType.customForm=Custom login form
mobile.loginType.customModule=External authentication service
mobile.loginType.external=Authentication via SSO
mobile.loginType.system=System login form
mobile.metainfo.attributeOfMetaclass={0} of metaclass {1}
mobile.metainfo.error.navigationMenu.notImported=The parameter "{0}" of mobile app navigation menu item "{1}" refers to the objects, which does not exist in the system: {2}. Navigation menu item has not been imported.
mobile.metainfo.error.objectCard.action.editFormNotExists=The parameter "Форма редактирования" (Edit form) of the item "{0}" of action menu of the card "{1}" refers to the edit form, which does not exist in the system: formCode = "{2}". The value of the parameter had been set to the default value - "[не указано]"
mobile.metainfo.error.objectCard.attrOfCaptionNotExist=The parameter "Object caption in mobile application" of the object's card "{0}" refers to the attribute, which does not exist in the system: "{1}". The value of the parameter had been set to the default value - "Title".
mobile.metainfo.error.objectCard.attrOfListNotExist=The parameter "List for display the value" of the attribute "{0}" on the card "{1}" refers to the list, which does not exist in the system: uuid = "{2}". The value of the parameter had been set to the default value - "[not specified]".
mobile.metainfo.error.objectCard.attrOfListNotFill=The parameter "List for display the value" of the attribute "{0}" on the card "{1}" must not to be filled. The value of the parameter had been set to the default value - "[not specified]".
mobile.metainfo.error.objectCard.content.action.editFormNotExists=The parameter "Форма редактирования" (Edit form) of the item "Редактировать" in the "Object parameters" content with the code "{0}" refers to the edit form, which does not exist in the system: formCode = "{1}". The value of the parameter had been set to the default value - "[не указано]".
mobile.metainfo.error.objectCard.content.tagsNotExistOnCard=The following tags in the "Tags" parameter are not found on the "{0}" card: "{1}". The undetected tags in the parameter are not set.
mobile.metainfo.error.objectCard.content.tagsNotExistOnContent=In the "{0}" content on the "{1}" card the following tags specified in the "Tags" parameter are not found: "{2}". The undetected tags in the parameter are not set.
mobile.metainfo.error.objectCard.parameterControlNotExist=The parameter "Action" of the item "{0}" of action menu of the card "{1}" refers to the action, which does not exist in the system: uuid = "{2}". The value of the parameter had been set to the default value - "[not specified]".
mobile.metainfo.error.securitySettings.customLoginFormCode="Mobile application" page, "Other" tab, "Security" block, unavailable value of the "Login form" parameter.
mobile.metainfo.error.securitySettings.customLoginModuleCode="Mobile application" page, "Other" tab, "Security" block, unavailable value of the "Script module" parameter.
mobile.metainfo.error.securitySettings.loginAttemptsCount="Mobile application" page, "Other" tab, "Security" block, invalid parameter value "Number of unsuccessful PIN attempts, after which all data deleted".
mobile.metainfo.error.securitySettings.loginType="Mobile application" page, "Other" tab, "Security" block. The "Authentication type" parameter (login-type tag) contains a value that is not available with current system settings – "{0}" ({1}).
mobile.metainfo.error.securitySettings.passwordStorageTime="Mobile application" page, "Other" tab, "Security" block, invalid parameter value "Password storage time in mobile application".
mobile.navigationMenu.embeddedApplication=Embedded application
mobile.navigationMenu.error.couldNotGetLinkedObject=Could not get work object for embedded app in navigation menu item "{0}" ("{1}").
mobile.navigationMenu.linkObjectAttrs=Attribute of link to the object with which the app is working
mobile.navigationMenu.linkObjectCase=User type
mobile.navigationMenu.tags=Tags
mobile.newType=New type
mobile.noAddForms=You can not add objects from the mobile application.
mobile.noAppLoadFrom=No application?<br>Get on device&nbsp;
mobile.noAppLoadFromIos=No application?<br>Load on the&nbsp;
mobile.noPermissionsToChangeResponsible=You dont have permissions to change responsible in the class/type "{0}".
mobile.noPermissionsToChangeType=You don''t have permissions to change type of this object.
mobile.noPermissionsToChangeTypeAndAdd=You don''t have permissions to change type of this object to "{0}".
mobile.noPermissionsToSCAssociation=You have no permission for changing this request association.
mobile.objectCards=Object cards
mobile.objectLists=Object lists
mobile.openInApp=Open in mobile app
mobile.openInWeb=Continue in browser
mobile.openLink=Open link
mobile.remember=Remember choice
mobile.rest.access_token_error=Authorization key lifetime expired. Please re-enter the system.
mobile.rest.account.archived_error=Your account has been archived in system, contact your administrator.
mobile.rest.account.associated_super_user_error=Your superuser account has been linked to a user account. Please re-enter the system.
mobile.rest.account.locked_error=Your account has been locked in system, contact your administrator.
mobile.rest.action.not_for_mobile=The action can not be executed in the mobile application.
mobile.rest.action.not_found_error=The action is not listed. Probably, it have been deleted.
mobile.rest.action.object_deleted_error=This action cannot be done because the object has been deleted.
mobile.rest.action.requires_location_error=The action requires the user''s location.
mobile.rest.add_form_is_not_set_error=Add form is not set for type "{0} ({1})".
mobile.rest.card_is_not_set_error=Object card is not set for type "{0} ({1})".
mobile.rest.comment_add_form_title=Add comment
mobile.rest.content_is_not_available=You don''t have permissions to view content.
mobile.rest.custom_auth_error=An error occurred while authorizing the user. Contact your system administrator.
mobile.rest.custom_settings_error=An error occurred while getting the login form. Contact your system administrator.
mobile.rest.delete_files_permission_error=You have no permission to delete this file
mobile.rest.edit_form_is_not_set_error=Edit form is not set for type "{0} ({1})".
mobile.rest.events.locationPermissionRequired=Location permission is required.
mobile.rest.file_error.large=File is to large ({0})
mobile.rest.file_error.main=File wasn''t uploaded. {0}.
mobile.rest.file_error.malicious=This file was considered to be malicious
mobile.rest.file_error.unacceptable_extension=Unacceptable extension
mobile.rest.get_edit_form_error=You can not edit object using this form.
mobile.rest.message=message
mobile.rest.mobile_list_not_available_error=Current list is not available anymore
mobile.rest.mobile_object_not_exist_error=The object does not exist. It may have been deleted.
mobile.rest.module_is_not_available_error=License file does not contain module "Mobile Application"
mobile.rest.object_is_not_file=Object is not file: uuid={0}
mobile.rest.password.should_be_changed_error=Your password has been reset. Set a new password in the web application.
mobile.rest.title=title
mobile.rest.unsupported_api=This API is no longer available. Please log in to the app again.
mobile.rest.upload_file_size_exceed_error=File not uploaded. Possible causes: the file size exceeds maximum value, server''s file system is not available for recording files or it''s a malicious file.
mobile.rest.view_add_form_permission_error=You do not have enough permissions to view add form of type "{0} ({1})"
mobile.rest.view_card_permission_error=You do not have enough permissions to view card of type "{0} ({1})"
mobile.rest.view_comment_add_form_permission_error=You have no permission to add a comment.
mobile.rest.view_comment_card_permission_error=You have no permission to view this comment.
mobile.rest.view_comment_delete_permission_error=You have no permission to delete this comment.
mobile.rest.view_comment_edit_form_permission_error=You have no permission to edit this comment.
mobile.rest.view_comments_permission_error=You do not have enough permissions to view comments
mobile.rest.view_edit_form_permission_error=You do not have enough permissions to view edit form of type "{0} ({1})"
mobile.rest.view_files_permission_error=You do not have enough permissions to view files
mobile.rest.view_form_attrs_empty=You have no attributes available for editing.
mobile.rest.wrong.associatedSuperUserAuthError=You cannot log in. Superuser account is associated with the user account.
mobile.rest.wrong.login_pass_error=Your login or password is incorrect.
mobile.store.appgallery=AppGallery
mobile.store.appstore=App Store
mobile.store.googleplay=Google Play
mobile.store.rustore=RuStore
mobile.toolCaptionIsEmpty=Error in the settings of mobile app: The title of control, located in the card with code "{0}", has not been set.
mobile.transitionsNotFoundInCases=Following transitions: {1}, were not found in class/types: {0}.
mobile.transitionsNotFoundInClass=Following transitions: {1}, were not found in class "{0}".
mobile.useAttrRelObjectListAttrLink=Attribute ''{0}'' does not exist in the metaclass ''{1}''
mobile.usedInContentButton="{0}" (object card with the code ''{1}'', content ''{2}'' in the mobile application)
mobileQuickAction.take=Assign to myself
mobileQuickAction.takeTeam=Assign to my team
mobileSettings=Mobile application
mobileSettingsObjectCards=Object cards
mobileSettingsObjectLists=Object lists
monitoring=Monitoring system
moreThanOneEmployeeFailure=Unable to login. More than one account found, contact your administrator.
must.have.javascipt=JavaScript must be enabled in the browser.
naming.ruleNotValid=In the rule must be used only structures, listed in [Help] section.
navigation=Navigation
needCompressImage=Compress images in attributes "Text in RTF format"
nestedObjects=Nested objects
newEntryFormClazz=Add form for class
no=no
notAddAlreadyLinkFiles=Files ''{0}'' were not added to the form, as they are already attached to other objects.
notFileUuid=Not a file UUID: ''{0}''.
notSpecified=[not specified]
#ru.naumen.core.server.script.api.DbApi
notSupportedDBMS=Current DBMS is not supported
notification.body=Body
notification.object=object: ''{0}''
notification.script=Script
notification.subj=Subject
number=Number
objLinkedToCurrentUser=Object linked to current user
object=object
objectAddForm=object add form for metaclass
objectAlreadyIsInNewState=Transition to the status ''{0}'' can not be executed. Object {1} is already in this status.
objectCases=Object types
objectClass=Object class
objectEditForm=object edit form
objectFiles=Object files
objectList=Object List
objectsAlreadyAreInNewState=The requested transition to status ''{0}'' can''t be performed for the following objects of class ''{1}'': {2}. Objects already are in selected state.
#ru.naumen.metainfo.server.impl.ObjectsExistsListener
objectsExistsListener.childClassCanNotBeDeleted=Child {0} ''{1}'': is used in settings of event actions: {2}
objectsExistsListener.childClassCanNotBeDeletedBecauseOfRules=Child {0} ''{1}'': is used in an event log storage rule: {2}
objectsExistsListener.classCanNotBeDeleted={0} is used in settings of event actions: {1}
objectsExistsListener.classCanNotBeDeletedBecauseOfRules={0} is used in an event log storage rule: {1}
objectsExistsListener.etc=etc.
objectsExistsListener.objectsExists=Existing objects of this type: {0}.
objectsExistsListener.objectsOfChildTypeExists=Existing objects of the child type ''{0}'': {1}
objectsExistsListener.objectsOfClassExist=Existing objects of this class: {0}.
objectsTransitionDisabled=The requested transition to status ''{0}'' can''t be performed for the following objects of class ''{1}'': {2}. Transition is not permitted in accordance with the workflow settings.
ok=Ok
on=On
onlyFileTypeParam=Only objects of the ''File'' class can be transferred in the ''@files'' parameter.
openObjectCard=Open the object card
operation.OK=Operation completed successfully
crudOperationForbiddenMessage=Editing/Creating/Deleting objects of class Technologist using scripts is forbidden
operationForbiddenAddForSuperUsers=Unable to add a new superuser. The limit on the number of employees with the "Superuser" license has been reached.
operationForbiddenEditVendor=Editing the vendor is prohibited
orange=Orange
originalFileDoesNotExists=File not found.
otherAdminOptions=Other settings
ou=Department
#ou.domain.xml
ou.domain.ouHead=Head of department
ou.domain.ouMember=Employee of department
ou.domain.upperOUHead=Head of upper department
ou.domain.upperOUMember=Employee of upper department
#ou.window.xml
ou.window.AddComment=Add comment
ou.window.AddFile=Add file
ou.window.Comments=Comments
ou.window.Department=Department
ou.window.Files=Files
ou.window.History=History
ownerEverybody=All
parameters=Parameters
partition.cleanup=Migration for the ''{0}'' class is in the cleanup process.
partition.ddlPartitionTables=Migration for the ''{0}'' class in the process of creating sections.
partition.deletePartitionTables=Migration for the ''{0}'' class in the process of deleting a section.
partition.detachPartitionTables=Migration for the ''{0}'' class in the process of unpinning the section.
partition.error=Migration failed for the class ''{0}''.
partition.error.alreadyRun=There is already an incomplete migration for the ''{0}'' class.
partition.error.alreadyRunParams=Migration is already in progress, parameters cannot be changed.
partition.error.alreadyMigrated=The table with ''{0}'' is already sectioned by the attribute ''{1}''.
partition.error.changeParameterOfMigration=Parameter must be greater than 0.
partition.error.cleanup=For the class ''{0}'' the migration data cleanup failed.
partition.error.createAggregatedPartitionTable=For the ''{0}'' class the aggregation table for partitioning could not be created.
partition.error.createFunctionAndTrigger=For the ''{0}'' class failed to create a function and trigger for object migration.
partition.error.createPartitionTable=For the ''{0}'' class section could not be created with the ''{1}'' name.
partition.error.dbNotSupported=Partitioning is not available for the ''{0}''.
partition.error.deletePartition=For the ''{0}'' class, the section with the ''{1}'' name could not be deleted.
partition.error.detachPartition=For the ''{0}'' class, the section with the ''{1}'' name could not be disabled.
partition.error.forbiddenAttrCode=Sectioning by attribute with the code ''{0}'' for the class ''{1}'' is prohibited.
partition.error.forbiddenTransition=For the ''{0}'' class, transitioning from ''{1}'' to ''{2}'' is not allowed.
partition.error.forbiddenTransitionForResume=Migration has not been paused for class ''{0}''.
partition.error.getPartitionsName=No sectioned table was found for class ''{0}''.
partition.error.get=For the ''{0}'' class could not get information about the partitioned table.
partition.error.prepare=For the class ''{0}'' the preparation step failed.
partition.error.notExistsPartitionTable=For the ''{0}'' class, the section with the ''{1}'' name could not be found.
partition.error.notFoundMigration=There is no migration started for class ''{0}''.
partition.error.operationForbidden=The operation is prohibited for the current user.
partition.error.incorrectAttrType=For the ''{0}'' class partitioning by attribute type with code ''{1}'' is not available
partition.error.interruptedMigrate=For the class ''{0}'' migration of objects has been interrupted. See details in the application log.
partition.error.vacuum=For the ''{0}'' class it was not possible to disable autovacuum to start migration.
partition.error.wrongCountObject=For the ''{0}'' class less than two objects have been created. Partitioning by date is not available.
partition.error.wrongRule=An invalid value for the rule is specified for class ''{0}''. Valid values are: {1}.
partition.finished=There is no migration started for class ''{0}''.
partition.finishRollback=For class ''{0}'' migration cancellation has been completed.
partition.finishedMigration=Migration for class ''{0}'' has been completed successfully.
partition.finishedPrepareRollback=Cancellation of the migration has been prepared for the ''{0}'' class.
partition.finishedPrepareMigration=Migration has been prepared for the ''{0}'' class.
partition.finishedDdlPartitionTables=The creation of the ''{0}'' class sections is complete.
partition.processMigration=Migration for class ''{0}'' has been completed at {1}.
partition.prepareRollback=Cancellation of the migration preparation for the ''{0}'' class.
partition.prepareMigration=Migration preparation for the ''{0}'' class.
partition.pause=Migration for class ''{0}'' has been stopped at {1}.
partition.processRollback=Migration for class ''{0}'' has been canceled at {1}.
partition.revokedMigration=Migration for the class ''{0}'' has been canceled. You can start a new one.
password=Password
pc.module.unlicensed=Unlicensed
permissionSettings=Permission settings
personalServiceTimeCreatorValidator.canNotCreatePersonalServiceTime=You can not create a Personal Class of Service item using another Personal Class of Service item.
personalServiceTimeCreatorValidator.canNotSetPersonalServiceTimeToSC=You cannot set a Personal Class of Service "{0}" for an object of class "{1}".
phones=phones {0}
plannedVersion.BranchApi.objectNotFoundInBranch=Unable to add the object {0} to the branch. The object is not in the source branch.
plannedVersion.BranchApi.versionInfoNotFound=The object with the version information was not found.
plannedVersion.ErrorDetails.relatedTo={0} linked to the following entities in planned version mode ''{1}'': {2}
plannedVersion.PlannedVersionListener.module.off=The license for the planned versioning module ''{0}'' is missing or invalid. The mechanism for working with planned versions is disabled.
plannedVersion.PlannedVersionListener.module.on=The license file is loaded with the enabled scheduled versioning module ''{0}''. The mechanism for working with planned versions is enabled.
plannedVersion.bcp.AttributesAreBlockedOnEditMainBranch.error=The object attributes {0} is blocked for editing, because the object has versions in the following branches that are in blocking statuses: {1}.
plannedVersion.bcp.AttributesAreBlockedOnEditPlannedBranch.error=The attributes {0} of the object is blocked for editing, because the branch {1} is in the blocking status.
plannedVersion.bcp.CheckEnvironmentObjectOperation.error=The environment object cannot be edited in change planning mode. For editing, add the object to the branch.
plannedVersion.bcp.ObjectAreBlockedOnDeleteMainBranch.error=The object is blocked for deletion, since the object has versions in the following branches that are in blocking statuses: {0}
plannedVersion.bcp.ObjectAreBlockedOnDeletePlannedBranch.error=The object is blocked for deletion, because the branch {0} is in a blocking status.
plannedVersion.bcp.ValidateCreationPlannedVersionOperation.error=Creating planned versions for the ''{0}'' class ({1}) is prohibited by the license file settings
plannedVersion.branch={0} "{1}"
plannedVersion.branchSysNonBlockedAttributeGroup=Non-blocking attributes (planned version)
plannedVersion.branches=Branches
plannedVersion.deleteBranch.usedByAnotherUser=The object "{0}" cannot be deleted. The following users are in change planning mode for the current object: {1}.
plannedVersion.editAttrNotAllowedInPlaningMode=The ''{0}'' ({1}) attribute of type ''{2}'' is not editable in change planning mode.
plannedVersion.editDisabled=The "Allow creation of planned versions" property cannot be changed.
plannedVersion.linkToListCanNotBeOpened=The link cannot be opened for the following reasons:\n1. The link leads to the list of objects from the planning mode.
plannedVersion.locked=Branch locked
plannedVersion.mainBranch=Master branch
plannedVersion.plannedVersionCantBeDisabled=The "Allow creation of planned versions" property cannot be disabled.\n1. Versions of objects or environment objects of "{0}" class are used in: {1}\nExclude class objects from all branches.
plannedVersion.plannedVersionCantBeEnabled=The "Allow creation of planned versions" property cannot be enabled for the following reasons:
plannedVersion.plannedVersionCantBeEnabledReason=In the planning mode ''{0}'', the class objects are associated with the object versions: {1}
plannedVersion.plannedVersions=Planned versioning
plannedVersion.rest.LinkToVersion.restricted=Work with objects of the class "Object version information" (sys_objVerInfo) is not available.
plannedVersion.restricted=Creating planned versions for the ''{0}'' class ({1}) is prohibited
plannedVersion.selectAvailableObject=\nSelect an available object using the navigation menu or search.
plannedVersion.skipAddUnversionedObjects=Method {0} failed for objects {1}, because in classes {2} versioning is prohibited.
plannedVersion.stateUsedInBlockingVersionsSettings=Status ''{0}'' is used in settings to block object versions.
plannedVersion.unableToFindSnapshot=Unable to find the object snapshot. The same branch is specified as the source and the destination branches.
plannedVersion.wrongArgumentType=The object passed in the argument must be an instance of the SnapshotsDto class.
plannedVersion.wrongHomePageURL=Error. Invalid homePage value - planning version or environment object. Set the object page in the main mode.
polishLang=Polish language
preauthFailed=External pre-authorization failed
#PrefixObjectLoaderServiceImpl
prefixObjectLoaderService.objectNotFound=Object not found: uuid={0}
presentation=Representation
print=Print
privateCommentView=Show private comments
#utils.processDocx
processDocx.error=An error occurred while template processing.
processDocx.notFileError=Seems like data that was thrown into method is not a file.
processDocx.wrongFormat=Wrong file format. Please, use *.docx instead.
processSettings=Business process settings
processingRules=Processing rules
profileVers=[vers.]
properties=Properties
#ru.naumen.core.server.script.spi.ScriptDtOHelper
propertyChangingDeniedInScript=Direct values settings of the object properties are not permitted. To edit the properties, please use corresponding methods calls
proxyService.authProblem=authentication on the proxy server failed
proxyService.connectProblem=connection to the proxy server failed or the error occurred on FCM service side
proxyService.incorrectHostPort=the settings for connecting to the proxy server are incorrect
push.body=Body
push.object=object: ''{0}''
push.script=Script
pushApi.employeeBlockedOrArchived=The notification cannot be sent to the employee. Possible reasons: employee is blocked or archived
pushApi.employeeNotFound=The employee is not found
pushApi.nullRecipient=The recipient can not be null
pushApi.templateNotFound=Style template not found
pushHeaderFormatType.subject=Subject
pushHeaderFormatType.systemName=System name
quickAccessPanel=Quick access panel
quickAccessPanel.MenuRoot.defaultHint=Show whole menu
quickActions.cyclicDependenciesDetected=Unable to add objects due to values of their required attributes has cyclic dependencies
quickActions.cyclicDependency=value of required attribute "{0}" in object "{1}" refers to object "{2}"
quickActions.noname=[{0} without title]
quickForms.attributeOfCase="{0}" (type "{1}" of class "{2}")
quickForms.attributeOfClass="{0}" (class "{1}")
quickForms.import.invalidAddAttributeReference=Attribute "{0}" of class/type "{1}" refers to quick add form with code "{2}" not existing within attribute restrictions. Reference will be replaced by "null" value.
quickForms.import.invalidEditAttributeReference=Attribute "{0}" of class/type "{1}" refers to quick edit form with code "{2}" not existing within attribute restrictions. Reference will be replaced by "null" value.
quickForms.unableToDelete=Form "{0}" cannot be deleted for the following reasons:{1}
quickForms.unableToEditCases=Form "{0}" cannot be edited for the following reasons:{1}
quickForms.usedInAttribute=Form is used in settings of the attribute {0}.
quickForms.usedInAttributeParam=Form is used in the event action parameter settings {0}.
quickForms.usedInAttributeParamRestrictions=Form is used in settings of the event action parameters {0} having type restrictions that do not meet types available for form.
quickForms.usedInAttributeParamsRestrictions=Form is used in settings of the event action parameters {0} having type restrictions that do not meet types available for form.
quickForms.usedInAttributeRestrictions=Form is used in settings of the attribute {0} having type restrictions that do not meet types available for form.
quickForms.usedInAttributes=Form is used in settings of the attributes {0}.
quickForms.usedInAttributesRestrictions=Form is used in settings of the attributes {0} having type restrictions that do not meet types available for form.
quickForms.usedInTool=Form is used in settings of the action tool {0}.
quickForms.usedInTools=Form is used in settings of the action tools {0}.
rebuildReport=Rebuild
refresh=Refresh
relObjectList=List of related objects
reloadableSessionFactoryBean.clusterRealoadError=The operation cannot be performed because the synchronization of metainformation on other node not completed. Please try again later.
reloadableSessionFactoryBean.concurrentRealoadError=The operation cannot be performed because the previous operation with metainformation not completed. Please try again later.
reloadableSessionFactoryBean.concurrentSchemaOptimizationError=The operation cannot be performed because the database optimization operation running. Please try again later.
removed.false=active
removed.true=archive
removedShort=(arch.) {0}
#ReportParameters
reportParameters.catalogNotFound=Parameter "{0}"({1}) refers to the catalog which doesn''t exist in the system: catalog code={2}.
reportParameters.missingMetaclass=Parameter "{0}"({1}) refers to the metaclass which doesn''t exist in the system: fqn={2}.
reportParameters.nonExistentObjects=Default value of the parameter "{0}"({1}) refers to the objects which doesn''t exist in the system: uuids={2}.
reportParameters.nonExistentStates=Default value of the parameter "{0}"({1}) refers to the states in metaclass {2} which doesn''t exist in the system: {3}.
reportParameters.reportBuildIdAlreadyExist=The process of building a report with this identifier already exists.
reportTemplates=Report templates and printed forms
#reports
reports.generationReportError=System Error. Report generation failed. Please, contact your support.
reports.missingQuoteInAdditionalParametersError=An error occurred during script execution for templates: ''{0}''\nCheck that the '' (single quote) symbol is set up correctly for the attribute ''{1}'' in the script
reports.maxMemoryExceededError=Exceeds the maximum size of memory available for the report. Report generation failed. Please, contact your support.
reports.maxShowTableSizeExceededError=The report is created and available for download. In order to obtain the report, please, use export to available formats (export button located in the header of the report). The created report can''t be displayed in the system interface since it exceeds the size limit set by the administrator.
reports.report=report ''{0}''
reports.sentEmail.endGeneration=End generation report/printing form ''{0}''
reports.sentEmail.export=Export report "{0}" {1}
reports.sentEmail.failure=not sent to addressee:
reports.sentEmail.fileDownloadLink=Link for downloading file with report/printing form "{0}": <br><a href="{1}">{1}</a>.<br>
reports.sentEmail.fileLifetime=<br>File will be available for download {0} (until {1}).
reports.sentEmail.fileUserAccess=<br>Only user, who generated report/printing form, can download file.
reports.sentEmail.reportCardLink=<br><br>Link to report/printing form card: <a href="{0}">{1}</a>
reports.sentEmail.send.with.file=Report "{0}" generated: {1}
reports.sentEmail.send.with.file.description=Report "{0}" in the format {1} generated: {2}. The report file is in the attachment.
reports.sentEmail.sent=Mail with link for downloading file with report/printing "{0} {1}" was sent to addressee {2}.
reports.sentEmail.sent.attachment=Mail with file of report/printing "{0} {1}" was sent to address {2}.
reports.sentEmail.startGeneration=Start generation report/printing form ''{0}''
reports.sentEmail.success=sent to addressee:
resolver.notIntegerValue=Value ''{0}'' is not integer
responsibilityTransfer=Responsibility transfer
responsibleCurrentTeamEmployee=Assign responsible for the object employee from the team of the current responsible
responsibleStrategy.authorResponsibleStrategy.superUserError=Superuser cannot responsible for request
responsibleStrategy.authorResponsibleStrategy2=Object author
responsibleStrategy.currentResponsibleStrategy=Current responsible
responsibleStrategy.emptyResponsibleStrategy=Without responsible
responsibleStrategy.prevResponsibleStrategy=Previous responsible
responsibleStrategy.prevResponsibleStrategy.serviceError=Field "Service" in the request is not filled in
responsibleStrategy.prevResponsibleStrategy.serviceResponsibleError=Field "Curator" in the request service is not filled in
responsibleStrategy.prevTeamResponsibleStrategy=Team of the previous responsible
responsibleStrategy.prevTeamResponsibleStrategy.notSetError=Team of the previous responsible not specified
responsibleStrategy.serviceResponsibleStrategy=Curator of the service
responsibleStrategy.solvedResponsibleStrategy=Request resolved by
responsibleStrategy.solvedResponsibleStrategy.notSolvedError=In the request field "Resolved by" not filled in
responsibleStrategy.teamLeaderResponsibleStrategy=Team leader of the current responsible
responsibleStrategy.teamLeaderResponsibleStrategy.leaderError=Team leader of the current responsible not specified
responsibleStrategy.teamLeaderResponsibleStrategy.teamError=Team of the current responsible not specified
responsibleStrategy.teamResponsibleStrategy=Team of the current responsible
responsibleStrategy.teamResponsibleStrategy.notSetError=Team of the current responsible not specified
responsibleValidationOperation.notInTeamError=Employee ''{0}'' is not the member of the team ''{1}''
responsibleValidationOperation.notTeamError=Responsible team not specified
responsibleValidationOperation.performerEmployeeError=Employee ''{0}'' is in archive or is not licensed
responsibleValidationOperation.performerError.employee=Employee is in archive or is not licensed
responsibleValidationOperation.performerError.team=In the team do not exist non-archive employees - performers or the team is in archive
responsibleValidationOperation.performerTeamError=In the team ''{0}'' do not exist non-archive employees - performers or the team is in archive
responsibleValidationOperation.permissionError=You don''t have permissions to assign the specified responsible
responsibleValidationOperation.statusError=In the "{0}" state settings the class of responsible ''{1}'' is not available
responsibleValidationOperation.teamEmptyError=Team ''{0}'' does not contain any active employees
responsibleValidationOperation.transferError=Delegation of responsibility from ''{0}'' to ''{1}'' is not allowed
restServiceFailture=Enter login and password to confirm action.
richtext=Text in RTF format
roles=Roles
root.window.Agreements=SLA''s
root.window.AgreementsAndServices=SLA''s and services
root.window.ArchiveNoun=Archive
#root.window.xml
root.window.Attributes=Company attributes
root.window.Departments=Departments
root.window.ExportList=Export list
root.window.Filter=Filter
root.window.Issues=Requests
root.window.ObjectsList=Object list
root.window.Orgstructure=Company structure
root.window.RefreshList=Refresh list
root.window.SaveListView=Save view
root.window.Services=Services
root.window.Sort=Sort
root.window.Teams=Teams
ru.naumen.metainfo.shared.elements.mail.SendMailParameters.resendDelay=Parameter "Resending delay" must be greater then ''{0}'' s.
ru.naumen.sd.bobjects.nomenclature.designs.DayDesign.HelpString=The structure '{'DD'}' will be replaced by the system with the current day of the month
ru.naumen.sd.bobjects.nomenclature.designs.DayIdDesign.HelpString=The structure '{'ND'}' will be replaced by the system with the identifier unique within counter of the class within the day
ru.naumen.sd.bobjects.nomenclature.designs.HourDesign.helpString=The structure '{'HH'}' will be replaced by the system with the current hour in the 24-hour format
ru.naumen.sd.bobjects.nomenclature.designs.IdDesign.HelpString=The structure '{'N'}' will be replaced by the system with the identifier unique within counter of the class
ru.naumen.sd.bobjects.nomenclature.designs.LengthHelp=The structure '{'?N'}', where instead of “?” is specified a natural number, will be replaced by the system with the unique identifier within the class counter, in which “?” is the length to which will be added leading zeros. When using this construct for attributes of the type “Integer”, leading zeros will be ignored, if they are not preceded by a non-zero number.
ru.naumen.sd.bobjects.nomenclature.designs.MinuteDesign.HelpString=The structure '{'mm'}' will be replaced by the system with the minutes of the current time
ru.naumen.sd.bobjects.nomenclature.designs.MonthDesign.HelpString=The structure '{'MM'}' will be replaced by the system with the current month
ru.naumen.sd.bobjects.nomenclature.designs.RandomIdDesign.HelpString=The structure '{'RND'}' will be replaced by the system with the random identifier unique within counter of the class
ru.naumen.sd.bobjects.nomenclature.designs.ServiceCallIdTitleDesign.HelpString=The structure '{'SCID'}' will be replaced by the system with the request identifier (to use in the title of the request)
ru.naumen.sd.bobjects.nomenclature.designs.SourceNameDesign.HelpString=The structure '{'SNum'}' will be replaced by the system with the source title
ru.naumen.sd.bobjects.nomenclature.designs.SourceNumberDesign.HelpString=The structure '{'SNum'}' will be replaced by the system with the source number
ru.naumen.sd.bobjects.nomenclature.designs.YearDesign.HelpString=The structure '{'YYYY'}' will be replaced by the system with the current year
# SOAP-service
ru.naumen.soap.server.SoapServiceHelper.file_added=File was added
ru.naumen.soap.server.SoapServiceHelper.find_method_error=The number of returned objects exceeds maximum size {0}
ru.naumen.soap.server.SoapServiceHelper.get_method_error=Tag ''uuid'' contains file UUID but not object UUID
ru.naumen.soap.server.SoapServiceHelper.incorrect_request=Incorrect soap-request. Check your xml document
ru.naumen.soap.server.SoapServiceHelper.object_deleted=Object was deleted
ru.naumen.soap.server.SoapServiceHelper.object_edited=Object was edited
ru.naumen.soap.server.SoapServiceHelper.wrong_sign=Incorrect message sign
ruleVersion=Version: {0}
russianLang=Russian language
scRegistration=Request registration
scheduler=Task scheduler
scheduler.addRandomDelay=For {0} ({1}) start time randomization is enabled in {2} seconds
scheduler.cantDisable=Unable to disable trigger with name "{0}". Please, check name spelled correctly.
scheduler.cantEnable=Unable to enable trigger with name "{0}". Please, check name spelled correctly.
scheduler.concreteDateError=Trigger with id "{0}" is not concrete date trigger. Try to change period instead.
scheduler.concreteDateTriggerPrefix=Concrete date and time execution.
scheduler.daily=Daily.
scheduler.date=Next trigger execution date : {0}
scheduler.deleteSchedulerTaskError=An error occurred while deleting the scheduler task.
scheduler.deleteSchedulerTaskSuccess=Scheduler task deleted successfully.
scheduler.deletedMailsForTask=All unprocessed mails have been deleted for task {0}.
scheduler.forceRunAlreadyStarted=The scheduler task is already running. Please wait for the current execution cycle to complete.
scheduler.getStatusError=An error occurred while getting the status.
scheduler.unableStartTask=Unable to start the task (see application log at WARN level).
scheduler.interruptingError=An error occurred while interrupting the task with name "{0}".
scheduler.invalidDateError=Date can''t be in past. Please, correct the date.
scheduler.invalidPeriod=Period is invalid. Use daily/weekly/monthly/yearly.
scheduler.invalidStrategy=Strategy is invalid. Use from_start or from_last_execution.
scheduler.isEnabled=Trigger is enabled : {0}
scheduler.monthly=Monthly.
scheduler.period=Trigger period : {0}
scheduler.periodicTriggerError=Trigger with id "{0}" is not periodic. Try to set concrete date instead.
scheduler.periodicTriggerPrefix=Periodic rule.
scheduler.resumeSuccess=Scheduling manager successfully resumed.
scheduler.resumingError=An error occurred while scheduler resuming.
scheduler.successInterrupt=Job interruption was successful. Thread Death errors are expected.
scheduler.suspendSuccess=Scheduling manager successfully suspended. MissfireHandler errors are expected.
scheduler.suspendingError=An error occurred while scheduler suspending.
scheduler.triggerChanged=Trigger changed successfully.
scheduler.triggerDisabled=Trigger with name {0} successfully disabled.
scheduler.triggerEnabled=Trigger with name {0} successfully enabled.
scheduler.triggerIsConcreteDate=Trigger has a concrete date.
scheduler.triggerName=Trigger name : {0}
scheduler.unknownTypeOfTrigger=Unknown type of trigger.
scheduler.weekly=Weekly.
scheduler.yearly=Yearly.
schemaOptimizationProcessAlreadyRun=The optimization process has already started.
schemaOptimizationProcessNotFound=No optimization process found. Please contact technical support.
scparams=Request parameters
script=Script
scriptConditions.betweenConditionError=Operation "{0}". The first and last values of the range must be of the same type.
scriptConditions.compareConditionError=Operation "{0}". The comparable value must be a number or a date.
scriptConditions.inConditionError=Operation "{0}". List of arguments must be non-null and have at least one item.
scriptConditions.nullConditionError=Operation "{0}". The comparable value must be non-null.
scriptConditions.orConditionError=Operation "{0}". List of arguments must be non-null and have a size greater than one.
scriptPossibleValues.dispatchException=An error occurred while getting attribute values after filtration.
search=Search comments and files
searchSettings=Search
searchSettingsTitle=Common search settings
secGroups=Groups of user
secProfiles=Profiles
secRoles=Roles
security=User groups, roles, and profiles
security.errorWrongShowNewPermissionsParameters=Error! Wrong parameters:
securityPolicy=Security policy
securityPolicy.cannotChangePasswordYet=In accordance with the settings of the security policy, established by the system administrator, You will not be able to change the password before {0}.
securityPolicy.letters=abcdefghijklmnopqrstuvwxyzабвгдеёжзийклмнопрстуфхцчшщъыьэюя
securityPolicy.password.invalidPassword=Password is not satisfied with security policy:
securityPolicy.password.mustBeNew=password must not be the same as current password.
securityPolicy.password.mustContain=password must contain {0}.
securityPolicy.password.mustNotBeLogin=password must not be the same as login.
securityPolicy.password.shouldBeLongerThan=amount of characters in password must be at least {0}.
securityPolicy.password.editForbidden=Password change prohibited
securityPolicy.props.enabled=On
securityPolicy.props.mandatorySymbols=Required to use following characters types in password
securityPolicy.props.maxFailedTries=Maximum number of failed login attempts
securityPolicy.props.maxFailedTriesWithoutSuspesion=Number of failed login attempts without temporary locking account
securityPolicy.props.maxPasswordLifespan=Maximum password age (days)
securityPolicy.props.minPasswordLength=Minimum amount of characters in password
securityPolicy.props.minPasswordLifespan=Minimum password age (days)
securityPolicy.props.restrictCurrentPasswordAsNew=Forbid to use current password as new password
securityPolicy.props.restrictLoginAsPassword=Forbid to use login as new password
securityPolicy.props.suspensionTime=Time account lockout between failed login attempts (s)
securityPolicy.symbols.digits=digits
securityPolicy.symbols.lowercaseLetters=English or Russian uppercase letters
securityPolicy.symbols.specialSymbols=special characters
securityPolicy.symbols.uppercaseLetters=English or Russian lowercase letters
securityPolicy.wrongCurrentPassword=Invalid password. To change the password, you must enter the valid current password.
securityProfiles.webAdminInterfaceProfileTitle=Web Interface Administrator
selectCase=Select object type
selectContacts=Select contacts
#serviceCall.domain.xml
service.domain.AddIssueForClientDepartament=Add request for client-department
service.domain.AddIssueForClientTeam=Add request for client-team
service.domain.AddIsueForClientEmployee=Add request for client-employee
service.domain.AddObectNoun=Add object
service.domain.ChangeIssueBinding=Change request association
service.domain.ChangeMassMode=Change mass attribute
service.domain.IssueAuthor=Request author
service.domain.IssueContact=Request client
service.domain.IssueContactDepartmentEmployee=Employee of the department - client of request
service.domain.IssueResolvedEmpoyee=Employee resolved the request
service.domain.IssueResolvedTeamMember=Member of the team resolved the request
service.domain.OtherAttributes=Other attributes
serviceCall=Ticket
#serviceCall.newentryform.xml
serviceCall.newentryform.AddForm=Add form
serviceCall.newentryform.IssueTypeSelect=Select type of request
serviceCall.newentryform.ObjectTypeSelect=Select type of object
serviceCall.newentryform.SystemAttributes=System attributes
serviceCall.solvedError={1} must be the performer
#serviceCall.wf.xml
serviceCall.wf.ClientRequestRecorded=Client request registered in the system
serviceCall.wf.Closed=Closed
serviceCall.wf.ClosedBy=Closed by
serviceCall.wf.ClosedByEmployee=Closed by (employee)
serviceCall.wf.ClosedByTeam=Closed by (team)
serviceCall.wf.CodeOfClosure=Code of closure
serviceCall.wf.DateOfClosure=Date of closure
serviceCall.wf.IssueProcessingResumed=Request processing reopened
serviceCall.wf.IssueWasResolved=Request resolved
serviceCall.wf.ReceivedConfirmationOfIssueResolutionFromClient=Confirmation of the request execution from the client received
serviceCall.wf.Registered=Registered
serviceCall.wf.Reopened=Reopened
serviceCall.wf.Resolved=Resolved
serviceCall.wf.SolvedBy=Resolved by
#serviceCall.wfprofile.xml
serviceCall.wfprofile.1=Request related to the mass request
serviceCall.wfprofile.2=Profile of related workflows, intended to link mass and child requests
serviceCall.window.ChangeClientAndService=Change association
serviceCall.window.ChangeState=Change state
serviceCall.window.ChangeType=Change type
serviceCall.window.ChnageAssignee=Change responsible
serviceCall.window.General=General information
serviceCall.window.HistoryResponsibleAndState=History of responsible and state changes
serviceCall.window.MassMode=Mass operations
#serviceCall.window.xml
serviceCall.window.ObjectParameters=Object parameters
serviceCall.window.TimeMetrics=Time metrics
serviceCallValidationOperation.agsSettingYouShouldSelectAgreement="SLA" must be selected in the "SLA/Service" field.
serviceCallValidationOperation.agsSettingYouShouldSelectService="Service" must be selected in the "SLA/Service" field
serviceCallValidationOperation.scAssociationError=Request cannot be changed for the following reasons: 1. Required attributes not filled in: Scheduled time to close the request, Standard processing time/ Priority.
serviceCallValidationOperation.scAssociationPriorityError=Request cannot be changed for the following reasons: 1. Required attributes not filled in: Priority.
serviceCallValidationOperation.scAssociationResolutionTimeError=Request cannot be changed for the following reasons: 1. Required attributes not filled in: Standard processing time.
serviceCallValidationOperation.serviceAggrementError=The service ''{1}'' is not related to the SLA ''{0}''
serviceCallValidationOperation.serviceCallCaseError=Request type ''{1}'' does not correspond to the selected service ''{0}''
serviceCallValidationOperation.serviceMassProblemError=Request association cannot be changed. With the request related {0}: {1}
serviceCallValidationOperation.serviceWithoutAggrementError=In request the service ''{0}'' is specified but SLA is not filled in
serviceCallValidationOperation.slaveRemovedDifferFromMasterFromRemove=Request is slave and cannot be restored from the archive separatly from the mass request "{0}".
serviceCallValidationOperation.slaveRemovedDifferFromMasterToRemove=The request is a child request and cannot be restored from the archive separately from the mass request "{0}".
#ru.naumen.metainfo.server.impl.ObjectsExistsListener
serviceExistsListener.etc=etc.
serviceExistsListener.hasRelatedAttrs=Class is referenced by the attributes: {0}.
serviceExistsListener.serviceExists=Type is related to the following objects: {1}.
serviceExistsListener.usesInScripts=Type is used in the calculated attribute of the class {0}.
serviceTime.allExclusionsIgnored=<br>File does not contain new exclusions for this service class
serviceTime.dateYearValidationError=Exclusion date must be in the range of 02.01.1900 to 01.01.3000
serviceTime.editServiceTimeHasDraft=You cannot change service class ''{0}''. The service class already has a draft, you can make changes to it.
serviceTime.exclusionsLoadComplete=Import completed
serviceTime.ignoredEclusions=<br>Existing service class exclusions were ignored:
serviceTime.loadedExclusions=<br>Exclusions uploaded:
serviceTime.titleForLoadResult=ServiceCall {0}
serviceTimeApi.errorFolderServiceTime=You can not edit an item, it is a directory folder
serviceTimeApi.errorOldServiceTime=You can not edit ServiceTime class in the ''Old version'' state
services=Services
sessionTerminated=Account deleted. Please enter the login and password of an existing account to log in.
sessionTimedOut=Session is timed out or this account is used on another computer. Please enter the login and password to log in.
sessionChangedSuperUser=The account has been changed. To log in, enter your updated username and password.
setEmployeeLoginOperation.superuser=Superuser ''{0}''
setEmployeeLoginOperation.user=User
settingsSet=Set
settingsSetsConfigurationErrorMessage=Sets functionality cannot be enabled. To enable it, the Administration Profiles functionality must be enabled first.
shareViewAvailableFor.any=Any licensed user
shareViewAvailableFor.forUserGroups=Only for selected user groups
shareViewAvailableFor.superusers=Superuser only
shareViews=Shared views can be created by
show=Show
showBreadCrumb=Show "breadcrumbs"
showHomePage=Show "Set as home page" button
showLeftMenu=Left menu items
showLeftMenuSettings=Show left menu
showNestedInNested=Show in list objects, nested in nested
showTopMenu=Show top menu
signIn=Log in
silentmode.connection.denied=Could not connect to the server {0} because of silent mode is enabled
simpleSearchSettings.maxLength=Request length exceeds maximum size {0} symbols
slmService=SlmServices
#slmService.domain.xml
slmService.domain.ServiceCurator=Service curator
slmService.domain.ServiceCustomer=Service consumer
slmService.domain.ServiceProvider=Service provider
smpInterfaceIsForbidden=Unable to open system interface. You do not have a license with access to the system interface.
#smpSync
smpSync.eaUploadError=An error occurred while loading the built-in application "{0}": {1}
smpSync.eaUploadOk=App loaded successfully
smpSync.licenseUploadOk=License file uploaded successfully
smpSync.superUserWarning=Only the superuser can use these methods
smpSync.wrongContentType=Request has wrong Content-Type. It supposed to be "multipart/form-data"
smpsync.moduleTextIsNotAvailable=Module ''{0}'' text is not available
# SMS
sms.authorIsTooLarge="Author" field is too large (more than 20 bytes)!
sms.isTooLarge=SMS message size is too large (more than {0} characters)!
sms.notSent=SMS message send failed!
sms.sent=SMS message was sent!
smtLessByNum=''{0}'' {1} less
someFilesNotExists=Some files were not added to the form because they are missing in the system.
sortingCriteria=Search sorting criteria
spnegoAuthFailure=Kerberos/SPNEGO authorization failed
spnegoUnsupported=Kerberos/SPNEGO authentication not supported
string=String
structuredObjectsView=Structure
structuredObjectsViewItems.unableToAddNonUniqueCode=The item of structure with the code ''{0}'' cannot be added. The code must be unique.
structuredObjectsViews=Structures
structuredObjectsViews.hasReferenced=Used in structure ''{0}'', in items ''{1}''.
structuredObjectsViews.unableToAddNonUniqueCode=The structure with the code ''{0}'' cannot be added. The code must be unique.
structuredObjectsViews.useInAttribute=Structure is used in attributes {0}.
structuredObjectsViews.useInAttributeInCustomForm=Structure is used in parameters {0}.
structuredObjectsViews.useInContent=Structure is used in contents {0}.
structuredObjectsViews.useInContentTemplate=Structure is used in content template settings {0}.
structuredObjectsViews.useInMenuItem=Structure is used in menu item settings {0}.
structuredObjectsViews.useInViewForEditingAttribute=Structure is used in the view for editing attributes {0}.
structuredObjectsViews.useInViewForEditingAttributeInCustomForm=Structure is used in view for editing parameters of user event actions {0}.
structuredObjectsViews.itemCanNotBeDeleted=The structure element ''{0}'' cannot be deleted for the following reasons:
structuredObjectsViews.itemCanNotBeEdited=The structure element ''{0}'' cannot be modified for the following reasons:
structuredObjectsViews.itemLinkedWithObjectsOfClassViaAttributes=It links the structure ''{0}'' with objects of the class ''{1}'' via the attribute(s) {2}
systemAuthFailure=An error occurred while trying to authenticate, contact your administrator.
systemCatalogs=System catalogs
systemFileCanNotBeCopy=System files cannot be copied.
systemIcons=System icons
systemJMSQueues=System queues
systemName=System name
systemSettings=System settings
superUserAuthenticationException=Login using this account is not possible. A violation of the licensing policy was detected. Contact your system administrator.
tabBar=Tab bar
tabName=Tab''s name
tabOfBrowser=Tab of browser
tabProperties=Tab properties
tabs=Tabs
team=Team
#team.domain.xml
team.domain.CuratorDepartmentEmployee=Employee of the curating department
team.domain.HeadOfCuratorDepartment=Head of the curating department
team.domain.TeamLeader=Team leader
team.domain.TeamMember=Team member
teams=Teams
tempFileCanNotBeCopy=System files cannot be copied.
templates=Templates
text=Text
themeInterfaceDisplay=The theme for the display interface
themeInterfaceSettings=The theme for the interface settings
themeParamsCyclicDependency=Cyclic dependency found in parameters: {0}.
themeParamsInvalidNames=File contains invalid parameters: {0}
themeParamsInvalidValues=File contains invalid parameters that have invalid values: {0}
timer.active=Active
timer.exceed=Expired
timer.expired=expired
timer.notExpired=not expired
timer.notStarted=Awaiting
timer.paused=Paused
timer.stoped=Stopped
#ru.naumen.core.server.timer.TimerUtils.getTimerContext()
timerUtils.attrMustBeFilled=Attribute ''{0}'' must be filled in. It is used in calculation: ''{1}''
timerUtils.attrsMustBeFilled=Attributes ''{0}'' must be filled in. They are used in calculation: ''{1}''
timers=Timers
title=Title
titleAndCode=''{0}'' ({1})
toggleOff=Disable
toggleOn=Enable
toolPanel=Action bar
topMenu=Top menu
transaction.timeout=Transaction timed out
tryAgain=Try again
typeSettings=Type settings
#ru.naumen.core.server.license.LicensingFormatter
ukrainianLang=Ukrainian language
unlicensedUser=Unlicensed user
uploadLicence=Uploading a license
uploadMetainfo=Upload metainformation
userArea=User area of the quick access panel
userBlocked=You cannot log in. Your account has been locked in system, contact your administrator.
userBlockedWhenChangePassError=Unable to change your password. Your account is temporarily blocked in the app. Try changing your password again later.
userCatalogs=User catalogs
userEvent.actionStarted=Action "{0}" has been started
userEvent.cannotDeleted=Event action "{0}" cannot be deleted for the following reasons:
userEvent.changeFqnsDenied=Set of object cases of event action "{0}" cannot be changed. This event action is used in the following controls: {1}
userEvent.changeTypeDenied=Event type of action "{0}" is not editable. This event action is used in the following controls: {1}
userEvent.error=An error occurred during execution of the action "{0}": {1}
userEvent.eventIsNotApplicable=This action cannot be performed for selected objects.
userEvent.executionDenied={0} cannot be executed for the following objects: {1}
userEvent.executionErrors.cannotApplicableForThisObjectType=it is not applicable to objects of this type
userEvent.executionErrors.cannotExecuteByReason=The action cannot be performed due to: {0}
userEvent.executionErrors.head=Action has not executed:
userEvent.executionErrors.log.warningForApplications={0}: action {1} not performed for objects: {2} user has no rights to perform the action.
userEvent.executionErrors.noSpecifiedActionOrDisabled=there is no specified action in the system or it is disabled by administrator
userEvent.executionErrors.permissionForEvent=you do not have permission to perform the action.
userEvent.info=Information
userEvent.isUsing=- This event action is used in the following controls:\n{0}
userEvent.nothingSelected=At least one object must be selected.
userIcons=User icons
userJMSQueues=User queues
userSuspended=You cannot log in. Too many incorrect login attempts . Please, try again after {0} s.
utils.find.sp.offsetError=There is no way to use offset() method without setting limit().
validateAndFixChangeStateMarkers.wasRemoved=Transition ''{0}'' - ''{1}'' deleted from permission markers of ''Edit state'' group
validateWorkflowWithoutState.delete=State ''{0}'' cannot be deleted. It is referenced by profiles of related workflows: {1}
validateWorkflowWithoutState.description=Status ''{0}'' is referenced by the profiles of related workflows: {1}
validateWorkflowWithoutState.disable=State ''{0}'' cannot be disabled. It is referenced by profiles of related workflows: {1}
validation.image.fileIsNotImage=Content of the file "{0}" is not an image. Supported image files formats: jpg, jpeg, gif, png, bmp, svg, svgz.
validation.image.fileIsNotVectorImage=Content of the file "{0}" is not vector image. Supported image files formats: {1}.
validation.image.invalidVectorIcon=The file contains redundant tags. To display the SVG file correctly in the interface, it must contains tags from base set: {0}.
validation.integerNotNegative=Value of attribute "{0}" must be a non-negative integer.
value=Value
valueMapImport.configurationAlreadyExists=Configuration with the code ''{0}'' already exists. You should save the new correspondence table with another code.
valueMapImport.existingConfigurationIsNotValueMap=Configuration with the code ''{0}'' already exists. To export or import the correspondence table with the code ''{0}'', you should save the new configuration file with another code.
valueMapImport.invalidArchiveContentMulti=Correspondence table can not be loaded. Archive must contain exactly one pair of files: XML with import configuration and CSV with correspondence table rows data. File names must be the same as code of imported table.
valueMapImport.invalidArchiveContentSingle=Correspondence table can not be loaded. Archive must contain exactly one pair of files: XML with import configuration and CSV with correspondence table rows data.
valueMapImport.invalidFileFormat=Correspondence table can not be loaded. File format is invalid.
valueMapImport.rowError=An error occurred during row {0} import: {1}.
valueMapImport.uploadedConfigurationIsNotValueMap=Uploaded configuration is not import configuration of correspondence table with code ''{0}''.
valueMapImport.valueMapNotFound=Configuration with the code ''{0}'' is not found.
valueMapImport.valueMapRemoved=Correspondence table ''{0}'' is archived, so it can not be imported.
valueMapIncorrectLinkedClassAndClassesValues=cannot be created: attribute''linkedClasses'' has value ''{0}'' which is not parent for cases of ''linkedClasses'' attribute (''Objects''): ''{1}''.
valueMapRowIncorrectAttributeValue=The field ''{0}'' contains incorrect value ''{1}''.
valueUnavailable=Value is not available
version=Version
visibility=Visibility
websocket.canNotAddHandlers=An error occurred while adding new mail handlers
websocket.configCreationFailed=An error occurred while configuration creation
websocket.configCreationSuccess=Successfully created config with code "{0}"
websocket.connectionFailed=Can''t connect to websocket with code "{0}"
websocket.moduleOrMethodNotFound=Error while connection establishment to config with code "{0}". Module or method with this name not found.
websocket.noWebsocketUrlByCode=Can''t get websocket url by code "{0}"
websocket.notDeletedConnectionIsActive=Can''t delete websocket connection configuration with code "{0}", because connection is active.
websocket.notInModule=Connection using this signature is only available if methods for this configuration are in the module
websocket.notSendedConnectionIsNotActive=Message not send. Can''t found active connection with code "{0}".
websocket.notSetInitializerConnectionIsActive=Can''t set initializer on active connection with code "{0}"
websocket.reloadError=Error while processing reload request
wfProfiles=Referenced by profiles of related workflows
#*.window.xml
window.Add=Add
window.AddIssue=Add request
window.Archive=Archive
window.ChangeType=Change type
window.Copy=Copy
window.Edit=Edit
window.Move=Move
window.ObjectCard=Object card
window.Remove=Delete
window.Restore=Restore from archive
window.SystemAttributes=System attributes
workflow=Workflow
workflow.editAttrInStateForbidden=Attribute "{0}" of the object "{1}" is not editable in state "{2}".
workflowValidation.closedTransitionsError=For the "{0}" there is no entry to the status ''{1}''
workflowValidation.registeredTransitionsError=For the "{0}" exit from the state ''{1}'' does not exist
workflowValidation.stateIncomingTransitionsError=For the ''{0}'' there are exits from the state ''{1}'', but there is no entry
workflowValidation.stateOutgoingTransitionsError=For the ''{0}'' there are entries to the state ''{1}'', but there is no exit
yes=yes
you.wrote=you wrote:
importMetainfo.loadingAdvListSettings.AttentionMissingSecGroup=The metainformation was loaded with comments. When loading meta information, the user group with the code ''{0}'' was removed from the list of groups that are available to create common views of complex lists, because the user group was not found
importMetainfo.DeletingEventStorageRule=['{}'/'{}'] Deleting event log storage rule '{}'
commonSearchSettings.analyzerIsUsed=The analyzer type "{0}" cannot be deleted.\nThe analyzer type is used in the object search settings {1}
importMetainfo.loadingEventStorageRulesDone=Event log storage rules loaded: '{}'
importMetainfo.loadingEventCleanerJobSettingsStart=Start loading the settings of the event log cleanup task
importMetainfo.loadingEventStorageRulesStart=Start loading event log storage rules
importMetainfo.loadingEventStorageRule=['{}'/'{}'] Loading event log storage rule: '{}'
importMetainfo.loadingEventCleanerJobSettingsDone=The settings for the event log cleanup task are loaded
