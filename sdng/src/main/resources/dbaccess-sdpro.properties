# Event actions
eventActions.groupNumber=100
eventActions.idle.limits.disabled=false
eventActions.notifications.threadNumber=2-2
eventActions.threadNumber=4-4
jwt.authModule.code=portal_jwtAuthUtils
ru.naumen.eventActions.alwaysAsyncExecution.enabled=true
# Deferred sender for notifications
eventActions.notifications.sender.deferred=true
mail.dispatcher.pool.threads=4
mail.dispatcher.strategy=bulk
# Mail cleaner
ru.naumen.mail.batch_size=1000
ru.naumen.mail.cleaner.allowed_hours_begin=22
ru.naumen.mail.cleaner.allowed_hours_end=6
ru.naumen.mail.error_age=60
ru.naumen.mail.issues_age=60
ru.naumen.mail.log_age=60
ru.naumen.mail.success_age=60
# History action conditions error cleaner
history.actionConditionsError.daysToExpire=0
history.cleaner.allowed_hours_begin=22
history.cleaner.allowed_hours_end=6
history.cleaner.batch_size=1000
# Advlist export
ru.naumen.advlist.export.size=10000
# Upload file limits
upload.file.max.size.bytes=52428800
upload.files.group.max.size.bytes=52428800
upload.files.webserver.text.error.size.bytes=52428800
# Tree search settings
ru.naumen.tree.search.string.min.length=3
ru.naumen.tree.search.string.max.length=50
# Settings timeout error in dispatch
ru.naumen.form.lock.blockingButtonsTimeout=60
ru.naumen.form.lock.blockingButtonsLogging=false
ru.naumen.gwt.rpc.ignore-request-timeout=false
# Reports max table size
reports.instances.max-table-size=52428800
# Login case sensitive
login.casesensitive=false
# Gateway settings
eventActions.integration.high.threadNumber=10-10
eventActions.integration.threadNumber=10-10
ru.naumen.gatewayIntegration.enabled=true
# REST API throttling configuration settings
ru.naumen.rest.denied=find
# Search settings
indexing.exceptions.forbiddenClasses=log,mail_sys,mailLogRecord,totalValue,rating,vote
indexing.priority.preferNewObjects=true
ru.naumen.fts.server.lucene.reindex.usedb=false
ru.naumen.indexing.backpressure.ignore=true
ru.naumen.indexing.batch.size=5000
ru.naumen.linkedobjects.indexing=true
ru.naumen.linkedobjects.indexing.threadNumber=1
ru.naumen.linkedobjects.cache.enabled=true
ru.naumen.reindexing.batch.size=5000
# Hibernate cache settings
hibernate.cache.collection.lifespan=1800000
hibernate.cache.collection.max_entries=20000
hibernate.cache.collection.maxidle=1200000
hibernate.cache.entity.lifespan=1800000
hibernate.cache.entity.max_entries=20000
hibernate.cache.entity.maxidle=1200000
hibernate.cache.query.lifespan=1800000
hibernate.cache.query.max_entries=20000
hibernate.cache.query.maxidle=1200000
ru.naumen.cluster.hibernate.cache.invalidation.all.sync=true
# Db settings
db.constraints.validateForeignKey.concurrently=true
db.indexes.createIndexes.concurrently=true
db.startup.drop_unnecessary_tables=false
# Watchdog settings
quartz.autoRecovery=false
quartz.watchdog.enabled=false
quartz.watchdog.maxStuckTime=4
quartz.watchdog.sleepTime=300
# Async servlets settings
ru.naumen.async.queue.appsrestapi.core.size=10
ru.naumen.async.queue.restApi.core.size=30
ru.naumen.async.servlets.enabled=true
# Throttler settings
ru.naumen.cluster.metainfoVersionCheck.jmsRateLimit=1
ru.naumen.cluster.metainfoVersionCheck.mobileRateLimit=1
ru.naumen.cluster.metainfoVersionCheck.quartzRateLimit=1
ru.naumen.cluster.metainfoVersionCheck.requestRateLimit=1
ru.naumen.cluster.split.check.timeSleepForRecheck=100
# Artemis settings
ru.naumen.jms.artemis.blockOnDurableSend=false
ru.naumen.jms.artemis.pooled.connectionFactory.enable=true
# Embedded application settings
ru.naumen.embeddedapplication.applicationsAvailableOnModalForm=dynamicFields
ru.naumen.embeddedapplication.showApplicationsInReadOnly=false
# Counting cache settings
ru.naumen.tab.counting.minCacheOnTabs=10
ru.naumen.tab.counting.timeLiveCacheOnTabs=0
# Modules compilation settings
ru.naumen.script_modules.compilation.mode=all
ru.naumen.script_modules.needBreakLinksWithOldClassLoader=true
# JMS settings
ru.naumen.jms.autoRecovery.enabled=true
ru.naumen.jms.autoRecovery.interval=1800
ru.naumen.jms.autoRecovery.period=900
ru.naumen.jms.autoRecovery.queues=Queue.EventAction,Queue.EventAction.Notifications,Queue.PlannedEvent,Queue.External.EventAction,Queue.UserEventAction,Queue.EventAction.Escalations
ru.naumen.jms.listeners.use.external.txManager=false
ru.naumen.jms.pubSubDomain.enabled=true
# Others settings
copy.mass.operation.restrictions.params={'serviceCall': {'workRecord'}}
eventActions.deviationPeriod=480
jwt.cookie.name=AccessToken
groovy.use.classvalue=false
ru.naumen.script_modules.useInvokeDynamic=false
mass.edit.max.cpu.threads=8
ndap.scheduler.NDAPServerCheckerJob.period=0
ru.naumen.bcp.bolinks.threshold_sql=0
ru.naumen.big_in_constructed_sql_enable=true
ru.naumen.cache.scripts.size=2500
ru.naumen.cacheAllCachedJsScript.enable=true
ru.naumen.cluster.maildispatcher.cron=0 */2 * ? * *
ru.naumen.hibernate.persistence_context.limit=150000
ru.naumen.core.server.excludeAllRelatedAttrs=true
ru.naumen.core.userEvent.reloadSubjectsOnFire=true
ru.naumen.dynaform.client.useDtoObjectsWithoutLinkAttrs=true
ru.naumen.inDirectAndBackLinkFilter.useSubCriterias=true
ru.naumen.in_with_id_storage_activate_border=3
ru.naumen.localstorage.clearAfterChangeVersion=true
ru.naumen.log.min.duration=1000
ru.naumen.lucene.backup_interval_minutes=**********
ru.naumen.mail.validation.domains=local
ru.naumen.metainfo.browser-cache.enable=true
ru.naumen.metainfo.l2cache.enabled=true
ru.naumen.metainfo.server.spi.MetainfoExportSource.useMetainfoFiltersUuidsTransformer=true
ru.naumen.naming.generator.cached=false
ru.naumen.noUpdateClassTitlesOnMetainfoLoad=false
ru.naumen.readonly.contents=hardwareList,softwareList,contractsList,licenseList,listConfigItems
ru.naumen.schema.ddl.timeout=120000
# Включить использование логгера класса скрипта вместо логгера общего класса Script
ru.naumen.scriptClassLoggingEnabled=true
ru.naumen.security.l2cache.enabled=true
ru.naumen.security.session.cookie.path=/
smia.modelCache.maxSize=3
use.Email.allow.uppercase=true
# SSO settings
ru.naumen.core.authentication.external.clientCustomErrorPageFileName=
ru.naumen.core.authentication.external.customErrorPageContent=
ru.naumen.core.authentication.external.useCustomErrorPage=true
