<infinispan xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
			xsi:schemaLocation="urn:infinispan:config:15.1 http://www.infinispan.org/schemas/infinispan-config-15.1.xsd"
			xmlns="urn:infinispan:config:15.1">

	<cache-container default-cache="default">
		<!-- отключаем попытки создать репозиторий метрик в инфиниспане -->
		<metrics gauges="false"/>

		<serialization marshaller="org.infinispan.commons.marshall.JavaSerializationMarshaller">
			<allow-list>
				<class>java.util.ArrayList</class>
				<class>java.util.HashMap</class>
				<class>java.util.HashSet</class>
				<regex>ru.naumen.*</regex>
			</allow-list>
		</serialization>

		<!-- транзакционные кэши  -->
		<!-- при создании нового либо его объявить здесь, либо использовать ISCacheAdapter -->

		<local-cache-configuration name="batch-template">
			<locking isolation="READ_COMMITTED"/>
			<transaction mode="BATCH"
						 transaction-manager-lookup="org.infinispan.transaction.lookup.JBossStandaloneJTAManagerLookup"/>
		</local-cache-configuration>

		<local-cache name="linkedObjectCache" configuration="batch-template"/>
		<local-cache name="scriptCodeToScriptCache" configuration="batch-template"/>
		<local-cache name="libraryNameToScriptCodeCache" configuration="batch-template"/>
		<local-cache name="moduleCodeToModuleCache" configuration="batch-template"/>
		<local-cache name="libraryNameToModuleCodeCache" configuration="batch-template"/>
		<local-cache name="roleCodeToScriptCache" configuration="batch-template"/>
		<local-cache name="libraryNameToRoleCodeCache" configuration="batch-template"/>
		<local-cache name="hierarchyExportCache" configuration="batch-template"/>
		<local-cache name="advlistexportCache" configuration="batch-template"/>

		<!-- Кеш, для отслеживания ресурсов, указанных в RichText
		не должен чистить сам себя, этим занимается rtfServletCache
		-->
		<local-cache name="rtfServletCacheForInnerSource" configuration="batch-template"/>
		<local-cache name="reportTemplatesCache" configuration="batch-template">
			<expiration lifespan="3600000"/>
		</local-cache>

		<local-cache name="default" configuration="batch-template">
			<memory max-count="10000" when-full="REMOVE"/>
		</local-cache>

		<!-- не транзакционные кэши -->

		<local-cache-configuration name="none-eviction-template">
			<expiration lifespan="600000"/>
			<memory when-full="NONE"/>
		</local-cache-configuration>

		<!-- Кэш виджетов выбора соглашения/услуги на 10 минут -->
		<local-cache name="agreementServiceCache" configuration="none-eviction-template"/>
		<!-- Кэш фильтрованных плоских списков на 10 минут -->
		<local-cache name="filteredListCache" configuration="none-eviction-template"/>

		<local-cache name="oneHourExpiration">
			<expiration lifespan="3600000"/>
			<memory when-full="NONE"/>
		</local-cache>

		<!-- ************************************** -->
		<!-- Corresponds to @Cacheable -->
		<!-- ************************************** -->
		<local-cache name="UserEventsServiceCache"/>
		<local-cache name="SettingsStorageImplCache"/>
		<local-cache name="ThemePropertiesCache"/>

	</cache-container>
</infinispan>