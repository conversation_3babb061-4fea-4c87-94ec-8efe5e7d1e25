<?xml version="1.0" encoding="UTF-8"?>
<licensingPolicy xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                 xmlns="http://www.naumen.ru/licensingPolicy"
                 xsi:schemaLocation="http://www.naumen.ru/licensingPolicy xsd/licensingPolicy.xsd ">
    <metaClasses>
        <metaClass>
            <!--Основной класс-->
            <fqn>
                <id>abstractBO</id>
            </fqn>
            <systemAttributes>
                <attributes>
                    <default>
                        <viewable>true</viewable>
                        <editable>false</editable>
                    </default>
                    <attribute>
                        <code>lastModifiedDate</code>
                        <viewable>true</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>creationDate</code>
                        <viewable>true</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>removalDate</code>
                        <viewable>true</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>author</code>
                        <viewable>true</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>stateStartTime</code>
                        <viewable>true</viewable>
                    </attribute>
                    <attribute>
                        <code>responsible</code>
                        <viewable>true</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>responsibleEmployee</code>
                        <viewable>true</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>responsibleTeam</code>
                        <viewable>true</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>responsibleStartTime</code>
                        <viewable>true</viewable>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>folders</code>
                        <viewable>true</viewable>
                        <editable>false</editable>
                    </attribute>
                </attributes>
            </systemAttributes>
            <userAttributes>
                <attributeTypes>
                    <attributeType>
                        <type>double</type>
                        <viewable>true</viewable>
                        <editable>true</editable>
                    </attributeType>
                    <attributeType>
                        <type>hyperlink</type>
                        <viewable>true</viewable>
                        <editable>true</editable>
                    </attributeType>
                    <attributeType>
                        <type>caseList</type>
                        <viewable>true</viewable>
                        <editable>true</editable>
                    </attributeType>
                    <attributeType>
                        <type>richtext</type>
                        <viewable>true</viewable>
                        <editable>true</editable>
                    </attributeType>
                    <attributeType>
                        <type>sourceCode</type>
                        <viewable>true</viewable>
                        <editable>true</editable>
                    </attributeType>
                    <attributeType>
                        <type>file</type>
                        <viewable>true</viewable>
                        <editable>true</editable>
                    </attributeType>
                </attributeTypes>
            </userAttributes>
        </metaClass>
        <metaClass>
            <!--Соглашение-->
            <fqn>
                <id>agreement</id>
            </fqn>
            <systemAttributes>
                <attributes>
                    <attribute>
                        <code>recipients</code>
                        <viewable>true</viewable>
                    </attribute>
                    <attribute>
                        <code>recipientsOU</code>
                        <viewable>true</viewable>
                    </attribute>
                    <attribute>
                        <code>recipientTeam</code>
                        <viewable>true</viewable>
                    </attribute>
                    <attribute>
                        <code>supplier</code>
                        <viewable>true</viewable>
                    </attribute>
                    <attribute>
                        <code>inventoryNumber</code>
                        <viewable>true</viewable>
                    </attribute>
                    <attribute>
                        <code>supplierEmpoyee</code>
                        <viewable>true</viewable>
                    </attribute>
                    <attribute>
                        <code>supplierTeam</code>
                        <viewable>true</viewable>
                    </attribute>
                    <attribute>
                        <code>priorityRule</code>
                        <viewable>true</viewable>
                    </attribute>
                    <attribute>
                        <code>resolutionTimeRule</code>
                        <viewable>true</viewable>
                    </attribute>
                </attributes>
            </systemAttributes>
        </metaClass>
        <metaClass>
            <!--Сотрудник-->
            <fqn>
                <id>employee</id>
            </fqn>
            <systemAttributes>
                <attributes>
                    <attribute>
                        <code>performer</code>
                        <viewable>true</viewable>
                    </attribute>
                    <attribute>
                        <code>license</code>
                        <viewable>true</viewable>
                    </attribute>
                    <attribute>
                        <code>employeeSecGroups</code>
                        <viewable>true</viewable>
                    </attribute>
                    <attribute>
                        <code>teams</code>
                        <viewable>true</viewable>
                    </attribute>
                </attributes>
            </systemAttributes>
        </metaClass>
        <metaClass>
            <!--Запрос-->
            <fqn>
                <id>serviceCall</id>
            </fqn>
            <systemAttributes>
                <attributes>
                    <default>
                        <editable>true</editable>
                    </default>
                    <attribute>
                        <code>clientLinkName</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>client</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>solvedBy</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>closedBy</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>clientOU</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>clientTeam</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>clientEmployee</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>deadLineTime</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>startTime</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>requestDate</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>massProblem</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>masterMassProblem</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>massProblemSlaves</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>solvedByTeam</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>solvedByEmployee</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>closedByTeam</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>closedByEmployee</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>impact</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>timeZone</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>priority</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>categories</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>resolutionTime</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>serviceTime</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>totalTimeTimer</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>processingTimeTimer</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>timeAllowanceTimer</code>
                        <editable>false</editable>
                    </attribute>
                    <attribute>
                        <code>wfProfile</code>
                        <editable>false</editable>
                    </attribute>
                </attributes>
            </systemAttributes>
            <userAttributes>
                <attributes>
                    <default>
                        <viewable>true</viewable>
                        <editable>true</editable>
                    </default>
                </attributes>
            </userAttributes>
        </metaClass>
        <metaClass>
            <!--Услуга-->
            <fqn>
                <id>slmService</id>
            </fqn>
            <systemAttributes>
                <attributes>
                    <attribute>
                        <code>inventoryNumber</code>
                    </attribute>
                    <attribute>
                        <code>callCases</code>
                    </attribute>
                </attributes>
            </systemAttributes>
        </metaClass>
    </metaClasses>
</licensingPolicy>
