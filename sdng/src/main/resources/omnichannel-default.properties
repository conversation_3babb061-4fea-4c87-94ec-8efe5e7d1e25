# Cluster
#################################################################################
# Разрешает ноде подключение к шлюзу. Такая нода в кластере должна быть единственной.
ru.naumen.omnichannel.cluster.mainNode.enabled=false

# Gateway MessageBox
#################################################################################
# Количество потоков для обработки входящих сообщений шлюза MessageBox
ru.naumen.omnichannel.messageBox.connection.poolSize=1
# Таймаут ожидания входящего трафика шлюза MessageBox (в сек). Определяет промежуток времени, по истечении которого
# необходимо разорвать соединение, в случае отсутствия входящих данных. Необходим для проверки связанности соединения со шлюзом.
ru.naumen.omnichannel.messageBox.connection.readTimeout=55
# Таймаут подключения к файловому хранилищу шлюза MessageBox (в сек)
ru.naumen.omnichannel.messageBox.fileStorage.connectionTimeout=30
# Количество потоков для отложенной загрузки файлов шлюза MessageBox
ru.naumen.omnichannel.messageBox.fileStorage.listeners.concurrency=1
# Максимальный размер автоматического скачивания файлов при поступлении сообщения (в байтах).
# При значении параметра равном -1 файлы будут скачиваться без ограничений.
ru.naumen.omnichannel.messageBox.fileStorage.maxFileSize=5242880
# Таймаут ожидания ответа от сервера после выполнения запроса шлюза MessageBox (в сек)
ru.naumen.omnichannel.messageBox.fileStorage.socketTimeout=60

# Gateway OmniGate
#################################################################################
# Таймаут подключения к шлюзу OmniGate (в сек)
ru.naumen.omnichannel.omniGate.connectionTimeout=30
# Кол-во сообщений которое может быть получено за раз от шлюза OmniGate
ru.naumen.omnichannel.omniGate.messages.limit=100
# Таймаут ожидания сообщений от шлюза OmniGate (в сек). Должен быть меньше чем ru.naumen.omnichannel.omniGate.readTimeout.
ru.naumen.omnichannel.omniGate.messages.timeout=50
# Таймаут ожидания ответа на запрос от шлюза OmniGate (в сек)
ru.naumen.omnichannel.omniGate.readTimeout=60

# General
#################################################################################
# Количество потоков для обработки исходящих сообщений
ru.naumen.omnichannel.outgoingMessage.listeners.concurrency=1
# Максимальное количество групп на которые будут разделены исходящие сообщения. Параметр необходим для балансировки
# обработчиков очереди исходящих сообщений, а также для последовательной отправки сообщений.
ru.naumen.omnichannel.outgoingMessage.listeners.groupNumber=1
