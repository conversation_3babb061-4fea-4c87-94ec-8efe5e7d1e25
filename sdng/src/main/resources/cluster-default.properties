# Автоматически удалять таблицы, которые не используются в приложении
db.startup.drop_unnecessary_tables=
#################################################################################
# проверять ли активность кластера при подключении (поможет при остановке кластера через kill -9, но увеличит время старта)
jgroups.check.availability.before.connect=true
# Использовать внешний менеджер транзакций для слушателей JMS-сообщений
#JDBC_PING настройки
jgroups.jdbcping.initialize.oracle=declare begin execute immediate 'CREATE TABLE JGROUPS (address varchar(200) NOT NULL, name varchar(200), cluster_name varchar(200) NOT NULL, ip varchar(200) NOT NULL, coord number(1,0) check (coord in (0,1)), check_date timestamp WITH TIME ZONE NOT NULL, constraint PK_JGROUPS PRIMARY KEY (address, cluster_name))'; exception when others then if SQLCODE = -955 then null; else raise; end if; end;
jgroups.jdbcping.insert.single.oracle=INSERT INTO JGROUPS (address, name, cluster_name, ip, coord, check_date) values (?, ?, ?, ?, ?, SYSTIMESTAMP)
jgroups.jdbcping.upsert.single.oracle=MERGE INTO JGROUPS t USING (SELECT ? AS address , ? AS name, ? AS cluster_name, ? AS ip, ? AS coord, SYSTIMESTAMP AS check_date FROM dual) s \
ON (t.address = s.address AND t.cluster_name = s.cluster_name) \
WHEN MATCHED THEN UPDATE SET t.check_date = s.check_date \
WHEN NOT MATCHED THEN INSERT (t.address, t.name, t.cluster_name, t.ip, t.coord, t.check_date) VALUES (s.address, s.name, s.cluster_name, s.ip, s.coord, s.check_date)
jgroups.jdbcping.initialize.postgresql=CREATE TABLE IF NOT EXISTS JGROUPS (address varchar(200) NOT NULL, name varchar(200), cluster_name varchar(200) NOT NULL, ip varchar(200) NOT NULL, coord boolean, check_date timestamp WITH TIME ZONE NOT NULL, constraint PK_JGROUPS PRIMARY KEY (address, cluster_name))
jgroups.jdbcping.insert.single.postgresql=INSERT INTO JGROUPS values (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
jgroups.jdbcping.upsert.single.postgresql=INSERT INTO JGROUPS values (?, ?, ?, ?, ?, CURRENT_TIMESTAMP) ON CONFLICT (address, cluster_name) DO UPDATE SET check_date=CURRENT_TIMESTAMP
jgroups.jdbcping.initialize.mssql=IF OBJECT_ID('JGROUPS', 'U') IS NULL CREATE TABLE JGROUPS (address varchar(200) NOT NULL, name varchar(200), cluster_name varchar(200) NOT NULL, ip varchar(200) NOT NULL, coord BIT, check_date datetimeoffset NOT NULL, constraint PK_JGROUPS PRIMARY KEY (address, cluster_name))
jgroups.jdbcping.insert.single.mssql=INSERT INTO JGROUPS (address, name, cluster_name, ip, coord, check_date) values (?, ?, ?, ?, ?, SYSDATETIMEOFFSET())
jgroups.jdbcping.upsert.single.mssql=MERGE JGROUPS AS target USING (SELECT ? AS address, ? AS name, ? AS cluster_name, ? AS ip, ? AS coord, CURRENT_TIMESTAMP AS check_date) AS source \
(address, name, cluster_name, ip, coord, check_date) \
ON (target.address = source.address AND target.cluster_name = source.cluster_name) \
WHEN MATCHED THEN UPDATE SET target.check_date = source.check_date \
WHEN NOT MATCHED THEN INSERT (address, name, cluster_name, ip, coord, check_date) VALUES (source.address,source.name,source.cluster_name,source.ip,source.coord,source.check_date);
jgroups.jdbcping.initialize.sql=
jgroups.jdbcping.insert.single.sql=
jgroups.jdbcping.delete.single.sql=DELETE FROM JGROUPS WHERE address=?
jgroups.jdbcping.clear.sql=DELETE from JGROUPS WHERE cluster_name=?
jgroups.jdbcping.select.all.pingdata.sql=SELECT address, name, ip, coord FROM JGROUPS WHERE cluster_name=?
# перезаписывать ли информацию о нодах во время выполнения процедуры валидности состава кластера
# нужно в гео/серверораспределенных кластерах, когда работа на одном сервере выставлять в false
jgroups.jdbcping.write.data.on.find=true

# настройки протокола KUBE_PING
# версия API
jgroups.kubeping.apiVersion=v1
# таймаут подключения к kubernetes
jgroups.kubeping.connectTimeout=5000
# селектор подов SMP кластера
jgroups.kubeping.labels=
# пространство имен
jgroups.kubeping.namespace=default
# кол-во попыток подключений к kubernetes
jgroups.kubeping.operationAttempts=3
# ожидание между попытками подключений в мс.
jgroups.kubeping.operationSleep=1000
# http протокол: http/https
jgroups.kubeping.protocol=https
# таймаут ожидания получения ответа от kubernetes в мс.
jgroups.kubeping.readTimeout=30000

# протокол обнаружения jgroups (JDBC/KUBE)
jgroups.ping_protocol=JDBC
# время в миллисекундах, на которое заснет нода перед подключением
jgroups.sleep.time.before.connect=65000

# урл подключения к бд, если необоходимо чтобы запросы выполнялись в отличную от бд, на которой поднята нода
ru.naumen.db.cluster.jdbc.url=
# логин подключения к бд, если необоходимо чтобы запросы выполнялись в отличную от бд, на которой поднята нода
ru.naumen.db.cluster.jdbc.user=
# пароль подключения к бд, если необоходимо чтобы запросы выполнялись в отличную от бд, на которой поднята нода
ru.naumen.db.cluster.jdbc.password=
# зашифрованный пароль подключения к бд, если необоходимо чтобы запросы выполнялись в отличную от бд, на которой поднята нода
ru.naumen.db.cluster.jdbc.password.enc=

# включена проверка на запрет параллельного запуска процесса синхронизации метаинформации на ноде
ru.naumen.cluster.api.reload.check.enable=true
# Интервал (в милисекундах), через которое сработает автоматическое обновление метаинфорамации в кластере, после того, как нода поймет, что она изменилась
ru.naumen.cluster.autoReload.interval=120000
# Асинхронная репликация сессий
ru.naumen.cluster.bus.invalidation.async.sendSession.enabled=false
ru.naumen.cluster.bus.invalidation.response.timeout=5000
#список регионов, что могут синхронизироваться асинхронно без остановки фоновых процессов
ru.naumen.cluster.customNonVersionedMetainfoRegions=
# Отправлять кластерные сообщения членам кластера, находящимся в процессе запуска приложения. Стоит использовать только для KUBE_PING протокола
ru.naumen.cluster.event.sendToNotStartedNodes=true
# Размер пула на обработку событий инвалидации
ru.naumen.cluster.invalidateEvent.receivePoolSize=10
# Размер очереди пула на отправку событий инвалидации
ru.naumen.cluster.invalidateEvent.sendPoolQueueLength=1000
# Размер пула на отправку событий инвалиидации
ru.naumen.cluster.invalidateEvent.sendPoolSize=10
# время (в милисекундах), в течении которого персистентная кластерная блокировка считается живой. По истечению при проверке будет снята.
ru.naumen.cluster.lock.timeout=600000
# период пробуждения задачи по отправке почты в кластере
ru.naumen.cluster.maildispatcher.cron=0 */10 * ? * *

# количество запросов в секунду на проверку изменения версии метаинформации в кластере для очередей jms
# (проверка работает, когда происходит забор сообщения из очереди)
ru.naumen.cluster.metainfoVersionCheck.jmsRateLimit=-1
# количество запросов в секунду на проверку изменения версии метаинформации в кластере для планировщика задач
# (проверка работает, когда стартует задача планировщика)
ru.naumen.cluster.metainfoVersionCheck.quartzRateLimit=-1
# количество запросов в секунду на проверку изменения версии метаинформации в кластере для мобильного клиента
ru.naumen.cluster.metainfoVersionCheck.mobileRateLimit=1
# количество запросов в секунду на проверку изменения версии метаинформации в кластере для рестов/оператора
ru.naumen.cluster.metainfoVersionCheck.requestRateLimit=1

# роль ноды в кластере, если не указана - standalone приложение
ru.naumen.cluster.node.role=
# признак, что в составе кластера есть ридонли-веб ноды
ru.naumen.cluster.readOnlyNodes.enabled=false
ru.naumen.cluster.session.retrieval.timeout=20000
#Пропуск schema update в кластерных конфигурациях при запуске второй и последующих нод
ru.naumen.cluster.skipSchemaUpdate.enabled=false
# количество запросов на проверку нахождения ноды в кластере в секунду
ru.naumen.cluster.split.check.rateLimit=1
# количество миллисекунд на которое нужно заснуть для перепроверки отрицательного результата нахождения ноды в кластере
ru.naumen.cluster.split.check.timeSleepForRecheck=50

# допустимое время рассинхронизации (в миллисекундах) нод кластера для вычисления дат периода обслуживания
ru.naumen.cluster.timer.outOfSyncServiceTimeLimit=100
#Тип SequenceProvider'а true - кэширующий провайдер, false - некэширующий.
ru.naumen.naming.generator.cached=