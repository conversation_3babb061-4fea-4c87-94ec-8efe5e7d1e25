#количество отсылаемых сообщений в секунду, может быть дробным и меньше 1. -1 - неограничено
mail.dispatcher.oneByOne.rateLimit=-1
#таймаут бездействия потока в пуле (В секундах)
mail.dispatcher.pool.idleTimeout=60
#таймаут выполнения действия отправки (В секундах)
mail.dispatcher.pool.taskTimeout=120
mail.dispatcher.pool.threads=1
#Доступны для выбора:bulk,oneByOne
mail.dispatcher.strategy=oneByOne
# вырезать спец теги ответа из html-тела письма
ru.naumen.email.body.cutAnswerTag=false
# сохранять исходник письма в том виде, в котором система его получила
ru.naumen.email.saveOriginal=false
#Сохранять черновик перед отправкой письма по ews
ru.naumen.ews.saveDraftBeforeSend.enabled=false
ru.naumen.mail.batch_size=1000
ru.naumen.mail_queue.batch_size=1000
ru.naumen.mail.chronologicalProcessing=false
ru.naumen.mail.cleaner.allowed_hours_begin=0
ru.naumen.mail.cleaner.allowed_hours_end=23
ru.naumen.mailqueue.cleaner.allowed_hours_begin=0
ru.naumen.mailqueue.cleaner.allowed_hours_end=23
ru.naumen.mail.debug=false
ru.naumen.mail.domainsThatDontSupportAPOP=
ru.naumen.mail.error_age=-1
ru.naumen.mail.issues_age=-1
##############ru.naumen.core.server.mail.MailCleaner#############################
# Количество писем или записей лога почты которые удаляются в одной транзакции
# которые удаляются в одной транзакции при периодической очистке лога почты
ru.naumen.mail.log_age=-1
ru.naumen.mail.maxErrorDelayMultiplier=5
ru.naumen.mail.max_recipients=90
# Переносит теги style в теги head
ru.naumen.mail.move_styles=false
#Включить простановку заголовка References для исходящих писем
ru.naumen.mail.outgoing.addReferencesHeader.enabled=true
#Таймаут на проверку подключения к smtp серверу исходящей почты в секундах
ru.naumen.mail.outgoing.smtpserver.timeout=5
#Проверять некорректные символы в теме письма для использования кастомного парсера
ru.naumen.mail.parser.needCheckWrongSymbolsInSubject=false
ru.naumen.mail.reader.batch.process.mode=all
# Минимальное значение задержки между попытками отправки письма (в сек.)
ru.naumen.mail.resendDelayLimit=60
transactional.mail.sender.send.tx.timeout=900
ru.naumen.mail.success_age=-1
# Разрешенные домены для API. Допустимо перечисление через ',' или ';'.
ru.naumen.mail.validation.domains=
#Включить режим использования верхнего регистра (стандарт RFC 5321) для отправки оповещений на email адреса
use.Email.allow.uppercase=false
#Использовать оригинальный текст оповещения без преобразования
ru.naumen.mail.useOriginalPlainText=false
