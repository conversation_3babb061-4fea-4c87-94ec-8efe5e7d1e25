#Admin's dispatches are send to dispatch-request-queue, and operator's dispatches to another
ru.naumen.async.dispatch.splitQueue.enabled=true
# Whether to allow core thread of dispatcher thread pools to be timed out
ru.naumen.async.dispatcher.executor.threadPool.allowCoreThreadTimeOut=false
#EA Rest API queue executors
ru.naumen.async.queue.appsrestapi.core.size=10
#EA Rest queue is async by default
ru.naumen.async.queue.appsrestapi.enabled=true
#EA Rest API queue limit
ru.naumen.async.queue.appsrestapi.limit=50000
#Common request queue executors
ru.naumen.async.queue.common.core.size=50
#Common queue is async by default
ru.naumen.async.queue.common.enabled=true
#Common request queue limit
ru.naumen.async.queue.common.limit=50000
#GWT-RPC queue executors
ru.naumen.async.queue.dispatch.core.size=50
#GWT-RPC queue is async by default
ru.naumen.async.queue.dispatch.enabled=true
#GWT-RPC queue limit
ru.naumen.async.queue.dispatch.limit=50000
#GWT-RPC queue executors for operator's dispatches
ru.naumen.async.queue.dispatchOperator.core.size=50
#GWT-RPC queue executors for operator's dispatches is async by default
ru.naumen.async.queue.dispatchOperator.enabled=true
#GWT-RPC queue executors for operator's dispatches limit
ru.naumen.async.queue.dispatchOperator.limit=50000
#Preview queue parameters
ru.naumen.async.queue.document.preview.core.size=10
ru.naumen.async.queue.document.preview.enabled=true
ru.naumen.async.queue.document.preview.limit=50000
#Download queue parameters
ru.naumen.async.queue.download.core.size=10
ru.naumen.async.queue.download.enabled=true
ru.naumen.async.queue.download.limit=50000
#Edit session queue parameters
ru.naumen.async.queue.editSession.core.size=10
ru.naumen.async.queue.editSession.enabled=true
ru.naumen.async.queue.editSession.limit=50000
#Adlist export queue
ru.naumen.async.queue.export.advlist.core.size=10
ru.naumen.async.queue.export.advlist.enabled=true
ru.naumen.async.queue.export.advlist.limit=50000
#Hierarchy export queue
ru.naumen.async.queue.export.hierarchy.core.size=10
ru.naumen.async.queue.export.hierarchy.enabled=true
ru.naumen.async.queue.export.hierarchy.limit=50000
#Report export queue
ru.naumen.async.queue.export.report.core.size=10
ru.naumen.async.queue.export.report.enabled=true
ru.naumen.async.queue.export.report.limit=50000
#Gateway request queue
ru.naumen.async.queue.gateway.enabled=true
ru.naumen.async.queue.gateway.limit=50000
ru.naumen.async.queue.gateway.size=10
#Health check queue executors for check stand methods
ru.naumen.async.queue.healthCheck.core.size=3
#Health check queue is async by default
ru.naumen.async.queue.healthCheck.enabled=true
#Gwt-Logging request queue executors
ru.naumen.async.queue.logging.core.size=5
#Gwt-Logging queue is async by default
ru.naumen.async.queue.logging.enabled=true
#Gwt-Logging request queue limit
ru.naumen.async.queue.logging.limit=50000
#Metrics request queue executors
ru.naumen.async.queue.metrics.core.size=1
#Metrics queue is async by default
ru.naumen.async.queue.metrics.enabled=true
#Metrics request queue limit
ru.naumen.async.queue.metrics.limit=5
#Mobile API queue executors
ru.naumen.async.queue.mobileApi.core.size=50
#Mobile API queue is async by default
ru.naumen.async.queue.mobileApi.enabled=true
#Mobile API queue limit
ru.naumen.async.queue.mobileApi.limit=50000
#Portal Rest API queue executors
ru.naumen.async.queue.portalrestApi.core.size=30
#Portal Rest queue is async by default
ru.naumen.async.queue.portalrestApi.enabled=true
#Portal Rest API queue limit
ru.naumen.async.queue.portalrestApi.limit=50000
#Rest API queue executors
ru.naumen.async.queue.restApi.core.size=30
#Rest API queue is async by default
ru.naumen.async.queue.restApi.enabled=true
#Rest API queue limit
ru.naumen.async.queue.restApi.limit=50000
#Rtf request queue executors
ru.naumen.async.queue.rtf.core.size=10
#Rtf queue is async by default
ru.naumen.async.queue.rtf.enabled=true
#Rtf request queue limit
ru.naumen.async.queue.rtf.limit=50000
#SmpSync request queue limit
ru.naumen.async.queue.smpSyncApi.limit=200
#Soap request queue executors
ru.naumen.async.queue.soap.core.size=5
#Soap queue is async by default
ru.naumen.async.queue.soap.enabled=true
#Soap request queue limit
ru.naumen.async.queue.soap.limit=50000
ru.naumen.async.queue.uploadRtfImage.enabled=true
ru.naumen.async.queue.uploadRtfImage.limit=50000
ru.naumen.async.queue.uploadRtfImage.size=30
#Async request processing enabled by default.
ru.naumen.async.servlets.enabled=false
ru.naumen.async.statistics.enabled=false