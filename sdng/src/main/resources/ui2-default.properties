# Временные объекты формы: Разрешенный час начала запуска задач очистки (0 - 23)
quartz.ui2.formCleaner.allowedHoursBegin=0
# Временные объекты формы: Предельный час окончания запуска задач очистки (0 - 23)
quartz.ui2.formCleaner.allowedHoursEnd=23
# Временные объекты формы: Размер пачки для задачи очистки
quartz.ui2.formCleaner.batchSize=1000
#Временные объекты формы: Максимально допустимая задержка запуска задач в секундах
quartz.ui2.formCleaner.delayRandomValue=0
# Временные объекты формы: Время жизни временного объекта, дней
quartz.ui2.formCleaner.lifetime=5
# Число потоков, выполняющих запросы интерфейса 2.0
ru.naumen.async.queue.uirestApi.core.size=30
# Параметр, отвечающий за включение очереди запросов интерфейса 2.0
ru.naumen.async.queue.uirestApi.enabled=false
# Лимит очереди запросов интерфейса 2.0, при достижении которого приложение будет отвечать HTTP 503
ru.naumen.async.queue.uirestApi.limit=50000
# Вкл/выкл отображения в ИА настройки логотипов ui 2
ru.naumen.ui2.adminLogoSettings.enabled=false
# Ограничение на максимальное количество значений одновременно выбранных в одном поле формы
ru.naumen.ui2.selectList.maxSelectedValues=5000
# Постраничное ограничение элементов в выпадающих списках
ru.naumen.ui2.selectList.paginationLimit=100
# код шаблона, который используется как профиль сотрудника
ru.naumen.ui2.userProfile=userProfile