<configuration xmlns="urn:activemq"
               xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xsi:schemaLocation="urn:activemq /schema/artemis-configuration.xsd">


    <core xmlns="urn:activemq:core" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="urn:activemq:core ">


        <!-- Макс. "объем" памяти для всех очередей 110 Mb -->
        <global-max-size>115343360</global-max-size>
        <journal-buffer-size>102400000</journal-buffer-size>
        <journal-file-size>102400000</journal-file-size>

        <max-disk-usage>99</max-disk-usage>
   		
        <!-- Other config -->
        <security-enabled>false</security-enabled>

        <address-settings>

            <!-- Применяется к очереди исходящих сообщений омниканальности Queue.omnichannel.OutgoingMessage -->
            <address-setting match="Queue.omnichannel.OutgoingMessage">
                <!-- Включение last value queue -->
                <default-last-value-queue>true</default-last-value-queue>
            </address-setting>

            <!-- применяется к очереди планируемых событий PEForSubjectCreateMessage -->
            <address-setting match="jms.queue.Queue.PlannedEvent.PEForSubjectCreateMessage">
            <!-- Ожидание перед повторной отправкой (первая попытка) сообщения = 5s -->
                <redelivery-delay>5000</redelivery-delay>
            <!-- Увеличение интервала ожидания до следующей попытки(во сколько раз) -->
                <redelivery-delay-multiplier>5.0</redelivery-delay-multiplier>
            <!-- Максимальное количество попыток отправки -->
                <max-delivery-attempts>1</max-delivery-attempts>
            </address-setting>

            <!--применяется ко всем очередям (wildcard #) -->
            <address-setting match="#">
                <dead-letter-address>Queue.Shared.DeadLetter</dead-letter-address>
                <expiry-address>Queue.Shared.Expiry</expiry-address>

                <redelivery-delay>5000</redelivery-delay>
                <max-delivery-attempts>5</max-delivery-attempts>  
         
                <!-- Размер страницы при пейджинге = 10 Mb -->
                <page-size-bytes>10485760</page-size-bytes>
         		
                <!-- Размер после которого включается пйджинг = 100 Mb -->
                <max-size-bytes>104857600</max-size-bytes>       
         
                 <!-- PAGE, DROP or BLOCK
                  Что мы должны делать с сообщением больше max-size-bytes? 
                  PAGE - использовать пейджинг
                  DROP - удалять 
                  BLOCK - блокировать клиент при попытке записи такого сообщения -->
                <address-full-policy>PAGE</address-full-policy>
				<auto-create-queues>false</auto-create-queues>
        		<auto-create-jms-queues>false</auto-create-jms-queues>
        		<auto-create-jms-topics>false</auto-create-jms-topics>
            </address-setting>

            <address-setting match="activemq.#">
                <auto-delete-addresses>true</auto-delete-addresses>
                <enable-metrics>false</enable-metrics>
            </address-setting>

        </address-settings>
        <connection-ttl-override>-1</connection-ttl-override>
    </core>
</configuration>