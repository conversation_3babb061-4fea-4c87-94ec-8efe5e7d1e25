AdvImportCategories.AFTER_IMPORT.title=Script that runs after import of all objects
AdvImportCategories.AFTER_PROCESS.title=Script that runs after execution of the business process of creating / editing an object
AdvImportCategories.BEFORE_PROCESS.title=Script that runs before execution of the business process of creating / editing the object
AdvImportCategories.BEFORE_PROCESS_ITEM.title=Script that runs before forming properties of the business process
AdvImportCategories.CONVERTER_SCRIPT.title=Converting value based on script
AdvImportCategories.IMPORT_FILTER.title=Filtering objects based on script
AdvImportCategories.METACLASS_RESOLVER.title=Strategy of class and types definition of created objects
AttributeCategories.COMPUTABLE.title=Computable attributes
AttributeCategories.COMPUTABLE_ON_FORM.title=Compute attribute values during edit
AttributeCategories.DATE_TIME_RESTRICTION.title=Restriction of value of date, date/datetime attribute
AttributeCategories.DEFAULT_VALUE.title=Computable default attribute values
AttributeCategories.FILTRATION.title=Filter attribute values during edit
EscalationCategories.CONDITION.title=Execution conditions of escalations
EscalationCategories.NOTIFICATION_CUSTOMIZATION.title=Escalation notification customization
EscalationCategories.SCRIPT.title=Escalation actions scripts
EventActionCategories.EVENTACTION_CONDITION.title=Execution conditions of event actions
EventActionCategories.EVENTACTION_INTEGRATION.title=Scripts for sending data to an external queue
EventActionCategories.EVENTACTION_NOTIFICATION_CUSTOMIZATION.title=Event actions notification customization
EventActionCategories.EVENTACTION_SCRIPT.title=Event actions scripts
EventActionCategories.EVENTACTION_WSMESSAGE_CUSTOMIZATION.title=Customization of object state messages
LearningProcessCategories.DATA_PREPARATION.title=Data preparation scripts
LearningProcessCategories.GATHERING_DATA.title=Gathering data scripts
LearningProcessCategories.LEARNING_AND_VALIDATION.title=Learning and validation scripts
LearningProcessCategories.SAVE_DATA.title=Save data scripts
MassDelete.dialog-caption=Confirm delete
MassDelete.dialog-message=Are you sure want to delete this modules?<br><br>All dependent scripts may become incorrect.
OtherCategories.APPLICATION.title=Built in application scripts
OtherCategories.CONSOLE.title=Console scripts
OtherCategories.CTI.title=Rule of calls processing
OtherCategories.FILTER_RESTRICTION.title=Filter restriction scripts
OtherCategories.MAIL_PROCESSOR_RULE.title=Mail processing rules
OtherCategories.MONITORING.title=Alert processing script
OtherCategories.PERMISSIONS.DefaultScriptTitle=Script of refining the permissions № {0}
OtherCategories.PERMISSIONS.title=Refining the permissions
OtherCategories.REPORT_TEMPLATE.title=Report and printing form templates scripts
OtherCategories.SCHEDULER_TASK.title=Task scheduler scripts
OtherCategories.VOICE_PROCESSING.title=Voice data processing scripts
OtherCategories.WITHOUT_CATEGORY.title=[Without category]
ParameterCategories.PARAM_COMPUTABLE_ON_FORM.title=Compute values of event parameters
ParameterCategories.PARAM_COMPUTE_ANY_CATALOG_ELEMENTS.title=Compute items of catalog
ParameterCategories.PARAM_DATE_TIME_RESTRICTION.title=Restriction of value of date, date/datetime parameter
ParameterCategories.PARAM_DEFAULT_VALUE.title=Computable default values of event parameters
ParameterCategories.PARAM_FILTRATION.title=Filter values of event parameters
RoleCategories.ACCESS.title=Determination user access permissions to the object
RoleCategories.LIST_FILTER.title=Determine the filtering condition for objects available while searching to the role holder
RoleCategories.FAST_LINK_RIGHTS.title=Determine the filtering condition for objects available for mention to the role holder
RoleCategories.OWNERS.title=Determination list of users holding the role
SCParametersCategories=Request parameters: "SLA/Service" field
SC_PARAMETERS_AGREEMENT=Filter SLA during edit
SC_PARAMETERS_CASES=Filter types during edit
SC_PARAMETERS_SLM=Filter services during edit
ScriptByCategory.cardTitle=Category "{0}"
ScriptByClass.cardTitle=Class "{0}" ({1})
ScriptModule.textIsHidden=Module text is not available.
SmiaModelCategories.POSTPROCESS.title=Answer postprocessing scripts
SmiaModelCategories.PREPROCESS.title=Data preprocessing scripts
TimerCategory.TIMER_CONDITION.title=Change states timer conditions
WorkflowCategory.WF_SCRIPT_ACTION.title=State entry/exit actions
WorkflowCategory.WF_SCRIPT_CONDITION.title=Conditions on state entry/exit
categoriesDefault=Categories (default)
categoriesUsages=Categories (where script is used)
code=Code
delete=Delete
deleteModuleCaption=Confirm remove
deleteModuleMessage=Are you sure you want to delete the module? If you remove all the scripts that use the methods of the module will not operate correctly.
deleteNotPermittedError=Modules {0} can not be deleted: you have no permission to delete this modules.
description=Description
documentation=Documentation
edit=Edit
haveNoPermission=You don''t have permissions to perform this operation
markerForProfile=Permission marker "{0}" for the profile "{1}"
markerForVersioningProfile=Permission marker "{0}" for the versioning profile "{1}"
moduleCardTitle=Module {0}
moduleCodeExistsError=Module cannot be added. Module with this code already exists.
monitoring=Monitoring system
newScript=[new script]
number=Number
resourceNotFound=The requested resource is not found. Use the navigation bar or enter the correct address in browser.
restAllowed=Available for REST-service
scriptCardTitle=Script {0} {1}
scriptCatalog=Script catalog
scriptCatalog.allScripts=All scripts
scriptCatalog.byCategories=By category
scriptCatalog.byCategories.scriptsCount=Scripts count
scriptCatalog.byClasses=By class
scriptCatalog.noClass=[No class]
scriptCodeExistsError=Script cannot be added. Script with code "{0}" already exists.
scriptEdit=Script editing
scriptLowerCase=script
scriptModuleAddition=Add module
scriptModuleEdit=Module editing
scriptModules=Module catalog
scriptsAndModules=Script and module catalog
set=Set
superUserReadable=Available for superusers to view
superUserReadable.short=View
superUserWritable=Available for superusers to edit
superUserWritable.short=Edit
text=Text
usagePointNotFoundError=Object can not be deleted. The link to script {0} is broken.
usagePoints=Settings, where script is used
version=Version
withoutScript=[no script]
