<?xml version="1.0" encoding="UTF-8"?>
<form 
    xmlns:guic="http://www.w3.org/2001/XMLSchema-instance"
    guic:noNamespaceSchemaLocation="../../../guic/server/schemas/guic.xsd"
    title=":SCParametersMessages.scParametersEditingClient:" 
    controller="ru.naumen.metainfoadmin.server.scparams.EditSCParametersClientFormController"
    client-controller="ru.naumen.metainfoadmin.client.scparams.EditSCParametersFormContragentClientController"
    name="EditSCParametersClient">
    <check-box-field name="clientRequiredEditable" 
        title=":SCParametersMessages.clientRequiredEditableTitle:"
        debug-id="clientRequiredEditable"
        value="clientRequiredEditable"
        on-change="ClientRefreshSync"/>
    <check-box-field name="clientAutoResolve" 
        title=":SCParametersMessages.clientAutoResolveTitle:"
        debug-id="clientAutoResolve"
        description=":SCParametersMessages.clientAutoResolveDescription:" 
        value="clientAutoResolve" />
</form>