SCParametersMessages.treeList=Список со сдвигом (соглашение и услуга)
SCParametersMessages.serviceTreeList=Список со сдвигом (услуги)
SCParametersMessages.hierarchicalTreeList=Иерархический список (соглашение и услуга)
SCParametersMessages.hierarchicalServiceTreeList=Иерархический список (услуги)
SCParametersMessages.servicesFiltrationScript=Скрипт фильтрации услуг
SCParametersMessages.servicesFiltration=Фильтрация услуг при редактировании
SCParametersMessages.agreementsFiltration=Фильтрация соглашений при редактировании
SCParametersMessages.agreement=Соглашение
SCParametersMessages.agreementOrService=Соглашение или услуга
SCParametersMessages.agreementService=Соглашение/услугу
SCParametersMessages.service=Услуга
SCParametersMessages.caseServiceCall=Тип запроса
SCParametersMessages.agreementsFiltrationScript=Скрипт фильтрации соглашений
SCParametersMessages.agreementServiceField=Поле "Соглашение/Услуга"
SCParametersMessages.agreementServiceValue=Значение поля "Соглашение/Услуга"
SCParametersMessages.agreementServiceOrCase=Определяющее поле при выборе соглашения/услуги и типа запроса
SCParametersMessages.orderValue=Выбирать сначала
SCParametersMessages.scParametersEditing=Изменение параметров поля "Соглашение/Услуга"
SCParametersMessages.scParametersEditingOrder=Изменение параметров запроса
SCParametersMessages.unconformedServiceCallsMessage=В системе присутствуют запросы, для которых текущее значение поля "Соглашение/Услуга" не соответствует настройке {0}: {1}
SCParametersMessages.serviceCallParametersDescription=Используется для настройки возможностей привязки запросов
SCParametersMessages.flatList=Плоский список
SCParametersMessages.serviceCallParameters=Параметры запросов
SCParametersMessages.selectFromCatalog=Выбор из каталога
SCParametersMessages.editPresentation=Представление для редактирования
SCParametersMessages.unconformedClientMetaClassesMessage=В системе присутствуют классы/типы контрагентов, в которых текущее значение параметров запроса по умолчанию не соответствует настройке {0}: {1}. Для данных контрагентов привязка запроса по умолчанию действовать не будет.
SCParametersMessages.client=Контрагент
SCParametersMessages.clientAutoResolveTitle=Автоматически заполнять текущим пользователем
SCParametersMessages.scParametersEditingClient=Изменение параметров поля "Контрагент"
SCParametersMessages.clientAutoResolveDescription=Если пользователь лицензированный, контент "Выбор контрагента" отсутствует на форме добавления, и в URL не передан соответствующий параметр, то контрагентом запроса устанавливается текущий пользователь. Если пользователь не лицензированный, то поле “Контрагент” заполняется автоматически не зависимо от параметра.
SCParametersMessages.clientRequiredEditableTitle=Управлять обязательностью контрагента на уровне типа
SCParametersMessages.clientRequiredEditableAttention=Если “Контрагент” не указан, то для выбора будут доступны все “Соглашения”.
SCParametersMessages.casesFilteringTitle=Поле "Тип запроса"
SCParametersMessages.casesFilteringParamCaption=Фильтрация типов при редактировании
SCParametersMessages.casesFilteringScriptCaption=Скрипт фильтрации типов
SCParametersMessages.casesFilteringParametersEditing=Изменение параметров поля "Тип запроса"
