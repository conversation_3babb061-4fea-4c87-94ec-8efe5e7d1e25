changeAppParamParagraph1=Loading a new version of the built-in application "{0}". Used in: "{1}" etc. (total {2})
changeAppParamParagraph1Small=Loading a new version of the built-in application "{0}". Used in: "{1}"
changeAppParamParagraph2=These changes will be made to the new version of the built-in application: {0} etc. (total {1}) Continue loading?
changeAppParamParagraph2Small=These changes will be made to the new version of the built-in application:<br> {0} <br>Continue loading?
changeAssociationForm=Change association form
changeCaseForm=Change case form
changeParamPossibleValueEvent=List of parameter values changed
changeRequiredParamEvent=The parameter became required
changeResponsibleForm=Change responsible form
changeStateForm=Change state form
changeTypeParamEvent=Parameter type changed
commentForm=Comment add and edit form
deleteParamEvent=Parameter removed
fileForm=File add and edit form
newRequiredParamEvent=Required parameter added
quickAddAndEditForm=Quick add and edit form
userEventActionForm=Form of user event action
continueLoad=Continue loading?
