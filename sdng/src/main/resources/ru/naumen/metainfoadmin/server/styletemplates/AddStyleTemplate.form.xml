<form
    xmlns:guic="http://www.w3.org/2001/XMLSchema-instance"
    guic:noNamespaceSchemaLocation="../../../guic/server/schemas/guic.xsd"
    title=":styletemplates-addFormTitle:"
    controller="ru.naumen.metainfoadmin.server.style.templates.AddStyleTemplateFormController"
    client-controller="ru.naumen.metainfoadmin.client.style.templates.AddStyleTemplateFormClientController"
    fixed="false"
    name="AddStyleTemplate">
    <field name="title"
        presentation="textBox"
        title=":title:"
        debug-id="title" 
        required="true"
        on-change="ClientRefreshSync"
        validation="StringLength1000"
        value="title"
        max-length="1000" />
    <field name="code"
        presentation="textBox" 
        title=":code:"
        debug-id="code"
        required="true"
        validation="MetainfoKey"
        value="code"
        max-length="255" />
    <field name="templateText" 
        presentation="richTextArea"
        title=":styletemplates-templateText:" 
        debug-id="templateText"
        required="true"
        value="templateText" />
    <field name="settingsSet"
           title=":styletemplates-set:"
           presentation="listBoxWithEmptyOpt"
           debug-id="settingsSet"
           elements="settingsSet#elements"
           value="settingsSet"/>
</form>
