<tab
    xmlns:guic="http://www.w3.org/2001/XMLSchema-instance"
    guic:noNamespaceSchemaLocation="../../../guic/server/schemas/guic.xsd"
    place-class="ru.naumen.metainfoadmin.client.script.ScriptCatalogPlace" 
    title=":scriptcatalog-scriptCatalog.byCategories:"
    name="ScriptsByCategories">
    <table-list name="scriptsByCategories"
        controller="ru.naumen.metainfoadmin.server.script.ScriptsByCategoriesListController"
        debug-id="byCategoriesContent"
        selection-enabled="false"
        filtering-enabled="false"
        paging-enabled="false">
        <column name="title"
             title=":title:"
             width="650px"
             debug-id="@title"/>
         <column name="scriptsCount"
        title=":scriptcatalog-scriptCatalog.byCategories.scriptsCount:"
        debug-id="@scriptsCount"/>
        </table-list>
</tab>
