markerForVersioningProfile=Rechtetoken "{0}" für Planungsmodusprofil "{1}"
scriptModules=Modulkatalog
MassDelete.dialog-caption=Löschen bestätigen
MassDelete.dialog-message=Wollen Sie die Module wirklich löschen?<br><br>Beim <PERSON> werden alle Skripte, die Modulmethoden verwenden, nicht korrekt funktionieren.
superUserReadable=Zur Ansicht durch Superuser verfügbar
superUserWritable=Von Superusern bearbeitbar
description=Beschreibung
text=Text
documentation=Documentation
newScript=[neues Skript]
edit=Bearbeiten
delete=Löschen
withoutScript=[kein Skript]
monitoring=Überwachungssystem
markerForProfile=Rechtetoken "{0}" für Profil "{1}"
code=Code
scriptModuleAddition=Hinzufügen eines Moduls
superUserReadable.short=Ansicht
superUserWritable.short=Bearbeitung
moduleCodeExistsError=Das Modul kann nicht hinzugefügt werden. Ein Modul mit diesem Code existiert bereits.
scriptCodeExistsError=Das Skript konnte nicht hinzugefügt werden. Skript mit Code "{0}" ist bereits vorhanden.
deleteNotPermittedError=Module {0} können nicht gelöscht werden: Sie sind nicht berechtigt, diese Module zu löschen.
usagePointNotFoundError=Das Objekt kann nicht gelöscht werden. Die Verbindung mit dem Skript mit dem Code {0} wurde unterbrochen.
scriptsAndModules=Katalog der Skripten und Module
scriptCatalog=Katalog der Skripte
scriptCatalog.allScripts=Alle Skripte
scriptCatalog.byCategories=Nach Kategorie
scriptCatalog.byClasses=Nach Klasse
scriptCatalog.noClass=[Keine Klasse]
version=Version
resourceNotFound=Die angeforderte Ressource wurde nicht gefunden. Verwenden Sie das Navigationsmenü oder geben Sie die richtige Adresse in Ihren Browser ein.
scriptCatalog.byCategories.scriptsCount=Anzahl der Skripte
ScriptByCategory.cardTitle=Kategorie "{0}"
ScriptByClass.cardTitle=Klasse "{0}" ({1})
AdvImportCategories.METACLASS_RESOLVER.title=Strategie zur Definition der Klasse und der Typen der zu erstellenden Objekte
AdvImportCategories.AFTER_IMPORT.title=Skript, das ausgeführt wird, nachdem alle Objekte importiert wurden
AdvImportCategories.AFTER_PROCESS.title=Skript, das nach dem Geschäftsprozess der Erstellung/Bearbeitung eines Objekts ausgeführt wird
AdvImportCategories.BEFORE_PROCESS_ITEM.title=Skript, das vor dem Generieren von Geschäftsprozesseigenschaften ausgeführt werden soll
number=Nummer
AdvImportCategories.BEFORE_PROCESS.title=Skript, das ausgeführt werden soll, bevor der Geschäftsprozess zum Erstellen/Bearbeiten eines Objekts ausgeführt wird
AdvImportCategories.IMPORT_FILTER.title=Skriptbasierte Objektfilterung
AdvImportCategories.CONVERTER_SCRIPT.title=Skriptbasierte Wertumwandlung
AttributeCategories.COMPUTABLE.title=Berechenbare Attribute
AttributeCategories.DEFAULT_VALUE.title=Berechnbare Standard-Attributwerte
AttributeCategories.FILTRATION.title=Filtern von Attributwerten bei der Bearbeitung
AttributeCategories.COMPUTABLE_ON_FORM.title=Berechnung von Attributwerten bei der Bearbeitung
ParameterCategories.PARAM_FILTRATION.title=Filtern von Ereignisparameterwerten
ParameterCategories.PARAM_COMPUTABLE_ON_FORM.title=Berechnung der Ereignisparameterwerte
ParameterCategories.PARAM_DEFAULT_VALUE.title=Berechenbare Standardwerte für Ereignisparameter
AttributeCategories.DATE_TIME_RESTRICTION.title=Einschränkung von Attributwerten des Typs Datum, Datum/Zeit
ParameterCategories.PARAM_COMPUTE_ANY_CATALOG_ELEMENTS.title=Berechnung von Verzeichniselementen
LearningProcessCategories.GATHERING_DATA.title=Skripte zur Datenerfassung
LearningProcessCategories.SAVE_DATA.title=Skripte zur Datensicherung
LearningProcessCategories.DATA_PREPARATION.title=Datenaufbereitungsskripte
ParameterCategories.PARAM_DATE_TIME_RESTRICTION.title=Grenzwerte der Parametertypen Datum, Datum/Zeit
EventActionCategories.EVENTACTION_INTEGRATION.title=Skripte zum Senden von Daten an eine externe Warteschlange
OtherCategories.VOICE_PROCESSING.title=Skripte für die Sprachdatenverarbeitung
ScriptModule.textIsHidden=Der Text des Moduls ist nicht verfügbar.
EscalationCategories.SCRIPT.title=Skripte für Eskalationsaktionen
EscalationCategories.NOTIFICATION_CUSTOMIZATION.title=Eskalationswarnungen individuell anpassen
EscalationCategories.CONDITION.title=Bedingungen für die Ausführung von Eskalationsaktionen
OtherCategories.APPLICATION.title=Scripts für integrierte Anwendungen
OtherCategories.FILTER_RESTRICTION.title=Skripte für Filterbeschränkungen
EventActionCategories.EVENTACTION_SCRIPT.title=Skripte für Ereignisaktionen
EventActionCategories.EVENTACTION_CONDITION.title=Bedingungen für eine Ereignisaktion
OtherCategories.CONSOLE.title=Konsolenskripte
OtherCategories.CTI.title=Regel für die Bearbeitung von Anrufen
OtherCategories.MAIL_PROCESSOR_RULE.title=Regeln für die Postbearbeitung
OtherCategories.MONITORING.title=Skript zur Alarmverarbeitung
OtherCategories.PERMISSIONS.title=Klärung der Zugriffsrechte
OtherCategories.REPORT_TEMPLATE.title=Skripte für Berichtsvorlagen und Druckformulare
OtherCategories.SCHEDULER_TASK.title=Skripte für Scheduler-Aufgaben
OtherCategories.WITHOUT_CATEGORY.title=[Unkategorisiert]
SmiaModelCategories.POSTPROCESS.title=Skripte für die Nachbearbeitung von Antworten
RoleCategories.LIST_FILTER.title=Definieren der Bedingung für die Auswahl von Objekten, die dem Rolleninhaber bei der Suche zur Verfügung stehen
RoleCategories.OWNERS.title=Definieren einer Liste von Benutzern mit der Rolle
SCParametersCategories=Anfrageparameter: Feld "Vereinbarung/Service"
SC_PARAMETERS_CASES=Skript zum Filtern von Typen beim Bearbeiten
LearningProcessCategories.LEARNING_AND_VALIDATION.title=Trainings- und Testskripte
restAllowed=Verfügbar für REST-Anfragen
EventActionCategories.EVENTACTION_NOTIFICATION_CUSTOMIZATION.title=Benachrichtigungsanpassung von Ereignisaktionen
RoleCategories.ACCESS.title=Definieren von Benutzerzugriffsrechten für ein Objekt
SC_PARAMETERS_AGREEMENT=Skript zur Filterung von Vereinbarungen beim Bearbeiten
OtherCategories.PERMISSIONS.DefaultScriptTitle=Zugriffsrechte Klärung Skript Nr. {0}
SC_PARAMETERS_SLM=Skript zum Filtern von Services bei der Bearbeitung
SmiaModelCategories.PREPROCESS.title=Skripte für die Datenvorverarbeitung
TimerCategory.TIMER_CONDITION.title=Bedingungen für die Änderung von Zeitzählerständen
WorkflowCategory.WF_SCRIPT_ACTION.title=Aktionen beim Betreten/Verlassen des Status
WorkflowCategory.WF_SCRIPT_CONDITION.title=Bedingungen für den Eingangs-/Ausgangsstatus
categoriesDefault=Kategorien (bestimmungsgemäße Verwendungsorte)
categoriesUsages=Kategorien (Verwendungsorte)
haveNoPermission=Sie haben keine Berechtigung, diesen Vorgang durchzuführen
moduleCardTitle=Modul {0}
scriptCardTitle=Skript {0}
deleteModuleCaption=Bestätigung der Löschung
deleteModuleMessage=Wollen Sie das Modul wirklich entfernen? Wenn Sie das Modul deinstallieren, werden alle Skripte, die Methoden des Moduls verwenden, nicht korrekt funktionieren.
scriptEdit=Bearbeitung des Skripts
scriptModuleEdit=Ein Modul bearbeiten
scriptLowerCase=Skript
usagePoints=Einstellungen, wo das Skript verwendet wird
EventActionCategories.EVENTACTION_WSMESSAGE_CUSTOMIZATION.title=Anpassung von Objektzustandsmeldungen
RoleCategories.FAST_LINK_RIGHTS.title=Definieren der Bedingung für die Auswahl von Objekten, die dem Rolleninhaber bei Erwähnung zur Verfügung stehen
