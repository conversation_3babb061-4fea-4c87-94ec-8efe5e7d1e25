code=Код
scriptModules=Каталог модулів
scriptsAndModules=Каталог скриптів та модулів
scriptCatalog=Каталог скриптів
description=Опис
version=Версія
text=Текст
newScript=[новий скрипт]
withoutScript=[без скрипта]
MassDelete.dialog-caption=Підтвердження видалення
superUserReadable=Доступний для перегляду суперкористувачами
superUserWritable=Доступний для редагування суперкористувачами
monitoring=Система моніторингу
markerForProfile=Маркер прав "{0}" для профілю "{1}"
markerForVersioningProfile=Маркер прав "{0}" для профілю режиму "{1}"
scriptModuleAddition=Додавання модуля
superUserReadable.short=Перегляд
superUserWritable.short=Редагування
moduleCodeExistsError=Модуль не може бути доданий. Модуль із таким кодом вже існує.
scriptCodeExistsError=Скрипт не може бути доданий. Скрипт із кодом "{0}" вже існує.
usagePointNotFoundError=Об''єкт не можна видалити. Порушено зв''язок зі скриптом із кодом {0}.
scriptCatalog.allScripts=Усі скрипти
scriptCatalog.byCategories=За категоріями
scriptCatalog.byClasses=За класами
scriptCatalog.noClass=[Без класу]
scriptCatalog.byCategories.scriptsCount=Кількість скриптів
ScriptByCategory.cardTitle=Категорія "{0}"
ScriptByClass.cardTitle=Клас "{0}" ({1})
AdvImportCategories.AFTER_IMPORT.title=Скрипт після імпорту всіх об''єктів
AdvImportCategories.AFTER_PROCESS.title=Скрипт, який виконується після виконання бізнес-процесу створення/редагування об''єкта
AdvImportCategories.BEFORE_PROCESS.title=Скрипт, який виконується перед виконанням бізнес-процесу створення/редагування об''єкта
AdvImportCategories.IMPORT_FILTER.title=Фільтрування об''єктів на основі скрипту
AdvImportCategories.CONVERTER_SCRIPT.title=Перетворення значення на основі скрипту
AttributeCategories.COMPUTABLE.title=Обчислювані атрибути
AttributeCategories.COMPUTABLE_ON_FORM.title=Обчислення значень атрибутів під час редагування
ParameterCategories.PARAM_FILTRATION.title=Фільтрування значень параметрів подій
ParameterCategories.PARAM_COMPUTABLE_ON_FORM.title=Обчислення значень параметрів подій
ParameterCategories.PARAM_DEFAULT_VALUE.title=Значення за замовчуванням параметрів подій
ParameterCategories.PARAM_COMPUTE_ANY_CATALOG_ELEMENTS.title=Обчислення елементів довідника
LearningProcessCategories.GATHERING_DATA.title=Скрипти збирання даних
LearningProcessCategories.SAVE_DATA.title=Скрипти збереження даних
LearningProcessCategories.DATA_PREPARATION.title=Скрипти підготовки даних
LearningProcessCategories.LEARNING_AND_VALIDATION.title=Скрипти навчання та тестування
EscalationCategories.SCRIPT.title=Скрипти дій щодо ескалації
EscalationCategories.CONDITION.title=Умови виконання дій щодо ескалації
EventActionCategories.EVENTACTION_SCRIPT.title=Скрипти дій щодо події
EventActionCategories.EVENTACTION_NOTIFICATION_CUSTOMIZATION.title=Кастомізація оповіщень дій щодо події
EventActionCategories.EVENTACTION_CONDITION.title=Умови виконання дій щодо події
OtherCategories.CTI.title=Правило обробки дзвінків
OtherCategories.CONSOLE.title=Консольні скрипти
OtherCategories.SCHEDULER_TASK.title=Скрипти завдань планувальника
OtherCategories.MAIL_PROCESSOR_RULE.title=Правила обробки пошти
OtherCategories.MONITORING.title=Скрипт обробки тривог
OtherCategories.PERMISSIONS.title=Уточнення прав доступу
OtherCategories.WITHOUT_CATEGORY.title=[Без категорії]
OtherCategories.APPLICATION.title=Скрипти вбудованих додатків
OtherCategories.FILTER_RESTRICTION.title=Скрипти для обмежень фільтрації
OtherCategories.VOICE_PROCESSING.title=Скрипти обробки голосових даних
RoleCategories.OWNERS.title=Визначення списку користувачів, які мають роль
SCParametersCategories=Параметри запитів: Поле "Угода/Послуга"
SC_PARAMETERS_SLM=Скрипт фільтрації послуг під час редагування
SC_PARAMETERS_AGREEMENT=Скрипт фільтрації угод під час редагування
SC_PARAMETERS_CASES=Скрипт фільтрації типів під час редагування
TimerCategory.TIMER_CONDITION.title=Умови зміни статусів лічильників часу
WorkflowCategory.WF_SCRIPT_CONDITION.title=Умови при вході/виході зі статусу
WorkflowCategory.WF_SCRIPT_ACTION.title=Події при вході/виході зі статусу
deleteModuleCaption=Підтвердження видалення
deleteModuleMessage=Ви дійсно хочете видалити модуль? При видаленні всі скрипти, що використовують методи модуля, працюватимуть некоректно.
edit=Редагувати
delete=Видалити
scriptModuleEdit=Редагування модуля
scriptEdit=Редагування скрипта
ScriptModule.textIsHidden=Текст модуля недоступний.
number=Номер
categoriesDefault=Категорії (імовірні місця використання)
categoriesUsages=Категорії (місця використання)
moduleCardTitle=Модуль {0}
scriptCardTitle=Скрипт {0}
resourceNotFound=Затребуваний ресурс не знайдено. Скористайтеся меню навігації або введіть правильну адресу в адресний рядок браузера.
haveNoPermission=У Вас немає прав на виконання цієї операції
usagePoints=Налаштування, де використовується скрипт
OtherCategories.PERMISSIONS.DefaultScriptTitle=Скрипт уточнення прав доступу № {0}
scriptLowerCase=скрипт
SmiaModelCategories.PREPROCESS.title=Скрипти попередньої обробки даних
SmiaModelCategories.POSTPROCESS.title=Скрипти постобробки відповіді
MassDelete.dialog-message=Ви дійсно хочете видалити модулі?<br><br>При видаленні всі скрипти, що використовують методи модулів, працюватимуть некоректно.
deleteNotPermittedError=Модулі {0} не можуть бути видалені: у вас немає прав на видалення даних модулів.
AdvImportCategories.METACLASS_RESOLVER.title=Стратегія визначення класу та типів створюваних об''єктів
AdvImportCategories.BEFORE_PROCESS_ITEM.title=Скрипт, який виконується перед формуванням властивостей бізнес-процесу
AttributeCategories.DEFAULT_VALUE.title=Значення за замовчуванням атрибутів
AttributeCategories.FILTRATION.title=Фільтрування значень атрибутів під час редагування
AttributeCategories.DATE_TIME_RESTRICTION.title=Обмеження значень атрибутів типу дата, дата/час
ParameterCategories.PARAM_DATE_TIME_RESTRICTION.title=Обмеження значень параметрів типу дата, дата/час
EscalationCategories.NOTIFICATION_CUSTOMIZATION.title=Кастомізація оповіщень дій щодо ескалації
EventActionCategories.EVENTACTION_INTEGRATION.title=Скрипти надсилання даних у зовнішню чергу
OtherCategories.REPORT_TEMPLATE.title=Скрипти шаблонів звітів та друкованих форм
RoleCategories.ACCESS.title=Визначення прав доступу користувача до об''єкту
RoleCategories.LIST_FILTER.title=Визначення умови відбору об''єктів, доступних власнику ролі
