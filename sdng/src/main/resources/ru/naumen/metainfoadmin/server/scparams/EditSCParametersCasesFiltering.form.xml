<?xml version="1.0" encoding="UTF-8"?>
<form 
    xmlns:guic="http://www.w3.org/2001/XMLSchema-instance"
    guic:noNamespaceSchemaLocation="../../../guic/server/schemas/guic.xsd"
    title=":SCParametersMessages.casesFilteringParametersEditing:" 
    controller="ru.naumen.metainfoadmin.server.scparams.EditSCParametersCasesFiltering"
    fixed="false"
    name="EditSCParametersCasesFiltering">
    <check-box-field name="isFilterCases" 
        title=":SCParametersMessages.casesFilteringParamCaption:"
        debug-id="filterCases" 
        value="isFilterCases"
        control-visibility="casesFiltrationScript" />
    <script-field name="casesFiltrationScript" 
        presentation="scriptComponentEdit" 
        title=":SCParametersMessages.casesFilteringScriptCaption:" 
        debug-id="casesFiltrationScript" 
        value="casesFiltrationScript" 
        required="true" 
        script-category="filtration_slm"/>
</form>