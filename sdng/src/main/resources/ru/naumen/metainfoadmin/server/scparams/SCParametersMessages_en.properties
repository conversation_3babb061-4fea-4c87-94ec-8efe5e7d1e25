#
#Wed Jun 05 12:08:02 YEKT 2013
SCParametersMessages.treeList=Shifted list (agreement and service)
SCParametersMessages.serviceTreeList=Shifted list (services)
SCParametersMessages.hierarchicalTreeList=Hierarchical list (agreement and service)
SCParametersMessages.hierarchicalServiceTreeList=Hierarchical list (services)
SCParametersMessages.servicesFiltrationScript=Script to filter services
SCParametersMessages.servicesFiltration=Filter services during edit
SCParametersMessages.agreementsFiltration=Filter SLA during edit
SCParametersMessages.agreement=SLA
SCParametersMessages.agreementOrService=SLA or service
SCParametersMessages.agreementService=SLA/service
SCParametersMessages.service=Service
SCParametersMessages.caseServiceCall=Type of request
SCParametersMessages.agreementsFiltrationScript=Script to filter SLA
SCParametersMessages.agreementServiceField="SLA/Service" field
SCParametersMessages.agreementServiceValue="SLA/Service" field value
SCParametersMessages.agreementServiceOrCase=Defining field when selecting SLA/service and type of request
SCParametersMessages.orderValue=Select first
SCParametersMessages.scParametersEditing=Changing the parameters of the field "SLA/Service"
SCParametersMessages.scParametersEditingOrder=Edit request parameters
SCParametersMessages.unconformedServiceCallsMessage=The system contains requests where current value of the "SLA/Service" field does not correspond to the settings {0}: {1}
SCParametersMessages.serviceCallParametersDescription=Used to configure requests association settings
SCParametersMessages.flatList=Flat list
SCParametersMessages.serviceCallParameters=Request parameters
SCParametersMessages.selectFromCatalog=Select from directory
SCParametersMessages.editPresentation=Representation to edit
SCParametersMessages.unconformedClientMetaClassesMessage=The system contains classes/types of clients, in which the current value of the default request parameters does not correspond to the setting {0}: {1}. For these counterparties the default request association will not function.
SCParametersMessages.client=Client
SCParametersMessages.clientAutoResolveTitle=Automatically fill in the current user
SCParametersMessages.scParametersEditingClient=Changing the parameters of the field "Client"
SCParametersMessages.clientAutoResolveDescription=If the current user is licensed, the content "Select client" is absent on the form, and corresponding parameter is not set in URL, then the current user will be set as a "Client". If user is not licensed, then the "Client" is filled automatically, regardless of this setting.
SCParametersMessages.clientRequiredEditableTitle=Allow to change parameter "Required" of attribute "Client" in the types of class "Request"
SCParametersMessages.clientRequiredEditableAttention=If "Client" is not specified, then will be available all "Agreement".
SCParametersMessages.casesFilteringTitle="Request type" field
SCParametersMessages.casesFilteringParamCaption=Filter Type of request during edit
SCParametersMessages.casesFilteringScriptCaption=Script to filter request types
SCParametersMessages.casesFilteringParametersEditing=Edit request type parameters
