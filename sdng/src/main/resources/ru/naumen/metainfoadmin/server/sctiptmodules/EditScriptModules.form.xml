<form
    xmlns:guic="http://www.w3.org/2001/XMLSchema-instance"
    guic:noNamespaceSchemaLocation="../../../guic/server/schemas/guic.xsd"
    title=":scriptcatalog-scriptModuleEdit:"
    controller="ru.naumen.metainfoadmin.server.script.modules.EditScriptModuleFormController"
    fixed="false"
    name="EditScriptModule">
    <field name="code" 
        debug-id="code"
        presentation="text" 
        title=":scriptcatalog-code:" 
        value="code"
        enabled="false"/>
    <field name="description"
        debug-id="description" 
        presentation="textArea" 
        title=":scriptcatalog-description:"
        value="description"
        validation="XmlChars"/>
    <field name="version" 
        debug-id="moduleVersion"
        presentation="integer" 
        title=":scriptcatalog-version:" 
        value="version"/>
    <script-field name="script" 
        debug-id="script"
        presentation="scriptEdit" 
        title=":scriptcatalog-text:" 
        required="true"
        value="script"
        script-category="modul"
        validation="XmlChars"/>
    <check-box-field name="superUserReadable"
        debug-id="superUserReadable" 
        title=":scriptcatalog-superUserReadable:"
        value="superUserReadable" />    
    <check-box-field name="superUserWritable"   
        debug-id="superUserWritable"
        title=":scriptcatalog-superUserWritable:"
        value="superUserWritable"/>
    <check-box-field name="restAllowed"
        title=":scriptcatalog-restAllowed:"
        value="restAllowed"
        debug-id="restAllowed" />
    <field name="settingsSet"
           title=":scriptcatalog-set:"
           presentation="listBoxWithEmptyOpt"
           debug-id="settingsSet"
           elements="settingsSet#elements"
           value="settingsSet"/>
</form>