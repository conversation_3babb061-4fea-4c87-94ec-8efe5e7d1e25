changeAppParamParagraph1=Загрузка новой версии встроенного приложения "{0}". Используется в: "{1}" и т.д. (всего {2})
changeAppParamParagraph1Small=Загрузка новой версии встроенного приложения "{0}". Используется в: "{1}"
changeAppParamParagraph2=В новую версию встроенного приложения будут внесены изменения: {0} и т.д. (всего {1}) Продолжить загрузку?
changeAppParamParagraph2Small=В новую версию встроенного приложения будут внесены изменения:<br> {0} <br>Продолжить загрузку?
changeAssociationForm=Форма смены привязки
changeCaseForm=Форма смены типа
changeParamPossibleValueEvent=изменён список значений параметра
changeRequiredParamEvent=стал обязательным параметр
changeResponsibleForm=Форма смены ответственного
changeStateForm=Форма смены статуса
changeTypeParamEvent=изменён тип параметра
commentForm=Форма добавления и редактирования комментария
continueLoad=Продолжить загрузку?
deleteParamEvent=удален параметр
fileForm=Форма добавления и редактирования файла
newRequiredParamEvent=добавлен обязательный параметр
quickAddAndEditForm=Форма быстрого добавления и редактирования
userEventActionForm=Форма пользовательского действия по событию
