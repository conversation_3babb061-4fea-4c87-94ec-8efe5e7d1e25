<tab
    xmlns:guic="http://www.w3.org/2001/XMLSchema-instance"
    guic:noNamespaceSchemaLocation="../../../guic/server/schemas/guic.xsd"
    place-class="ru.naumen.metainfoadmin.client.script.ScriptCatalogPlace" 
    title=":scriptcatalog-scriptCatalog.byClasses:"
    name="ScriptsByClasses">
    <table-list name="scriptsByClasses"
        controller="ru.naumen.metainfoadmin.server.script.clazz.ScriptsByClassesListController"
        debug-id="byClassesContent"
        selection-enabled="false"
        filtering-enabled="false"
        paging-enabled="false">
        <column name="title"
            title=":title:" 
            width="300px"
            debug-id="@title"/>
        <column name="code"
            title=":code:" 
            width="300px"
            debug-id="@code"/>
        <column name="scriptsCount"
            title=":scriptcatalog-scriptCatalog.byCategories.scriptsCount:"
            debug-id="@scriptsCount"/>
    </table-list>
</tab>
