SCParametersMessages.serviceTreeList=Ієрархічний список (послуги)
SCParametersMessages.servicesFiltrationScript=Скрипт фільтрації послуг
SCParametersMessages.agreementsFiltration=Фільтрування угод під час редагування
SCParametersMessages.agreement=Угода
SCParametersMessages.agreementOrService=Угода чи послуга
SCParametersMessages.agreementService=Угода/послугу
SCParametersMessages.service=Послуга
SCParametersMessages.caseServiceCall=Тип запиту
SCParametersMessages.agreementServiceField=Поле "Угода/Послуга"
SCParametersMessages.agreementServiceValue=Значення поля "Угода/Послуга"
SCParametersMessages.agreementServiceOrCase=Визначальне поле при виборі угоди/послуги та типу запиту
SCParametersMessages.orderValue=Вибирати спочатку
SCParametersMessages.scParametersEditingOrder=Зміна параметрів запиту
SCParametersMessages.serviceCallParametersDescription=Використовується для налаштування можливостей прив''язки запитів
SCParametersMessages.flatList=Плаский список
SCParametersMessages.serviceCallParameters=Параметри запитів
SCParametersMessages.selectFromCatalog=Вибір із каталогу
SCParametersMessages.editPresentation=Подання для редагування
SCParametersMessages.client=Контрагент
SCParametersMessages.clientAutoResolveTitle=Автоматично заповнювати поточним користувачем
SCParametersMessages.scParametersEditingClient=Зміна параметрів поля "Контрагент"
SCParametersMessages.clientRequiredEditableTitle=Керувати обов''язковістю контрагента на рівні типу
SCParametersMessages.clientRequiredEditableAttention=Якщо “Контрагент” не вказано, для вибору будуть доступні всі “Угоди”.
SCParametersMessages.casesFilteringTitle=Поле "Тип запиту"
SCParametersMessages.casesFilteringParamCaption=Фільтрування типів під час редагування
SCParametersMessages.casesFilteringScriptCaption=Скрипт фільтрації типів
SCParametersMessages.casesFilteringParametersEditing=Зміна параметрів поля "Тип запиту"
SCParametersMessages.treeList=Ієрархічний список (угода та послуга)
SCParametersMessages.servicesFiltration=Фільтрування послуг під час редагування
SCParametersMessages.agreementsFiltrationScript=Скрипт фільтрації угод
SCParametersMessages.scParametersEditing=Зміна параметрів поля "Угода/Послуга"
SCParametersMessages.unconformedServiceCallsMessage=У системі є запити, для яких поточне значення поля "Угода/Послуга" не відповідає налаштуванню {0}: {1}
SCParametersMessages.unconformedClientMetaClassesMessage=У системі присутні класи/типи контрагентів, у яких поточне значення параметрів запиту за замовчуванням не відповідає параметру {0}: {1}. Для цих контрагентів прив''язка запиту за замовчуванням не діятиме.
SCParametersMessages.clientAutoResolveDescription=Якщо користувач ліцензований, контент "Вибір контрагента" відсутній на формі додавання, і URL не передано відповідний параметр, то контрагентом запиту встановлюється поточний користувач. Якщо користувач не ліцензований, поле "Контрагент" заповнюється автоматично незалежно від параметра.
