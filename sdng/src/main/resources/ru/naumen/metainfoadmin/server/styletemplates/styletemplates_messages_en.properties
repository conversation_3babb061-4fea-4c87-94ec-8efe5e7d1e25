styleTemplates=Style templates
styleTemplate=Style template "{0}"
cardBackLink=to style templates list

addTemplate=Add template
edit=Edit
delete=Delete

creationDate=Creation date
lastModifiedDate=Last modified date
set=Set
templateText=Template text
usagePlaces=Usage places

addFormTitle=Add template
editFormTitle=Edit template

deleteTemplate=Delete template
deleteTemplateCaption=Confirm delete
deleteTemplateMessage=Do you really want to delete style template: {0}?
usagePlacesWarning=The template is used in setting of the following event actions:<br><br>{0}<br><br>If you confirm the action, the value of the parameter "Template" of these event actions will be changed to "[without template]".

unableToAddNonUniqueCode=The style template with the code "{0}" cannot be added. The code must be unique.
resourceNotFound=The requested resource is not found. Use the navigation bar or enter the correct address in browser.

etc=etc
