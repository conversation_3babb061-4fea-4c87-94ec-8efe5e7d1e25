<form
    xmlns:guic="http://www.w3.org/2001/XMLSchema-instance"
    guic:noNamespaceSchemaLocation="../../../guic/server/schemas/guic.xsd"
    title=":styletemplates-editFormTitle:"
    controller="ru.naumen.metainfoadmin.server.style.templates.EditStyleTemplateFormController"
    fixed="false"
    name="EditStyleTemplate">
    <field name="title"
        presentation="textBox"
        title=":title:"
        debug-id="title" 
        required="true"
        validation="StringLength1000"
        value="title"
        max-length="1000" />
    <field name="code"
        presentation="textBox" 
        title=":code:"
        debug-id="code"
        enabled="false"
        value="code" />
    <field name="templateText" 
        presentation="richTextArea"
        title=":styletemplates-templateText:" 
        debug-id="templateText"
        required="true"
        value="templateText" />
    <field name="settingsSet"
           title=":styletemplates-set:"
           presentation="listBoxWithEmptyOpt"
           debug-id="settingsSet"
           elements="settingsSet#elements"
           value="settingsSet"/>
</form>
