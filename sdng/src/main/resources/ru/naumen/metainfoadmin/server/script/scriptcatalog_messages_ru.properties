AdvImportCategories.AFTER_IMPORT.title=Скрипт, выполняемый после импорта всех объектов
AdvImportCategories.AFTER_PROCESS.title=Скрипт, выполняемый после выполнения бизнес-процесса создания/редактирования объекта
AdvImportCategories.BEFORE_PROCESS.title=Скрипт, выполняемый перед выполнением бизнес-процесса создания/редактирования объекта
AdvImportCategories.BEFORE_PROCESS_ITEM.title=Скрипт, выполняемый перед формированием свойств бизнес-процесса
AdvImportCategories.CONVERTER_SCRIPT.title=Преобразование значения на основе скрипта
AdvImportCategories.IMPORT_FILTER.title=Фильтрация объектов на основе скрипта
AdvImportCategories.METACLASS_RESOLVER.title=Стратегия определения класса и типов создаваемых объектов
AttributeCategories.COMPUTABLE.title=Вычислимые атрибуты
AttributeCategories.COMPUTABLE_ON_FORM.title=Вычисление значений атрибутов при редактировании
AttributeCategories.DATE_TIME_RESTRICTION.title=Ограничение значений атрибутов типа дата, дата/время
AttributeCategories.DEFAULT_VALUE.title=Вычислимые значения по умолчанию атрибутов
AttributeCategories.FILTRATION.title=Фильтрация значений атрибутов при редактировании
EscalationCategories.CONDITION.title=Условия выполнения действий по эскалации
EscalationCategories.NOTIFICATION_CUSTOMIZATION.title=Кастомизация оповещений действий по эскалации
EscalationCategories.SCRIPT.title=Скрипты действий по эскалации
EventActionCategories.EVENTACTION_CONDITION.title=Условия выполнения действий по событию
EventActionCategories.EVENTACTION_INTEGRATION.title=Скрипты отправки данных во внешнюю очередь
EventActionCategories.EVENTACTION_NOTIFICATION_CUSTOMIZATION.title=Кастомизация оповещений действий по событию
EventActionCategories.EVENTACTION_SCRIPT.title=Скрипты действий по событию
EventActionCategories.EVENTACTION_WSMESSAGE_CUSTOMIZATION.title=Кастомизация сообщений о состоянии объекта
LearningProcessCategories.DATA_PREPARATION.title=Скрипты подготовки данных
LearningProcessCategories.GATHERING_DATA.title=Скрипты сборки данных
LearningProcessCategories.LEARNING_AND_VALIDATION.title=Скрипты обучения и тестирования
LearningProcessCategories.SAVE_DATA.title=Скрипты сохранения данных
MassDelete.dialog-caption=Подтверждение удаления
MassDelete.dialog-message=Вы действительно хотите удалить модули?<br><br>При удалении все скрипты, использующие методы модулей, будут работать некорректно.
OtherCategories.APPLICATION.title=Скрипты встроенных приложений
OtherCategories.CONSOLE.title=Консольные скрипты
OtherCategories.CTI.title=Правило обработки звонков
OtherCategories.FILTER_RESTRICTION.title=Скрипты для ограничений фильтрации
OtherCategories.MAIL_PROCESSOR_RULE.title=Правила обработки почты
OtherCategories.MONITORING.title=Скрипт обработки тревог
OtherCategories.PERMISSIONS.DefaultScriptTitle=Скрипт уточнения прав доступа № {0}
OtherCategories.PERMISSIONS.title=Уточнение прав доступа
OtherCategories.REPORT_TEMPLATE.title=Скрипты шаблонов отчетов и печатных форм
OtherCategories.SCHEDULER_TASK.title=Скрипты задач планировщика
OtherCategories.VOICE_PROCESSING.title=Скрипты обработки голосовых данных
OtherCategories.WITHOUT_CATEGORY.title=[Без категории]
ParameterCategories.PARAM_COMPUTABLE_ON_FORM.title=Вычисление значений параметров событий
ParameterCategories.PARAM_COMPUTE_ANY_CATALOG_ELEMENTS.title=Вычисление элементов справочника
ParameterCategories.PARAM_DATE_TIME_RESTRICTION.title=Ограничение значений параметров типа дата, дата/время
ParameterCategories.PARAM_DEFAULT_VALUE.title=Вычислимые значения по умолчанию параметров событий
ParameterCategories.PARAM_FILTRATION.title=Фильтрация значений параметров событий
RoleCategories.ACCESS.title=Определение прав доступа пользователя к объекту
RoleCategories.LIST_FILTER.title=Определение условия отбора объектов, доступных обладателю роли при поиске
RoleCategories.FAST_LINK_RIGHTS.title=Определение условия отбора объектов, доступных обладателю роли при упоминании
RoleCategories.OWNERS.title=Определение списка пользователей, обладающих ролью
SCParametersCategories=Параметры запросов: Поле "Соглашение/Услуга"
SC_PARAMETERS_AGREEMENT=Скрипт фильтрации соглашений при редактировании
SC_PARAMETERS_CASES=Скрипт фильтрации типов при редактировании
SC_PARAMETERS_SLM=Скрипт фильтрации услуг при редактировании
ScriptByCategory.cardTitle=Категория "{0}"
ScriptByClass.cardTitle=Класс "{0}" ({1})
ScriptModule.textIsHidden=Текст модуля недоступен.
SmiaModelCategories.POSTPROCESS.title=Скрипты постобработки ответа
SmiaModelCategories.PREPROCESS.title=Скрипты предобработки данных
TimerCategory.TIMER_CONDITION.title=Условия смены статусов счетчиков времени
WorkflowCategory.WF_SCRIPT_ACTION.title=Действия при входе/выходе из статуса
WorkflowCategory.WF_SCRIPT_CONDITION.title=Условия при входе/выходе из статуса
categoriesDefault=Категории (предполагаемые места использования)
categoriesUsages=Категории (места использования)
code=Код
delete=Удалить
deleteModuleCaption=Подтверждение удаления
deleteModuleMessage=Вы действительно хотите удалить модуль? При удалении все скрипты, использующие методы модуля, будут работать некорректно.
deleteNotPermittedError=Модули {0} не могут быть удалены: у Вас нет прав на удаление данных модулей.
description=Описание
documentation=Документация
edit=Редактировать
haveNoPermission=У Вас нет прав на выполнение этой операции
markerForProfile=Маркер прав "{0}" для профиля "{1}"
markerForVersioningProfile=Маркер прав "{0}" для профиля режима планирования "{1}"
moduleCardTitle=Модуль {0}
moduleCodeExistsError=Модуль не может быть добавлен. Модуль с таким кодом уже существует.
monitoring=Система мониторинга
newScript=[новый скрипт]
number=Номер
resourceNotFound=Запрашиваемый ресурс не найден. Воспользуйтесь навигационным меню или введите правильный адрес в адресную строку браузера.
restAllowed=Доступен для REST-запросов
scriptCardTitle=Скрипт {0}
scriptCatalog=Каталог скриптов
scriptCatalog.allScripts=Все скрипты
scriptCatalog.byCategories=По категориям
scriptCatalog.byCategories.scriptsCount=Количество скриптов
scriptCatalog.byClasses=По классам
scriptCatalog.noClass=[Без класса]
scriptCodeExistsError=Скрипт не может быть добавлен. Скрипт с кодом "{0}" уже существует.
scriptEdit=Редактирование скрипта
scriptLowerCase=скрипт
scriptModuleAddition=Добавление модуля
scriptModuleEdit=Редактирование модуля
scriptModules=Каталог модулей
scriptsAndModules=Каталог скриптов и модулей
set=Комплект
superUserReadable=Доступен для просмотра суперпользователями
superUserReadable.short=Просмотр
superUserWritable=Доступен для редактирования суперпользователями
superUserWritable.short=Редактирование
text=Текст
usagePointNotFoundError=Объект не может быть удален. Нарушена связь со скриптом с кодом {0}.
usagePoints=Настройки, где используется скрипт
version=Версия
withoutScript=[без скрипта]
