SCParametersMessages.treeList=Hierarchische Liste (Vereinbarung und Service)
SCParametersMessages.serviceTreeList=Hierarchische Liste (Dienste)
SCParametersMessages.servicesFiltrationScript=Dienstfilterungsskript
SCParametersMessages.servicesFiltration=<PERSON><PERSON><PERSON> von Diensten während der Bearbeitung
SCParametersMessages.agreementsFiltration=<PERSON><PERSON><PERSON> von Vereinbarungen während der Bearbeitung
SCParametersMessages.agreement=Vereinbarung
SCParametersMessages.agreementOrService=Vereinbarung oder Service
SCParametersMessages.agreementService=Vereinbarung/Service
SCParametersMessages.service=Service
SCParametersMessages.caseServiceCall=Typ des Tickets
SCParametersMessages.agreementsFiltrationScript=Skript zum Filtern von Vereinbarungen
SCParametersMessages.agreementServiceField=Feld "Vereinbarung und Service"
SCParametersMessages.agreementServiceValue=Wert des Feldes "Vereinbarung/Service"
SCParametersMessages.agreementServiceOrCase=Definierendes Feld bei der Auswahl von Vereinbarung/Service und Anfragetyp
SCParametersMessages.orderValue=Zuerst wählen
SCParametersMessages.scParametersEditing=Ändern der Einstellungen für das Feld „Vertrag/Service“.
SCParametersMessages.scParametersEditingOrder=Ticketsparameter ändern
SCParametersMessages.unconformedServiceCallsMessage=Es gibt Tickets im System, bei denen der aktuelle Wert des Feldes "Vereinbarung/Service" nicht mit der Einstellung {0} übereinstimmt: {1}
SCParametersMessages.serviceCallParametersDescription=Dient zur Einstellung von Bindungsmöglichkeiten für Tickets
SCParametersMessages.serviceCallParameters=Ticket-Parameter
SCParametersMessages.flatList=Flache Liste
SCParametersMessages.selectFromCatalog=Auswahl aus dem Katalog
SCParametersMessages.editPresentation=Ansicht zur Bearbeitung
SCParametersMessages.unconformedClientMetaClassesMessage=Es gibt Gegenparteiklassen/-typen im System, bei denen der aktuelle Standardwert der Abfrageparameter nicht mit der Einstellung {0} übereinstimmt: {1}. Für diese Gegenparteien wird die Standardabfrage nicht verbindlich sein.
SCParametersMessages.clientAutoResolveTitle=Automatisches Ausfüllen des aktuellen Benutzers
SCParametersMessages.client=Gegenpartei
SCParametersMessages.scParametersEditingClient=Ändern der Parameter des Feldes "Gegenpartei"
SCParametersMessages.clientAutoResolveDescription=Wenn der Benutzer lizenziert ist, der Inhalt "Wahl der Gegenpartei" auf dem Hinzufügen-Formular fehlt , und der entsprechende Parameter nicht in der URL übergeben wird, so wird der aktuelle Benutzer als Gegenpartei der Anfrage gesetzt. Wenn der Benutzer nicht lizenziert ist, wird das Feld „Gegenpartei“ unabhängig vom Parameter automatisch ausgefüllt.
SCParametersMessages.clientRequiredEditableTitle=Verwaltung der Gegenparteihaftung auf der Ebene des Typs
SCParametersMessages.clientRequiredEditableAttention=Wenn keine "Gegenpartei" angegeben wird, stehen alle "Vereinbarungen" zur Auswahl.
SCParametersMessages.casesFilteringTitle=Feld "Anfragetyp"
SCParametersMessages.casesFilteringParamCaption=Filtern von Typen bei der Bearbeitung
SCParametersMessages.casesFilteringScriptCaption=Skript zum Filtern von Typen
SCParametersMessages.casesFilteringParametersEditing=Ändern der Parameter des Feldes "Abfragetyp"
