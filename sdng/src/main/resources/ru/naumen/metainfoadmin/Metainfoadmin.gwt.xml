<?xml version="1.0" encoding="UTF-8"?>
<!--$Id$-->
<module>
    <inherits name="ru.naumen.metainfo.Metainfo" />
    <inherits name="ru.naumen.fts.Fts" />
    <inherits name="ru.naumen.objectlist.ObjectList"/>
	<inherits name="ru.naumen.dynaform.Dynaform"/>

	<source path="client" >
		<include name="**/*.java"/> 
        <exclude name="**/*JdkTest.java" />
    </source>
	<source path="shared">
		<include name="**/*.java"/> 
        <exclude name="**/*JdkTest.java" />
	</source>
    
    <replace-with class="ru.naumen.metainfoadmin.client.MaskedTextBoxAlertAdmin">
        <when-type-is class="ru.naumen.core.client.widgets.mask.MaskedTextBoxAlert" />
    </replace-with>
    
	 <stylesheet src="metainfoadmin.css"/>
</module>