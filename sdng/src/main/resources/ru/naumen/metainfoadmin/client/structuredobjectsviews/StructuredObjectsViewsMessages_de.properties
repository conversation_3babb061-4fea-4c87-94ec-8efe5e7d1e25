addStructuredObjectsView=Struktur hinzufügen
addingStructuredObjectsView=Hinzufügen einer Struktur
backToStructuredObjectsViews=zur Strukturliste
structuredObjectsViewItem=Strukturelement
structuredObjectsViewItem2=Element ''{0}'' der Struktur ''{1}''
structuredObjectsViewItems=Elemente der Struktur
showName=Den Namen des Strukturelements anzeigen
parentItem=Eingebettet in ein Element
addingStructuredObjectsViewItem=Hinzufügen eines Strukturelements
editingStructuredObjectsViewItem=Bearbeitung eines Strukturelements
structuredObjectsViewConfirmMassDelete=Wollen Sie die ausgewählten Strukturen wirklich löschen?
structuredObjectsViewItemConfirmDelete=Wollen Sie wirklich ein Element der Struktur ''{0}'' löschen?
structuredObjectsViewNotDelete=Die Struktur ''{0}'' kann aus folgenden Gründen nicht gelöscht werden:
attentionAllNestedItemsWillBeMoved=Alle verschachtelten Elemente werden zusammen mit dem aktuellen Element verschoben.
objectFilter=Begrenzung des Inhalts eines Elements
defaultSort=Standard-Sortierung
defaultSortAttention=Standardmäßig wird die im Strukturelement konfigurierte Sortierung verwendet.
editObjectFilter=Einstellung von Inhaltsbeschränkungen für Elemente
massOperation=Massenoperationen
editMassOperation=Konfigurieren der Verwendung des Massenoperationspanels in Strukturelementen
usedMassOperation=Werden verwendet
notUsedMassOperation=Nicht werden verwendet
editingStructuredObjectsView=Bearbeitung der Struktur
showNested=Verschachtelte Objekte in verschachtelten anzeigen
structuredObjectsViewConfirmDelete=Wollen Sie die Struktur ''{0}'' wirklich löschen?
allNestedItemsWillBeDeleted=Alle verschachtelten Elemente werden zusammen mit dem übergeordneten Element gelöscht: ''{0}''.<br>Möchten Sie mit dem Löschen fortfahren?
