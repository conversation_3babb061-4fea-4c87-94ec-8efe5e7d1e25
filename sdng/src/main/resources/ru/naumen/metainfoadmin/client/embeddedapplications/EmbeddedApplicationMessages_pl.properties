applicationAddress=Adres aplikacji
applicationHostedOnExternalServer=Aplikacja uruchomiona jest na zewnętrznym serwerze
applicationHelpText=Wbudowana aplikacja
applicationIsOff=Aplikacja jest wyłączona. Aby aplikacja była wyświetlana w interfejsie operatora, włącz ją.
wholeUrlIsDefinedByScript=URL jest całkowicie zdefiniowany przez skrypt
applicationNoServer=Aplikacja wykonywalna po stronie klienta
applicationHostedOnInternalServer=Aplikacja uruchomiona jest na wewnętrznym serwerze
applicationType=Typ aplikacji
applicationAdding=Dodawanie aplikacji
applicationEditing=Edytowanie aplikacji
application=aplikacja
goToEmbeddedApplications=do listy aplikacji
addApplication=Dodaj aplikację
applicationsGenitive=wybrane aplikacje
applicationProperty=Aplikacja
initialApplicationHeight=Wys<PERSON>ść początkowa aplikacji (px)
applicationFile=Plik aplikacji
lastConnectionStatus=Status ostatniego włączenia
lastConnectionDate=Data ostatniego włączenia
lastSuccessfulConnectionDate=Data ostatniego udanego włączenia
selectApplicationFile=Określ aplikacje, których pliki mają być pobrane
silentModeIsOn=Aplikacja jest wyłączona. Włączony jest Silent Mode.
methodOfUrlDefinition=Metoda określania adresu URL aplikacji
parametersAreDefinedByScript=Parametry są zdefiniowane przez skrypt
parametersAreDefinedBySystemLogic=Parametry są zdefiniowane przez logikę systemu
fullscreenAllowed=Pozwalać na ujawnienie na całą stronę
mobileApplicationHeight=Wysokość treści w aplikacji mobilnej
