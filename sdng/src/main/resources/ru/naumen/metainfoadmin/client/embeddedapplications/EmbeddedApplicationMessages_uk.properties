applicationAddress=Адреса програми
applicationHostedOnInternalServer=Додаток запущений на внутрішньому сервері
applicationHostedOnExternalServer=Додаток запущений на зовнішньому сервері
applicationNoServer=Додаток, що виконується на стороні клієнта
applicationType=Тип додатку
applicationAdding=Додавання додатку
applicationEditing=Редагування додатку
application=додаток
goToEmbeddedApplications=до списку додатків
addApplication=Додати додаток
applicationsGenitive=вибрані програми
applicationHelpText=Вбудований додаток
applicationIsOff=Програму вимкнено. Щоб програма відображалася в інтерфейсі оператора, увімкніть його.
applicationProperty=Додаток
initialApplicationHeight=Вихідна висота програми (px)
applicationFile=Файл додатку
lastConnectionStatus=Статус останнього ввімкнення
lastConnectionDate=Дата останнього ввімкнення
lastSuccessfulConnectionDate=Дата останнього успішного ввімкнення
selectApplicationFile=Зазначте програми, файли яких потрібно вивантажити
silentModeIsOn=Додаток вимкнено. Увімкнено Silent Mode.
methodOfUrlDefinition=Метод визначення URL додатку
wholeUrlIsDefinedByScript=URL повністю визначається скриптом
parametersAreDefinedByScript=Параметри визначаються скриптом
parametersAreDefinedBySystemLogic=Параметри визначаються системною логікою
fullscreenAllowed=Дозволяти розкривати на всю сторінку
mobileApplicationHeight=Висота контенту в мобільному додатку (pt)
