applicationAddress=Standort-Adresse
applicationHostedOnInternalServer=Die Anwendung läuft auf einem internen Server
applicationHostedOnExternalServer=Die Anwendung läuft auf einem externen Server.
addApplication=Anwendung hinzufügen
addingUsagePlace=Verwendungsort hinzufügen
addUsagePlace=Verwendungsort hinzufügen
applicationAdding=Anwendung hinzufügen
application=App
applicationCustomLoginForm=Login-Formular für die mobile App
applicationEditing=Bewerbungsbearbeitung
applicationFile=Bewerbungsmappe
applicationHelpText=Integrierte Anwendung
applicationIsOff=Die Anwendung ist deaktiviert. Um die Anwendung in der Bedienerschnittstelle anzuzeigen, aktivieren Sie sie.
applicationNoServer=Auf der Client-Seite laufende Anwendung
applicationProperty=Anwendung
fullscreenAllowed=Auf ganze Seite erweitern lassen
applicationType=Anwendungstyp
applicationsGenitive=ausgewählte Anwendungen
availableForms=Verfügbare Formulare
editingUsagePlace=Bearbeitung des Ortes der Verwendung
goToEmbeddedApplications=zur Anwendungsliste
initialApplicationHeight=Initiale Anwendungshöhe (px)
lastConnectionStatus=Status des letzten Einschaltens
lastSuccessfulConnectionDate=Datum der letzten erfolgreichen Aktivierung
mobileApplicationHeight=Inhaltshöhe der mobilen App (pt)
lastConnectionDate=Datum des letzten Einschaltens
methodOfUrlDefinition=Methode zur Bestimmung der Anwendungs-URL
parametersAreDefinedByScript=Parameter werden durch das Skript definiert
scriptModule=Skript-Modul
parametersAreDefinedBySystemLogic=Parameter werden durch die Systemlogik bestimmt
selectApplicationFile=Geben Sie die Anwendungen an, deren Dateien hochgeladen werden sollen
silentModeIsOn=Die App ist ausgeschaltet. Der Lautlos-Modus ist eingeschaltet.
transitions=Übergänge zwischen Status
typeOfForm=Art des Modalformulars
wholeUrlIsDefinedByScript=Die URL wird vollständig vom Skript bestimmt
usagePlace=Ort der Nutzung
usagePlaces=Orte, an denen die Anwendung in modalen Formularen verwendet werden kann
userEventActionFormTitle=Benutzerdefiniertes Formular für Ereignisaktionen
availableUserEventActions=Verfügbare benutzerdefinierte Ereignisaktionen
