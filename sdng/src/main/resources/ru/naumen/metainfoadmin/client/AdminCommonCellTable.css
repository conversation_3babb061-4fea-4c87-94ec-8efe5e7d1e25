
@external .table__row__icons, .link, .vectorIcon;

@eval tableRowBackgroundHover ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().tableRowBackgroundHover();
@eval tableHeaderColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().tableHeaderColor();

.cellTableWidget {
	padding-top: 0;
	padding-bottom: 10px;
	}
.cellTableCell {
	font-size: 13px !important;
	vertical-align: middle;
	}

.cellTableLastColumn,
.cellTableLastColumnFooter,
.cellTableLastColumnHeader {
	padding-right: 8px !important;
	}

.cellTableHeader {
	color: tableHeaderColor;
	text-align: left;
	text-shadow: none;
	font-weight: normal;
	}
	.cellTableSortableHeader {
		cursor: pointer;
		}
.cellTableEvenRow:hover,
.cellTableOddRow:hover {
	background: tableRowBackgroundHover;
	}
.itemRemoved > .cellTableCell,
.itemRemoved .link,
.folderRowRemoved > .cellTableFirstColumn,
.folderRowRemoved > .cellTableCell,
.folderRowRemoved .link {
	color: #808080 !important;
	}
	.itemRemoved .vectorIcon svg path {
        fill: #bbb !important;
        }
.folderRow,
.folderRowRemoved,
.folderRow:hover,
.folderRowRemoved:hover {
	background: #f3f3f3;
	}
	.folderRow .titleColumn,
	.folderRow > .cellTableFirstColumn{
		color: #597398;
		}
.alignCenter {
	text-align: center;
	}

.alignCenterTransition {
	text-align: center;
	}
    .alignCenterTransition span:before {
	    display: inline;
	    }

.cellWithLongText {
	max-width: 300px;
	min-width: 200px;
    }

.itemAttentionRow {
    background-color: #FFF3BB;
   	}