addUserAttribute=Додавання атрибуту користувача
aggregateClasses=Агрегувати класи
aggregatingAttributes=Агрегувати класи
attributeParamsChanging=Зміна параметрів атрибуту
caseAtributes=Атрибути типу
caseProperties=Властивості типу
classAtributes=Атрибути класу
classProperties=Властивості класу
composite=Складовий
compositeValue=Складове
computableOnForm=Обчислення значення під час редагування
computableOnFormScript=Скрипт обчислення значення під час редагування
complexAttrGroup=Група атрибутів у списку
complexRelation=Розширене редагування зв''язків
defaultByScript=Обчислюване
description=Опис
determinableByValueMap=Визначається за таблицею відповідностей
directLink=Пряме посилання
editable=Редагований
editableInLists=Редагований у списках
editAttribute=Редагування атрибуту
editPresentation=Подання для редагування
filterWhileEditing=Фільтрування значень під час редагування
inheritParams=Наслідувати параметри
inputmask=Маска введення
inputmaskMode=Режим маски введення
inputmaskModeAlias=Псевдонім
inputmaskModeDefinitions=Маска зі скороченнями
inputmaskModeRegex=Регулярний вираз
intervalAvailableUnits=Одиниці вимірювання доступні під час редагування
mandatoryInInterface=Обов''язковий для заповнення в інтерфейсі
needStoreUnits=Запам''ятовувати вибрані одиниці виміру
mandatory=Обов''язковий
objectClass=Клас об''єкта
ruleToDetermine=Правило визначення
script=Скрипт
selectSorting=Сортування списку
showPresentation=Подання для відображення
systemAttributes=Системні атрибути
template=Шаблон
timerDefinition=Лічильник часу
unique=Унікальний
userAttributes=Атрибути користувача
useSystemParams=Використовувати системні параметри
hideWhenEmpty=Приховувати при відображенні, якщо не заповнено
hideWhenNo=Приховувати при відображенні, якщо значення "Ні"
hideWhenZero=Приховувати при відображенні, якщо значення "0"
exportNDAP=Доступний із системи моніторингу
hideCaptionAttribute=Приховувати назву атрибуту
hideArchived=Приховувати архівні об''єкти
inputmaskRegexHelp=При вибраному режимі маски ''Регулярний вираз'' у полі ''Маска введення'' вказується регулярний вираз, який буде застосовано для валідації введеного рядка при введенні символів, виході з поля та збереженні форми введення. Якщо символ, що вводиться, згідно з регулярним виразом, не може стояти на цьому місці рядка, то він не потрапить у поле. Якщо після закінчення введення введений вираз не підходитиме під умови регулярного виразу, користувач побачить помилку валідації.
inputmaskAttributeValidationMessage=Невірний формат введення. Вказівки щодо заповнення поля описані в розділі [Довідка].
checkBoxDefValue=Значення за промовчанням "{0}"
complexEmplAttrGroup=Група атрибутів у списку для класу Співробітник
complexOuAttrGroup=Група атрибутів у списку для класу Відділ
complexTeamAttrGroup=Група атрибутів у списку для класу Команда
quickAddForm=Форма швидкого додавання
quickEditForm=Форма швидкого редагування
yes=Так
no=Ні
attributeParameters=Параметры атрибуту
defaultValueByScript=Обчислюване значення за замовчуванням
defaultValue=Значення за замовчуванням
complexFormEmplAttrGroup=Розширене редагування зв''язку для класу Співробітник
complexFormOuAttrGroup=Розширене редагування зв''язку для класу Відділ
complexFormTeamAttrGroup=Розширене редагування зв''язку для класу Команда
type=Тип
clazz=Клас
attribute=Атрибут
catalog=Довідник
timer=Лічильник
attributeValue=Значення атрибуту
hides=Приховується
linkedTo=Посилається на
determinedBy=Особливість обчислення
filteringOnEdit=Фільтрується під час редагування
calculatingOnEdit=Обчислюється під час редагування
calculatingByScript=Обчислюється скриптом
determinableByCorrespondanceTable=За таблицею відповідностей
determinableByNameRules=За правилом іменування
required=Обов''язковий
requiredInInterface=Обов''язковий для заповнення в інтерфейсі
needStoreUnitsInfo=Якщо ознака виставлена, при відображенні значення використовуватиметься одиниця вимірювання, яку вказав користувач під час редагування. Інакше значення буде наводитися до найбільшої одиниці виміру, в якій можна уявити значення в цілісному вигляді.
needStoreUnitsAttention=Інформація про одиниці вимірювання, введені користувачами раніше, буде втрачена. Тимчасовий інтервал буде приводиться до найбільшої доступної одиниці виміру, в якій значення можна у цілочисленному вигляді. Ви дійсно хочете змінити параметр?
relatedAttrsToExport=Атрибути, доступні із системи моніторингу
hideWhenNoPossibleValues=Приховувати під час редагування, якщо немає значень для вибору
inputmaskAliasHelp=<b>Синтаксис маски</b>: При вибраному режимі маски ''Псевдонім'' у полі ''Маска введення'' вказується назва псевдоніму. Псевдонім впливає на:<br/>\
<ul style=''list-style-type:disc''>\
<li>зовнішній вигляд порожнього поля введення зі встановленим курсором - у ньому відображається підказка, в якому вигляді потрібно ввести значення</li>\
<li>реакцію поля на введення неприпустимих символів, автоматичне встановлення службових символів</li>\
<li>валідацію значення при закінченні введення</li>\
</ul><br/>\
Повний список доступних стандартних псевдонімів наведено в документації до системи, нижче наведені деякі варіанти, що часто використовуються.<br/><br/>\
<table>\
<tr>\
<td style=''-moz-user-select:text;vertical-align:top;''>\
<b>Загальні псевдоніми</b> :<br/>\
<ul style=''list-style-type:disc''>\
<li>ip - ip-адреса виду ***********</li>\
<li>email - адреса електронної пошти</li>\
<li> mac - стандартна 12-символьна мережна адреса. Наприклад, FD:98:DF:DF:F5:6D</li>\
</ul><br/>\
</td><td style=''-moz-user-select:text;vertical-align:top;''>\
<b>Псевдоніми для чисельних значень</b>:<br/>\
<ul style=''list-style-type:disc''>\
<li>decimal - дробове число</li>\
<li>integer - ціле число< /li>\
<li>currency - число з обмеженням дробової частини до 2 знаків</li>\
<li>currencyWithGroups - число з поділом цілої частини на розряди та обмеженням дробової частини до 2 знаків</li>\
<li>% - дробове число від 0 до ста зі знаком %</li>\
</ul><br/>\
</td></tr></table>
inputmaskDefinitionsHelp=<b>Синтаксис маски</b>: \
При вибраному режимі маски ''Маска зі скороченнями'' в полі ''Маска вводу'' вказується рядок, який може містити як звичайні символи, \
так і спеціальні скорочення. Звичайні символи вставляються в маску введення як шаблони і не можуть бути з неї виключені. \
Символи-скорочення служать позначення допустимих значень. \
Наприклад, в масці ''MCК-9999'' підрядок ''МСК-'' не містить спецсимволів і буде розпізнаний, як шаблон, а підрядок ''9999'' буде розпізнаний, \
як 4 цифри поспіль. Користувач зможе ввести рядки МСК-1234 чи МСК-0000. \
Повний список доступних скорочень наведено в документації до системи, нижче наведені деякі варіанти, що часто використовуються.<br/><br/>\
<b>Загальні скорочення</b>:<br>\
<ul style=''list-style-type:disc''>\
<li>9 - одна цифра</li>\
<li>a - одна буква будь-якого регістру</li>\
<li>A - одна буква, що автоматично приводиться до верхнього регістру</li>\
<li>* - одна цифра або буква будь-якого регістру</li>\
<li>& - одна цифра або буква, що автоматично приводиться до верхнього регістру</li>\
<li># - шістнадцяткова цифра, 0-F</li>\
</ul>\
<b>Скорочення для дат та часу:</b><br>\
<ul style=''list-style-type:disc''>\
<li>h - годинник</li>\
<li>s - секунди або хвилини</li>\
<li >d - дні</li><li>m - місяці</li>\
<li>y - рік</li>\
</ul>\
Таким чином, маска введення ''ymd h:s:s'' вимагатиме від користувача введення дати у форматі ''рік.місяць.день години:хвилини:секунди'', \
в якій мають бути заповнені всі символи. Наприклад, ''2015.01.01 23:59:59''\
<br><br>\
<b>Доступні спецсимволи</b>:<br>\
<ul style=''list-style-type:disc''>\
<li> Угруповання: ( і )</li>\
<li>Об''єднання АБО: |. Наприклад, (aaa) | (999). Відповідні значення - ''abc'', ''123''.</li>\
<li>Необов''язковість: [ та ]. Наприклад, 99[99]-999. Відповідні значення - ''12-345'', ''1234-567''.</li>\
<li>Динаміка: '{'n'}' - n повторень; '{'n, m'}' - від n до m повторень; '{'+'}' - починаючи з 1; '{'*'}' - починаючи з 0. Наприклад, aa-9'{'1,4'}'. Відповідні значення - ''bc-34'', ''sd-1234''</li>\
<li>Екранування: скасовує спеціальне значення для символу. Наприклад, 99-ААА999. Відповідні значення: #12-ASK654</li>\
</ul>
determinableByNumbersFormationRule=Атрибут пов''язаного класу
linkAttribute=Атрибут зв''язку
attibuteOfRelatedClass=Атрибут пов''язаного класу
displayValueWithHierarchy=Показувати значення атрибута
buildHierarchyFrom=Побудувати ієрархію починаючи від
levelOfHierarchy=Рівень ієрархії
parentHierarchy0=Пов''язаного об''єкта
parentHierarchy1=Батьків пов''язаного об''єкта
parentHierarchy2=Батька 2-го рівня пов''язаного об''єкта
parentHierarchy3=Батька 3-го рівня пов''язаного об''єкта
parentHierarchyN=Батька {0}-ого рівня зв''язаного об''єкта
parentHierarchyTop=Батьків верхнього рівня
sortByTitle=За назвою
sortByCode=За кодом
sortByValueType=За типом призначення
sortSystemFirst=Спочатку системні
sortUserFirst=Спочатку користувацькі
sortBy=Сортувати за:
dateTimeRestrictionScript=Скрипт обмеження значення атрибуту
dateTimeRestrictionType=Додаткове обмеження на введення дати
dateTimeCommonRestrictions=Значення атрибута можна вказувати
dateTimeRestrictionAttribute=Атрибут
dateTimeRestrictionCondition=Умова
attributeUsedInOtherRestriction=Для поточного атрибуту недоступне налаштування обмеження на введення дати, оскільки цей атрибут використовується в налаштуваннях обмеження атрибута "{0}".
attributeUsageRestrictionPlace=Налаштування обмеження атрибута "{0}".
dateTimeAttributeRestriction=Встановлено обмеження "{0} атрибута {1}".
dateTimeScriptRestriction=Обмеження скриптом
formDateTimeCommonRestrictions=Значення параметра можна вказувати
formDateTimeRestrictionScript=Скрипт обмеження значення параметра
formDateTimeRestrictionAttribute=Параметр
dateTimeAttributeRestrictionInfo={0} атрибута {1}
computeAnyCatalogElementsScript=Скрипт обчислення елементів довідника
hasGroupSeparators=Розділяти за розрядами
digitsCountRestrictions=Обмеження на введення десяткових знаків
advlistSemanticFiltering=Фільтрування у складних списках з урахуванням морфології
structuredObjectsView=Структура
complexRelationType=Тип списку
complexRelationType[false]=Вимкнено
complexRelationType[flat]=Плаский список
complexRelationType[hierarchy]=З використанням структури
editOnComplexFormOnly=Редагування лише через розширену форму
