toSameCls=в об''єкт свого класу
addClassDialogCaption=Додавання класу
namingRule=Правило формування атрибута "{0}" (title)
numerationRule=Правило формування атрибуту "{0}" (number)
help=[Довідка]
classCodeDuplication=Код класу має бути унікальним. Клас із таким кодом вже існує.
typeCodeDuplication=Код типу має бути унікальним у межах класу. Тип із таким кодом вже існує.
addCaseDialogCaption=Додавання типу до класу ''{0}''
hasResponsiblePropertyCaption=Призначення відповідального
copyClassDialogCaption=Копіювання класу
editClassDialogCaption=Зміна класу
hasWorkflowPropertyCaption=Життєвий цикл
editCaseDialogCaption=Зміна типу
copyCaseDialogCaption=Копіювання типу
parentType=Батьківський тип
parentMetaclass=Об''єкти вкладені в
attributesBackLinksNotCopy=При копіюванні класу ''{0}'' не буде скопійовано наступні атрибути типів "Зворотне посилання" та "Атрибут зв''язаного об''єкта": {1}.
etc=і т.д.
responsibilityTransferTableEnabled=Контролювати передачу відповідальності між командами
toNestedItselfMetaClassAttention=\n1. При збереженні змін об''єкти класу ''{0}'' не можна буде створювати з "Списків об''єктів" та "Списків пов''язаних об''єктів".\n2. Зміни можуть тривати тривалий час.
toNonNestedfMetaClassAttention=\n1. При збереженні змін всі "Списки вкладених об''єктів" класу ''{0}'' будуть видалені.\n2. Буде видалено атрибут ''{1}'' (клас ''{0}''). Інформація з атрибута не може бути відновлена!\n3. В атрибутах типу "Атрибут пов''язаного об''єкта", що посилаються на поточний клас, параметр "Показувати значення атрибута" буде скинуто до значення за промовчанням.\n4. Зміни можуть тривати тривалий час.
addWorkflowAttention=\n1. Додавання життєвого циклу - тривала операція, під час якої метаінформація буде недоступна змін.\n2. Після додавання зворотна операція буде неможливою. Видалити життєвий цикл із класу не можна.
confirmOperation=Підтвердження операції
addWorkflowRequestTimedOut=Операція додавання життєвого циклу до класу "{0}" продовжиться у фоновому режимі, оскільки перевищено час очікування відповіді від сервера.<br />До повного завершення операції робота з метаінформацією неможлива.
addWorkflowRequestTimedOutTitle=Перевищено час очікування
helpDigitsCountRestriction=Для введених даних при відображенні буде використовуватися математичне округлення до зазначених у параметрі одиниць, при цьому значення в базі даних не змінюється.
confirmAddWorkflowQuestion=Увага.<br />У системі працюють користувачі. При роботі з формами вони не зможуть відправити введені дані, що може призвести до втрати введених даних.<br />Настійно рекомендуємо додавати життєвий цикл, коли в системі немає користувачів, оскільки операція може тривати тривалий час.<br /><br / >Додати життєвий цикл у клас {0} зараз?
resetUI=Скинути налаштування
toLeft=Зліва
delTabCaption=Видалення вкладки
ouAttributeGroup=Група атрибутів відділу
editUI=Редагувати налаштування
relObjectClass=Клас пов''язаного об''єкту
toRight=Праворуч
addingContent=Додавання контенту
toFull=На всю ширину
place=форми {0}
deleteTabMessage=При видаленні вкладки будуть видалені всі розміщені на ній контенти
emplAttributeGroup=Група атрибутів працівника
teamAttributeGroup=Група атрибутів команди
resetToSystemBtn=Відновити системні установки
newForm=додавання
clientInfo=Інформація про користувача
additionalContacts=Додаткові контактні особи
delLastTabError=Не можна видаляти останню вкладку
tabGenitive=вкладку
contentProfiles=Профілі
contentVersProfiles=Профілі режиму планування
editForm=редагування
removed=(арх.) {0}
contentType=Тип контенту
position=Розташування
addTabDialogCaption=Додавання нової вкладки
addNewGroup=Додати групу
