addApplication=Add application
addUsagePlace=Add usage place
addingUsagePlace=Adding usage place
application=application
applicationAdding=Application adding form
applicationAddress=Application address
applicationCustomLoginForm=Login form in the mobile application
applicationEditing=Application editing form
applicationFile=Application file
applicationHelpText=Built in application
applicationHostedOnExternalServer=Application hosted on external server
applicationHostedOnInternalServer=Application hosted on internal server
applicationIsOff=Application is off. Turn it on to see it in operator interface.
applicationNoServer=Client side application
applicationProperty=Application
applicationType=Application type
applicationsGenitive=selected applications
availableForms=Available forms
availableUserEventActions=Available user event actions
editingUsagePlace=Editing usage place
fullscreenAllowed=Allow to open on fullscreen
goToEmbeddedApplications=to the applications list
initialApplicationHeight=Initial application height (px)
lastConnectionDate=Last switching date
lastConnectionStatus=Last switching state
lastSuccessfulConnectionDate=Last successful switching date
methodOfUrlDefinition=Method of the application URL definition
mobileApplicationHeight=Content height in mobile application (pt)
parametersAreDefinedByScript=The parameters are defined by the script
parametersAreDefinedBySystemLogic=The parameters are defined by system logic
scriptModule=Script module
selectApplicationFile=Select applications to download their files
silentModeIsOn=Application is off. Silent Mode is on
transitions=Transitions
typeOfForm=Modal form type
usagePlace=usage place
usagePlaces=Application usage places on modal forms
userEventActionFormTitle=Form of user event action
wholeUrlIsDefinedByScript=The URL is entirely defined by the script
