addCaseDialogCaption=Добавление типа в класс ''{0}''
addClassDialogCaption=Добавление класса
addGroupDialogCaption=Добавление группы атрибутов
addNewGroup=Добавить группу
addTabDialogCaption=Добавление новой вкладки
addWorkflowAttention=\n1. Добавление жизненного цикла - длительная операция, на время которой метаинформация будет недоступна для изменений.\n2. После добавления обратная операция будет невозможна. Удалить жизненный цикл из класса нельзя.
addWorkflowRequestTimedOut=Операция добавления жизненного цикла в класс "{0}" продолжится в фоновом режиме, так как превышено время ожидания ответа от сервера.<br>До полного завершения операции работа с метаинформацией невозможна.
addWorkflowRequestTimedOutTitle=Превышено время ожидания
addingContent=Добавление контента
addingMarker=Добавление маркера прав
additionalContacts=Дополнительные контактные лица
attributeIsUsedInTabTitle=Атрибут "{0}" используется в названии вкладки браузера для карточек объектов класса {1}. При удалении атрибута название вкладки будет формироваться с использованием названия объекта.
attributeIsUsedInTabTitleMultiple=Атрибут "{0}" используется в названии вкладки браузера для карточек объектов классов {1}. При удалении атрибута название вкладки будет формироваться с использованием названия объекта.
attributesBackLinksNotCopy=При копировании класса ''{0}'' не будут скопированы следующие атрибуты типов "Обратная ссылка" и "Атрибут связанного объекта": {1}.
classCodeDuplication=Код класса должен быть уникален. Класс с таким кодом уже существует.
clientInfo=Информация о пользователе
confirmAddWorkflowQuestion=Внимание.<br>В системе работают пользователи. При работе с формами они не смогут отправить введенные данные, что может привести к потере введенных данных.<br>Настоятельно рекомендуем добавлять жизненный цикл, когда в системе нет пользователей, так как операция может занимать длительное время.<br><br>Добавить жизненный цикл в класс {0} сейчас?
confirmDeleteGroup=Вы действительно хотите удалить группу атрибутов ''{0}''?
confirmOperation=Подтверждение операции
contentProfiles=Профили
contentType=Тип контента
contentVersProfiles=Профили режима планирования
copyCaseDialogCaption=Копирование типа
copyClassDialogCaption=Копирование класса
delLastTabError=Нельзя удалять последнюю вкладку
delTabCaption=Удаление вкладки
deleteTabMessage=При удалении вкладки будут удалены все расположенные на ней контенты
deletionImpossible=Группа атрибутов ''{0}'' не может быть удалена. Группа используется в настройках {1}
editAttrGroupDialogCaption=Редактирование группы атрибутов
editCaseDialogCaption=Изменение типа
editClassDialogCaption=Изменение класса
editForm=редактирования
editUI=Редактировать настройки
editingMarker=Редактирование маркера прав
emplAttributeGroup=Группа атрибутов сотрудника
etc=и т.д.
groupAttributes=Атрибуты, входящие в группу
hasResponsiblePropertyCaption=Назначение ответственного
hasWorkflowPropertyCaption=Жизненный цикл
help=[Справка]
helpDigitsCountRestriction=Для ранее введенных данных при отображении будет использоваться математическое округление до указанных в параметре единиц, при этом значение в базе данных не изменяется.
namingRule=Правило формирования атрибута "{0}" (title)
newForm=добавления
numerationRule=Правило формирования атрибута "{0}" (number)
ouAttributeGroup=Группа атрибутов отдела
overrideTabTitleAttribute=Переопределить название вкладки браузера для карточки объекта
parentMetaclass=Объекты вложены в
parentType=Родительский тип
place=формы {0}
position=Расположение
quotaExpirationDate=Срок действия
quotaName=Название квоты
quotaRemainder=Доступный остаток
quotingEnabled=Ограничивается квотой
relObjectClass=Класс связанного объекта
removed=(арх.) {0}
resetSettings=Сбросить настройки
resetToSystemBtn=Восстановить системные настройки
resetUI=Сбросить настройки
responsibilityTransferTableEnabled=Контролировать передачу ответственности между командами
selectAttributes=Изменить набор
settingsSet=Комплект
tabGenitive=вкладку
tabTitleAttribute=Использовать в названии вкладки значение атрибута
teamAttributeGroup=Группа атрибутов команды
toFull=На всю ширину
toLeft=Слева
toNestedItselfMetaClassAttention=\n1. При сохранении изменений объекты класса ''{0}'' нельзя будет создавать из "Списков объектов" и "Списков связанных объектов". \n2. Изменения могут занять длительное время.
toNonNestedfMetaClassAttention=\n1. При сохранении изменений все "Списки вложенных объектов" класса ''{0}'' будут удалены. \n2. Будет удален атрибут ''{1}'' (класс ''{0}''). Информация из атрибута не может быть восстановлена! \n3. В атрибутах типа "Атрибут связанного объекта", ссылающихся на текущий класс, параметр "Показывать значение атрибута" будет сброшен к значению по умолчанию. \n4. Изменения могут занять длительное время.
toRight=Справа
toSameCls=в объект своего класса
typeCodeDuplication=Код типа должен быть уникален в рамках класса. Тип с таким кодом уже существует.
