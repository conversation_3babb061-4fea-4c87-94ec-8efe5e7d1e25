toSameCls=zu einem Objekt seiner Klasse
addCaseDialogCaption=Hinzufügen eines Typs zur Klasse ''{0}''
groupAttributes=Gruppenattribute
addGroupDialogCaption=Hinzufügen einer Attributgruppe
addClassDialogCaption=Hinzufügen einer Klasse
addingMarker=Hinzufügen einer Rechte-Markierung
namingRule=Regel für die Bildung des Attributs "{0}" (Titel)
addNewGroup=Gruppe hinzufügen
addTabDialogCaption=Hinzufügen eines neuen Tabs
resetSettings=Einstellungen zurücksetzen
editingMarker=Bearbeiten einer Rechtemarkierung
deletionImpossible=Die Attributgruppe ''{0}'' kann nicht entfernt werden. In den Einstellungen verwendete Gruppe {1}
addWorkflowAttention=\n1. Das Hinzufügen eines Workflow ist ein langwieriger Vorgang, während dessen die Metainformationen nicht für Änderungen verfügbar sind.\n2. Nach dem Hinzufügen ist der umgekehrte Vorgang nicht möglich. Sie können den Workflow nicht aus einer Klasse entfernen.
addWorkflowRequestTimedOut=Der Vorgang des Hinzufügens eines Workflow zur Klasse "{0}" wird im Hintergrund fortgesetzt, da der Server beim Warten auf eine Antwort das Zeitlimit überschritten hat.<br>Es ist nicht möglich, mit Metainformationen zu arbeiten, bis der Vorgang abgeschlossen ist.
addWorkflowRequestTimedOutTitle=Zeitüberschreitung überschritten
addingContent=Inhalte hinzufügen
editAttrGroupDialogCaption=Bearbeiten einer Attributgruppe
confirmDeleteGroup=Möchten Sie die Attributgruppe ''{0}'' wirklich löschen?
selectAttributes=Satz ändern
additionalContacts=Weitere Ansprechpartner
attributeIsUsedInTabTitle=Das Attribut "{0}" wird im Titel des Browser-Tabs für Karten von Objekten der Klasse {1} verwendet. Wenn das Attribut entfernt wird, wird der Name des Tabs anhand des Objektnamens gebildet.
attributeIsUsedInTabTitleMultiple=Das Attribut "{0}" wird im Titel des Browser-Tabs für Karten der Klassenobjekte {1} verwendet. Wenn das Attribut entfernt wird, wird der Tab-Name unter Verwendung des Objektnamens generiert.
attributesBackLinksNotCopy=Beim Kopieren der Klasse ''{0}'' werden die folgenden Attribute der Typen "Rückverweis" und "Attribut des verknüpften Objekts" nicht kopiert: {1}.
classCodeDuplication=Klassencode muss eindeutig sein. Klasse mit diesem Code existiert bereits.
clientInfo=Nutzerinformation
contentType=Inhaltstyp
confirmAddWorkflowQuestion=Achtung.<br>Benutzer arbeiten im System. Wenn sie mit Formularen arbeiten, können sie die eingegebenen Daten nicht übermitteln, was zum Verlust der eingegebenen Daten führen kann.<br>Es wird dringend empfohlen, einen Workflow als Operation hinzuzufügen, wenn keine Benutzer im System vorhanden sind kann sehr lange dauern.<br><br>Workflow jetzt zur Klasse {0} hinzufügen?
contentVersProfiles=Profile des Planungsmodus
confirmOperation=Bestätigen des Vorgangs
contentProfiles=Profile
copyCaseDialogCaption=Typ kopieren
copyClassDialogCaption=Kopieren einer Klasse
delLastTabError=Letzter Tab kann nicht gelöscht werden
delTabCaption=Entfernen eines Tabs
deleteTabMessage=Wenn Sie eine Tabs entfernen, werden alle darauf befindlichen Inhalte gelöscht
editCaseDialogCaption=Änderung des Typs
editClassDialogCaption=Ändern der Klasse
editForm=Bearbeitung
editUI=Einstellungen bearbeiten
emplAttributeGroup=Mitarbeiter-Attributgruppe
etc=usw.
hasResponsiblePropertyCaption=Bestellung eines Verantwortlichen
hasWorkflowPropertyCaption=Workflow
help=[Referenz]
helpDigitsCountRestriction=Bei zuvor eingegebenen Daten wird auf dem Display eine mathematische Rundung auf die im Parameter angegebenen Einheiten vorgenommen, der Wert in der Datenbank ändert sich jedoch nicht.
newForm=Hinzufügungen
numerationRule=Attributgenerierungsregel "{0}" (number)
ouAttributeGroup=Gruppe von Abteilungsattributen
quotaName=Name der Quote
quotaRemainder=Verfügbares Guthaben
overrideTabTitleAttribute=Browser-Tab-Name für Objektkarte überschreiben
parentMetaclass=Objekte sind eingeschlossen in
parentType=Elterntyp
place=Formen {0}
position=Standort
quotaExpirationDate=Gültigkeitsdauer
quotingEnabled=Begrenzt auf eine Quote
resetToSystemBtn=Systemeinstellungen wiederherstellen
relObjectClass=Klasse des verknüpften Objekts
removed=(arch.) {0}
resetUI=Einstellungen zurücksetzen
toRight=Rechts
responsibilityTransferTableEnabled=Überwachung der Übertragung von Verantwortung zwischen Teams
typeCodeDuplication=Der Typencode muss innerhalb der Klasse eindeutig sein. Ein Typ mit diesem Code existiert bereits.
toFull=Volle Breite
toLeft=Links
teamAttributeGroup=Team-Attributgruppe
tabGenitive=Tab
tabTitleAttribute=Wert des Attributs im Tab-Namen verwenden
toNestedItselfMetaClassAttention=\n1. Wenn Änderungen gespeichert werden, können Objekte der Klasse ''{0}'' nicht aus ''Objektlisten'' und ''Listen verbundener Objekte'' erstellt werden. \n2. Änderungen können sehr lange dauern.
toNonNestedfMetaClassAttention=\n1. Beim Speichern der Änderungen werden alle "Verschachtelten Objektlisten" der Klasse ''{0}'' gelöscht. \nDas Attribut ''{1}'' (Klasse ''{0}'') wird gelöscht. Die Informationen aus dem Attribut können nicht wiederhergestellt werden! \n3. Bei Attributen vom Typ ''Attribut des verknüpften Objekts'', die sich auf die aktuelle Klasse beziehen, wird der Attributwertparameter ''Attributwert anzeigen'' auf seinen Standardwert zurückgesetzt. \n4. Änderungen können sehr lange dauern.
