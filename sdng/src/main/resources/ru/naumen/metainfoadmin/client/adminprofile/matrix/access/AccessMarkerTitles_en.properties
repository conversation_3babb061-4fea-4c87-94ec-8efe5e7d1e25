title={0}
title[accessRights]=Access rights
title[activeUsers]=List of active users
title[administratorInterface]=Administrator interface
title[administrationProfiles]=Administration profiles
title[administrationProfilesManagement]=Administration profile linking management
title[adminLiteSettings]=Lite administrator interface settings
title[adminLogs]=Admin logs
title[advLists]=Advanced lists
title[applicationLogs]=Application logs
title[archivingMetaclass]=Archiving a metaclass
title[catalogs]=Catalogs
title[changeTrackingSettings]=Track changes in real time
title[connectionsAndConfigurations]=Connections and configurations
title[console]=Console
title[cti]=CTI
title[customizationFiles]=Customization files
title[databaseManagement]=Database management
title[dropdownSelectionLists]=Dropdown selection lists
title[embeddedApplications]=Work with embedded applications
title[escalations]=Escalations
title[eventActions]=Event Actions
title[executeConsoleScripts]=Execute console scripts
title[exportStatistics]=Export statistics
title[exportSystemInformation]=Export system information
title[filePreview]=File Preview
title[importAdditionalLibraries]=Import additional libraries
title[importCertificates]=Import certificates
title[importExportEmbeddedApplications]=Import and export embedded applications
title[importExportExtensionsForMaskedSettings]=Import and export extensions for masked settings
title[importExportLicenseFiles]=Import and export license files
title[importExportMetainfoAndReportTemplates]=Import and export metainfo and report templates
title[interfaceAndNavigation]=Interface and navigation
title[localization]=Localization
title[loginAsEmployee]=Login as an employee
title[mail]=Mail
title[maintenance]=Blocking the entrance during maintenance work
title[manageProfilesOnForms]=Manage profiles on forms
title[mobileApplications]=Mobile applications
title[operatorInterface]=Operator interface
title[otherSettings]=Other settings
title[queues]=Queues
title[reindexing]=Reindexing
title[runSchedulerTask]=Run scheduler task
title[scheduler]=Scheduler
title[scParameters]=Service call parameters
title[scripts]=Script settings
title[searchSettings]=Search settings
title[securityGroup]=User groups
title[securityPolicy]=Security Policy
title[securityRole]=Roles
title[settingsSets]=Sets
title[structures]=Structures
title[superUsers]=List of superusers and technologists
title[systemCatalogs]=System catalogs
title[tags]=Tags
title[templates]=Work with templates
title[timers]=Timers
title[userCatalogs]=User catalogs
title[userInterface]=User interface
title[workflow]=Workflow