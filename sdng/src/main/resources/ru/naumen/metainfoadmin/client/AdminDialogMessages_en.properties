addCaseDialogCaption=Add type to the class ''{0}''
addClassDialogCaption=Add class
addGroupDialogCaption=Add attribute group
addNewGroup=Add group
addTabDialogCaption=Add new tab
addWorkflowAttention=\n1. Adding a workflow is a long operation, all that time metainformation will not be available for changes.\n2. After adding, the reverse operation is not possible. Workflow removal is not available.
addWorkflowRequestTimedOut=Adding workflow to class "{0}" will proceed in background mode because response time from the server has been exceeded.<br>Metainformation will not be available for changes, until the operation is complete.
addWorkflowRequestTimedOutTitle=Timed out
addingContent=Add content
addingMarker=Add permissions marker
additionalContacts=Additional contact persons
attributeIsUsedInTabTitle=The attribute "{0}" is used in the browser tab title for object cards of class {1}. When deleting an attribute, the name of the browser tab title will be formed using the name of the object.
attributeIsUsedInTabTitleMultiple=The attribute "{0}" is used in the browser tab title for object cards of classes {1}. When deleting an attribute, the name of the browser tab title will be formed using the name of the object.
attributesBackLinksNotCopy=When copy the class "{0}" the following attributes of the "Back link", "Attribute of related object" types will not be copied: {1}.
classCodeDuplication=Class code must be unique. Class with this code already exists.
clientInfo=User information
confirmAddWorkflowQuestion=Attention.<br>Users are working in system. They will not be able to save entered data on forms, which can cause data loss.<br>It is highly recommended to perform adding without users in system, because adding a workflow is a long operation.<br><br>Add workflow to class {0} anyway?
confirmDeleteGroup=Do you really want to delete the attribute group ''{0}''?
confirmOperation=Operation confirmation
contentProfiles=Profiles
contentType=Content type
contentVersProfiles=Versioning profiles
copyCaseDialogCaption=Copy type
copyClassDialogCaption=Copy class
delLastTabError=You cannot delete the last tab
delTabCaption=Delete tab
deleteTabMessage=When you delete the tab, all the contents located on it will be deleted
deletionImpossible=The attribute group ''{0}'' cannot be deleted. Group is used in settings {1}
editAttrGroupDialogCaption=Edit attribute group
editCaseDialogCaption=Change type
editClassDialogCaption=Edit class
editForm=edit
editUI=Edit settings
editingMarker=Edit permissions marker
emplAttributeGroup=Employee attribute group
etc=etc.
groupAttributes=Attributes in group
hasResponsiblePropertyCaption=Assign responsible
hasWorkflowPropertyCaption=Workflow
help=[Help]
helpDigitsCountRestriction=For previously entered data, the will use mathematical rounding to the units indicated in the parameter, and the value in the database will not change.
namingRule=Naming rule (attribute "{0}" (title)
newForm=add
numerationRule=Numeration rule (attribute "{0}" (number)
ouAttributeGroup=Department attribute group
overrideTabTitleAttribute=Override browser tab title for the object card
parentMetaclass=Objects are nested into
parentType=Parent type
place=of the form {0}
position=Position
quotaExpirationDate=Validity period
quotaName=Name of quota
quotaRemainder=Available remainder
quotingEnabled=Limited by quota
relObjectClass=Class of related object
removed=(arch.) {0}
resetSettings=Reset settings
resetToSystemBtn=Restore system settings
resetUI=Reset settings
responsibilityTransferTableEnabled=Check the transfer of responsibility between the teams
selectAttributes=Change set
settingsSet=Set
tabGenitive=tab
tabTitleAttribute=Use attribute value in the tab title
teamAttributeGroup=Team attribute group
toFull=Full width
toLeft=Left
toNestedItselfMetaClassAttention=\n1. When you save the changes, objects of class ''{0}'' cannot be created from "Object list" and "Lists of related objects". \n2. Changes may take a long time.
toNonNestedfMetaClassAttention=\n1. When you save the changes, all "Lists of nested objects" of class ''{0}'' will be removed. \n2. Deletes the attribute ''{1}'' (class ''{0}''). This information could not be restored! \n3. The parameter ''Show attribute value'' will be reset to the default value in attributes of the type ''Attribute of the related object'' that refer to the current class. \n4. Changes may take a long time.
toRight=Right
toSameCls=to object of the same class
typeCodeDuplication=Type code must be unique within the class. Type with this code already exists.
