addStructuredObjectsView=Add structure
addingStructuredObjectsView=Adding structure
addingStructuredObjectsViewItem=Adding item structure
allNestedItemsWillBeDeleted=The following nested items: ''{0}'' will be deleted with the parent item.<br>Do you really want to continue?
attentionAllNestedItemsWillBeMoved=All nested items will be moved together with this item.
backToStructuredObjectsViews=to structures
defaultSort=Default sort
defaultSortAttention=The sorting settings from the structure element are used by default.
editMassOperation=Configuring the use of the mass operations panel in structure elements
editObjectFilter=Restriction on the contents of a structure element
editingStructuredObjectsView=Editing structure
editingStructuredObjectsViewItem=Editing item structure
massOperation=Mass operations
notUsedMassOperation=Not used
objectFilter=Restriction on the contents of a structure element
parentItem=Nested in item
showName=Show structure element name
showNested=Show objects nested in nested
structuredObjectsViewConfirmDelete=Do you really want to delete the structure ''{0}''?
structuredObjectsViewConfirmMassDelete=Do you really want to delete selected structures?
structuredObjectsViewItem=Structure item
structuredObjectsViewItem2=Item ''{0}'' of structure ''{1}''
structuredObjectsViewItemConfirmDelete=Do you really want to delete the structure item ''{0}''?
structuredObjectsViewItems=Structure items
structuredObjectsViewNotDelete=Structure ''{0}'' cannot be deleted for the following reasons:
usedMassOperation=Used
