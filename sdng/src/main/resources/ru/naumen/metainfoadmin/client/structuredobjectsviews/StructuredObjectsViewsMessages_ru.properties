addStructuredObjectsView=Добавить структуру
addingStructuredObjectsView=Добавление структуры
addingStructuredObjectsViewItem=Добавление элемента структуры
allNestedItemsWillBeDeleted=Вместе с родительским элементом будут удалены все вложенные в него элементы: ''{0}''.<br>Продолжить удаление?
attentionAllNestedItemsWillBeMoved=Вместе с текущим элементом будут перемещены все вложенные в него элементы.
backToStructuredObjectsViews=к списку структур
defaultSort=Сортировка по умолчанию
defaultSortAttention=По умолчанию используется сортировка, настроенная в элементе структуры.
editMassOperation=Настройка использования панели массовых операций в элементах структуры
editObjectFilter=Настройка ограничения содержимого элемента
editingStructuredObjectsView=Редактирование структуры
editingStructuredObjectsViewItem=Редактирование элемента структуры
massOperation=Массовые операции
notUsedMassOperation=Не используются
objectFilter=Ограничение содержимого элемента
parentItem=Вложен в элемент
showName=Отображать название элемента структуры
showNested=Показывать объекты, вложенные во вложенные
structuredObjectsViewConfirmDelete=Вы действительно хотите удалить структуру ''{0}''?
structuredObjectsViewConfirmMassDelete=Вы действительно хотите удалить выбранные структуры?
structuredObjectsViewItem=Элемент структуры
structuredObjectsViewItem2=Элемент ''{0}'' структуры ''{1}''
structuredObjectsViewItemConfirmDelete=Вы действительно хотите удалить элемент структуры ''{0}''?
structuredObjectsViewItems=Элементы структуры
structuredObjectsViewNotDelete=Структура ''{0}'' не может быть удалена по следующим причинам:
usedMassOperation=Используются
