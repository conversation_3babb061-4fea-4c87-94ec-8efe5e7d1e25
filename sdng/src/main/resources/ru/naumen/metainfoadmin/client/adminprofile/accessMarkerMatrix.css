/*
 * Страница предназначена для кастомизации стилей "table-style-2", "buttonsGroup"
 * для отображения соотв. стилей в матрице маркеров доступа профиля администратора.
 * Кастомизация происходит за счёт введения дополнительных css-классов "matrix" и "actionButtonToolBar"
 * назначаемых тегам, являющимися предками для тега с css-классом "table-style-2" и "buttonsGroup" соответственно.
 */
@external .*;

.matrix {
}

.matrix .table-style-2 {
    border-bottom: 1px solid #D9D9D9;
    border-bottom: none;
}

.matrix .table-style-2 td {
    width: 150px;
}

.matrix > table > tbody > tr > td:first-child {
    padding: 0;
    border-right: 1px solid #d9d9d9;
}

.matrix #gwt-debug-tableData td:last-child {
	padding: 0;
}

.matrix .table-style-2 .cellContainer > div {
	display: flex;
	align-items: center;
	word-wrap: anywhere;
	margin-top: 7px;
	margin-left: 8px;
}

.matrix .table-style-2 .cellContainer .gwt-Label {
	margin-bottom: 9px;
	line-height: 14px;
}

.matrix .table-style-2 .cellContainer {
	display: flex;
	flex-direction: column;
}

.matrix .table-style-2 .tableStyle2CellFirstColumn {
	padding: 0;
	width: 10px !important;
}

.actionButtonToolBar {
}

.actionButtonToolBar .buttonsGroup td:first-child {
	padding-right: 30px;
}