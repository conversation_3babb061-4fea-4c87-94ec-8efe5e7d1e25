addStructuredObjectsView=Додати структуру
addingStructuredObjectsView=Додавання структури
editingStructuredObjectsView=Редагування структури
backToStructuredObjectsViews=до списку структур
structuredObjectsViewItem=Елемент структури
structuredObjectsViewItems=Елементи структури
structuredObjectsViewItem2=Елемент ''{0}'' структури ''{1}''
showName=Відображати назву елемента структури
parentItem=Вкладено в елемент
addingStructuredObjectsViewItem=Додавання елемента структури
editingStructuredObjectsViewItem=Редагування елемента структури
showNested=Показувати об''єкти, вкладені у вкладені
structuredObjectsViewConfirmDelete=Ви дійсно хочете видалити структуру ''{0}''?
structuredObjectsViewConfirmMassDelete=Ви дійсно хочете видалити вибрані структури?
structuredObjectsViewItemConfirmDelete=Ви хочете видалити елемент структури ''{0}''?
allNestedItemsWillBeDeleted=Разом з батьківським елементом буде видалено всі вкладені в нього елементи: ''{0}''.<br>Продовжити видалення?
structuredObjectsViewNotDelete=Структура ''{0}'' не може бути видалена з наступних причин:
attentionAllNestedItemsWillBeMoved=Разом з поточним елементом буде переміщено всі вкладені в нього елементи.
objectFilter=Обмеження вмісту елемента
defaultSort=Сортування за замовчуванням
editObjectFilter=Налаштування обмеження вмісту елемента
massOperation=Масові операції
editMassOperation=Налаштування використання панелі масових операцій в елементах структури
usedMassOperation=Використовуються
notUsedMassOperation=Не використовуються
