addApplication=Добавить приложение
addUsagePlace=Добавить место использования
addingUsagePlace=Добавление места использования
application=приложение
applicationAdding=Добавление приложения
applicationAddress=Адрес приложения
applicationCustomLoginForm=Форма входа в мобильном приложении
applicationEditing=Редактирование приложения
applicationFile=Файл приложения
applicationHelpText=Встроенное приложение
applicationHostedOnExternalServer=Приложение запущено на внешнем сервере
applicationHostedOnInternalServer=Приложение запущено на внутреннем сервере
applicationIsOff=Приложение отключено. Чтобы приложение отображалось в интерфейсе оператора, включите его.
applicationNoServer=Приложение, исполняемое на стороне клиента
applicationProperty=Приложение
applicationType=Тип приложения
applicationsGenitive=выбранные приложения
availableForms=Доступные формы
availableUserEventActions=Доступные пользовательские действия по событию
editingUsagePlace=Редактирование места использования
fullscreenAllowed=Позволять раскрывать на всю страницу
goToEmbeddedApplications=к списку приложений
initialApplicationHeight=Исходная высота приложения (px)
lastConnectionDate=Дата последнего включения
lastConnectionStatus=Статус последнего включения
lastSuccessfulConnectionDate=Дата последнего успешного включения
methodOfUrlDefinition=Метод определения URL приложения
mobileApplicationHeight=Высота контента в мобильном приложении (pt)
parametersAreDefinedByScript=Параметры определяются скриптом
parametersAreDefinedBySystemLogic=Параметры определяются системной логикой
scriptModule=Скриптовый модуль
selectApplicationFile=Укажите приложения, файлы которых необходимо выгрузить
silentModeIsOn=Приложение отключено. Включён Silent Mode.
transitions=Переходы между статусами
typeOfForm=Тип модальной формы
usagePlace=место использования
usagePlaces=Места использования приложения на модальных формах
userEventActionFormTitle=Форма пользовательского действия по событию
wholeUrlIsDefinedByScript=URL полностью определяется скриптом
