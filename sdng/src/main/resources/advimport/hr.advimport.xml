<?xml version="1.0" encoding="UTF-8"?>
<!-- Конфигурация импорта оргструктуры из HR -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="../../../../target/classes/advimport/schema1.xsd"
	save-log="true" threads-number="4" >

	<mode>CREATE</mode>
	<mode>UPDATE</mode>

	<!-- Параметры импорта -->

	<!-- Задает метакласс импортируемых отделов -->
	<parameter name="ouMetaClass">ou$office</parameter>

	<!-- Здает код атрибута отделов, в котором хранится внешний идентификатор -->
	<parameter name="ouIdHolder">idHolder</parameter>

	<!-- Задает метакласс импортируемых сотрудников -->
	<parameter name="employeeMetaClass">employee$management</parameter>

	<!-- Здает код атрибута сотрудников, в котором хранится внешний идентификатор -->
	<parameter name="employeeIdHolder">idHolder</parameter>

	<!-- Задает код отдела являющегося корневым отделом импорта -->
	<parameter name="rootOu">0001</parameter>

	<!-- Источник импортируемых отделов -->
	<parameter name="ouSource">http://hr.naumen.ru/backend/sd_export_divisions</parameter>

	<!-- Источник импортируемых сотрудников -->
	<parameter name="employeeSource">http://hr.naumen.ru/backend/sd_export_users</parameter>

	<!-- Импорт -->

	<!-- Импорт отделов -->
	<class name="ou" threads-number="1">

		<parameter name="metaClass">${ouMetaClass}</parameter>
		<parameter name="idHolder">${ouIdHolder}</parameter>

		<csv-data-source id-column="id" with-header="true" file-name="${ouSource}" encoding="UTF-8" delimiter=";">
			<column name="id" src-key="id" />
			<column name="parent" src-key="parent_id" />
			<column name="title" src-key="name" />
		</csv-data-source>

		<hierarchical-filter parent-column="parent" />
		<constant-metaclass-resolver />
		<object-searcher />

		<attr name="${idHolder}" column="id" />
		<attr name="title" column="title" />
		<attr name="parent" column="parent" default-value="${rootOu}">
			<object-converter />
		</attr>
	</class>

	<!-- Импорт сотрудников -->
	<class name="employee" threads-number="4">

		<parameter name="metaClass">${employeeMetaClass}</parameter>
		<parameter name="idHolder">${employeeIdHolder}</parameter>

		<csv-data-source id-column="id" with-header="true" file-name="${employeeSource}" encoding="UTF-8"
			delimiter=";">
			<column name="id" src-key="id" />
			<column name="parent" src-key="division_id" />
			<column name="login" src-key="email" />
			<column name="firstName" src-key="f_name" />
			<column name="middleName" src-key="s_name" />
			<column name="lastName" src-key="l_name" />
			<column name="email" src-key="email" />
			<column name="locationName" src-key="location_name" />
			<column name="roomNumber" src-key="room_number" />
			<column name="post" src-key="pos_name" />
			<column name="internalPhoneNumber" src-key="inner_phone" />
		</csv-data-source>

		<hierarchical-filter parent-column="parent" />
		
		<constant-metaclass-resolver />
		<object-searcher />

		<attr name="${idHolder}" column="id" />
		<attr name="login" column="login" />
		<attr name="firstName" column="firstName" />
		<attr name="middleName" column="middleName" />
		<attr name="lastName" column="lastName" />
		<attr name="email" column="email" />
		<attr name="locationName" column="locationName" />
		<attr name="roomNumber" column="roomNumber" />
		<attr name="post" column="post" />
		<attr name="internalPhoneNumber" column="internalPhoneNumber" />
		<attr name="parent" column="parent">
			<object-converter attr="${ouIdHolder}" metaclass="${ouMetaClass}" required="true" />
		</attr>
	</class>

	<!-- Назначение сотрудников руководителями отделов -->
	<class name="ou" threads-number="4">

		<parameter name="metaClass">${ouMetaClass}</parameter>
		<parameter name="idHolder">${ouIdHolder}</parameter>

		<csv-data-source id-column="id" with-header="true" file-name="${ouSource}" encoding="UTF-8" delimiter=";">
			<column name="id" src-key="id" />
			<column name="head" src-key="chief_id" />
		</csv-data-source>

		<column-notempty-filter column="head" />

		<constant-metaclass-resolver />
		<object-searcher />

		<attr name="head" column="head">
			<object-converter attr="${employeeIdHolder}" metaclass="${employeeMetaClass}" />
		</attr>
	</class>
</config>