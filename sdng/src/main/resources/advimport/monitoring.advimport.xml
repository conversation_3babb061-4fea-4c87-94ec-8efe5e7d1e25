<?xml version="1.0" encoding="UTF-8"?>
<!-- Конфигурация импорта ресурсов из Naumen Network Manager -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
    xsi:noNamespaceSchemaLocation="../../../../target/classes/advimport/schema1.xsd"
    save-log="true" threads-number="1">

    <mode>CREATE</mode>
    <mode>UPDATE</mode>

    <!-- Параметры импорта -->

    <!-- Задает метакласс импортируемых отделов -->
    <parameter name="metaClass">resource$printer</parameter>

    <!-- Здает код атрибута отделов, в котором хранится внешний идентификатор -->
    <parameter name="idHolder">idHolder</parameter>

    <!-- Маска группы импортируемых утсройств -->
    <parameter name="groupMask">users.admin.devgroups.printerGroup</parameter>

    <!-- Импорт -->

    <!-- Импорт ресурсов -->
    <class name="resource" threads-number="1">

        <parameter name="metaClass">${metaClass}</parameter>
        <parameter name="idHolder">${idHolder}</parameter>

        <monitoring-data-source 
            mask="${groupMask}"
            process-group-elements="true"
            id-column="id">
            <column name="id" src-key="path" />
              
            <!-- название принтера вычисляется с помощью запроса к серверу -->
            <column name="title" src-key="SELECT * FROM
                (SELECT data.hrDeviceTable\$hrDeviceDescr || ' (' || ip.ipAddrTable\$ipAdEntAddr || ')' as name FROM  
                    ${context}:hrDeviceTable as data, ${context}:ipAddrTable as ip 
                 WHERE data.hrDeviceTable\$hrDeviceType = 'hrDevicePrinter' and ip.ipAddrTable\$ipAdEntAddr &lt;&gt; '127.0.0.1' 
                 UNION ALL
                SELECT data2.hrDeviceTable\$hrDeviceDescr || ' (' || ip2.ipAddrTable\$ipAdEntAddr || ')' as name FROM
                    ${context}:hrDeviceTable as data2, ${context}:ipAddrTable as ip2 
                 WHERE ip2.ipAddrTable\$ipAdEntAddr &lt;&gt; '127.0.0.1') as q
                 LIMIT 1" />
       
        </monitoring-data-source>

        <constant-metaclass-resolver />
        <object-searcher />

        <attr name="${idHolder}" column="id" />
        <attr name="title" column="title" />
    </class>

</config>