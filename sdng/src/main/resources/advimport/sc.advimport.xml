<?xml version="1.0" encoding="UTF-8"?>
<!-- Пример конфигурации импорта запросов: основан на импорте запросов для миграции с 3.8 на 4.0 для Полюс-Золото -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../target/classes/advimport/schema1.xsd">

	<mode>CREATE</mode>

	<gui-parameter name="file" type="FILE" title="File for import" />

	<class name="serviceCall" threads-number="3">

		<csv-data-source file-name="${file}" with-header="true" delimiter="," encoding="UTF8" id-column="id">
			<column name="id" src-key="UUID" />
			<column name="clientTitle" src-key="ClientTitle" />
			<column name="clientName" src-key="ClientName" />
			<column name="clientEmail" src-key="ClientEmail" />
			<column name="clientPhone" src-key="ClientPhone" />
			<column name="number" src-key="Id" />
			<column name="title" src-key="Title" />
			<column name="requestDate" src-key="RequestDate" />
			<column name="registrationDate" src-key="RegistrationDate" />
			<column name="case" src-key="Case" />
			<column name="agreementTitle" src-key="ClientContractTitle" />
			<column name="serviceTitle" src-key="ClientServiceTitle" />
			<column name="Category" src-key="Category" />
			<column name="criticality" src-key="Criticality" />
			<column name="urgency" src-key="Urgency" />
			<column name="description" src-key="Description" />
			<column name="state" src-key="Stage" />
			<column name="author" src-key="Author" />
			<column name="responsibleEmployee" src-key="ResponsibleEmployee" />
			<column name="solvedByEmployee" src-key="SolvedByPerson" />
			<column name="closedByEmployee" src-key="ClosedByPerson" />
			<column name="codeOfClosing" src-key="CodeOfClosing" />
			<column name="massProblem" src-key="MassiveProblem" />
			<column name="lastSolved" src-key="LastSolved" />
			<column name="closureTime" src-key="ClosureTime" />
			<column name="AskChangeDeadli" src-key="AskChangeDeadline" />
			<column name="BE" src-key="BE" />
			<column name="BusinessUnit" src-key="BusinessUnit" />
			<column name="employees" src-key="Employees" />
			<column name="MarkQuality" src-key="Mark_quality" />
			<column name="OptionalEquipme" src-key="OptionalEquipment" />
			<column name="OptionalEquipSt" src-key="OptionalEquipmentString" />
			<column name="Place" src-key="Place" />
			<column name="Razdel" src-key="Razdel" />
			<column name="SentMessage" src-key="Sent_message" />
			<column name="Text" src-key="ClosedByPerson" />
			<column name="closedByEmployee" src-key="Text" />
			<column name="Time" src-key="Time" />
			<column name="TypeOfSupport1" src-key="Type_of_support_1" />
			<column name="Vopros" src-key="Vopros" />
			<column name="block" src-key="block" />
			<column name="priority" src-key="call_priority" />
			<column name="client1" src-key="client" />
			<column name="comnata" src-key="comnata" />
			<column name="desiredDeadline" src-key="desiredDeadline" />
			<column name="fio" src-key="fio" />
			<column name="laboriousness" src-key="laboriousness" />
			<column name="otdel" src-key="otdel" />
			<column name="parent1" src-key="parent" />
			<column name="po" src-key="po" />
			<column name="position" src-key="position" />
			<column name="project" src-key="project" />
			<column name="startTime" src-key="start_date" />
			<!-- <column name="СounterpartyVKS" src-key="СounterpartyVKS" /> -->
			<!-- <column name="СounterpartyVKSString" src-key="СounterpartyVKSString" /> -->
		</csv-data-source>

		<!-- Входящие данные для значений являющихся NULL содержат текст "null". Заменяем его на NULL -->
		<script-filter><![CDATA[
            for(def column in item.properties.propertyNames()) {
                def value = item.properties.getProperty(column);
                if ("null" == value) {
                    item.properties.setProperty(column, null);
                }
            }
            return true;
        ]]></script-filter>

		<script-metaclass-resolver><![CDATA[
            def callCase = item.properties.case.trim();
            
            if ("ASBNY" == callCase) {
                return 'serviceCall$ASBNY';
            }
            if ("request_for_system_change" == callCase) {
                return 'serviceCall$rfsc';
            }
            if ("VKC" == callCase) {
                return 'serviceCall$VKC';
            }
            if ("WorkplaceCreating" == callCase) {
                return 'serviceCall$WorkplaceCreati';
            }
            if ("ks_service_request" == callCase) {
                return 'serviceCall$ksServiceReques';
            }
            if ("service_request" == callCase) {
                return 'serviceCall$serviceRequest';
            }
            if ("ITsupport" == callCase) {
                return 'serviceCall$incident';
            }
            if ("Consultation_user" == callCase) {
                return 'serviceCall$ConsultUser';
            }
            if ("Email" == callCase) {
                return 'serviceCall$Email';
            }
            return 'serviceCall$consultation';
        ]]></script-metaclass-resolver>

		<!-- Атрибуты основной привязки -->
		<!-- <attr name="clientLinkName" column="clientTitle" /> -->
		<attr name="clientName" column="clientName" />
		<attr name="clientEmail" column="clientEmail" />
		<attr name="clientPhone" column="clientPhone" />
		<attr name="client" column="clientEmail">
			<!-- В реальном импорте клиенты уже должны существовать в базе. Использовать этот конвертер -->
			<!-- object-converter attr="email" metaclass="employee" required="true" / -->

			<!-- Тестовая база. Создаем клиентов если их нет -->
			<!-- <script-converter><![CDATA[ -->
			<!-- def existed = utils.get('employee', ['email' : value]); -->
			<!-- if (null != existed) { -->
			<!-- return existed; -->
			<!-- } -->
			<!-- def ou = utils.get('ou', ['title' : 'test']); -->
			<!-- return utils.create('employee$employee', ['lastName' : item.properties.clientName, -->
			<!-- 'email' : value, -->
			<!-- 'parent' : ou]); -->
			<!-- ]]></script-converter> -->
			<script-converter><![CDATA[
                return utils.get('employee', ['lastName' : 'test']);
            ]]></script-converter>
		</attr>
		<attr name="agreement" column="agreementTitle">
			<object-converter attr="title" metaclass="agreement" required="false" />
		</attr>
		<attr name="service" column="serviceTitle">
			<object-converter attr="title" metaclass="slmService" required="false" />
		</attr>

		<!-- Атрибуты Запроса -->
		<attr name="title" column="title" />
		<attr name="number" column="number" />
		<attr name="description" column="description" />

		<attr name="requestDate" column="requestDate">
			<datetime-converter format="dd.MM.yyyy HH:mm:ss" />
		</attr>
		<attr name="registrationDate" column="registrationDate">
			<datetime-converter format="dd.MM.yyyy HH:mm:ss" />
		</attr>

		<attr name="massProblem" column="massProblem" />

		<attr name="impact" column="criticality">
			<script-converter><![CDATA[
                def codeMap = ['SCC1' : '1', 'SCC2' : '2', 'SCC3' : '3'];
                def code = codeMap[value];
                return null == code ? null : utils.get('impact', ['code' : code]);
			]]></script-converter>
		</attr>
		<attr name="urgency" column="urgency">
			<object-converter attr="code" metaclass="urgency" required="false" />
		</attr>
		<attr name="codeOfClosing" column="codeOfClosing">
			<complex-object-converter>
                <script-converter><![CDATA[
                    def codeMap = ['1_Well_done' : '1WellDone', 'Well_done' : 'WellDone', 'IT BE' : 'ITBE'];
                    def code = codeMap[value];
                    return  utils.get("closureCode", ['code' : null == code ? value : code]);
                ]]></script-converter>
			</complex-object-converter>
		</attr>
		<attr name="priority" column="priority" />

		<attr name="author" column="author">
			<object-converter attr="login" metaclass="employee" required="false" />
		</attr>

		<attr name="responsibleEmployee" column="responsibleEmployee">
			<object-converter attr="login" metaclass="employee" required="false" />
		</attr>
		<attr name="solvedByEmployee" column="solvedByEmployee">
			<object-converter attr="login" metaclass="employee" required="false" />
		</attr>
		<attr name="closedByEmployee" column="closedByEmployee">
			<object-converter attr="login" metaclass="employee" required="false" />
		</attr>

		<!-- Пользовательские атрибуты Класса Запрос -->
		<attr name="BusinessUnit" column="BusinessUnit" />
		<attr name="Time" column="Time" />
		<attr name="MarkQuality" column="MarkQuality" />
		<attr name="TypeOfSupport1" column="TypeOfSupport1">
			<object-converter attr="title" metaclass="TypeOfSupport" required="false" />
		</attr>

		<!-- Пользовательские атрибуты "Запрос на доработку корп. системы" -->
		<metaclass-attrs>
			<metaclass>serviceCall$rfsc</metaclass>

			<attr name="project" column="project">
				<script-converter><![CDATA[
                    def codeMap = ['1' : '0001', '2' : '0002', '3' : '0003'];
                    def code = codeMap[value];
                    return null == code ? null : utils.get('Projects', ['code' : code]);
                ]]></script-converter>
			</attr>
		</metaclass-attrs>

		<!-- Пользовательские атрибуты "Запрос на доработку корпоративной системы" -->
		<metaclass-attrs>
			<metaclass>serviceCall$ASBNY</metaclass>

			<attr name="Text" column="Text" />
			<attr name="SentMessage" column="SentMessage" />
			<attr name="Razdel" column="Razdel" />
			<attr name="Client1" column="client1" />
		</metaclass-attrs>

		<!-- Пользовательские атрибуты "Запрос на обслуживание" -->
		<metaclass-attrs>
			<metaclass>serviceCall$serviceRequest</metaclass>

			<attr name="laboriousness" column="laboriousness" />
			<attr name="project" column="project">
				<script-converter><![CDATA[
                    def codeMap = ['1' : '0001', '2' : '0002', '3' : '0003'];
                    def code = codeMap[value];
                    return null == code ? null : utils.get('Projects', ['code' : code]);
                ]]></script-converter>
			</attr>
		</metaclass-attrs>

		<!-- Пользовательские атрибуты "Запрос на обслуживание корп. системы" -->
		<metaclass-attrs>
			<metaclass>serviceCall$ksServiceReques</metaclass>

			<attr name="AskChangeDeadli" column="AskChangeDeadli" />
			<attr name="Category" column="Category" />
			<attr name="desiredDeadline" column="desiredDeadline">
				<datetime-converter format="dd.MM.yyyy HH:mm:ss" />
			</attr>
		</metaclass-attrs>

		<!-- Пользовательские атрибуты "Запрос на организацию рабочего места" -->
		<metaclass-attrs>
			<metaclass>serviceCall$WorkplaceCreati</metaclass>

			<attr name="block" column="block" />
			<attr name="position" column="position" />
			<attr name="comnata" column="comnata" />
			<attr name="parent1" column="parent1" />
			<attr name="otdel" column="otdel" />
			<attr name="po" column="po" /><!-- узнать формат -->
			<attr name="fio" column="fio" />
		</metaclass-attrs>

		<!-- Пользовательские атрибуты "Инцидент" -->
		<metaclass-attrs>
			<metaclass>serviceCall$incident</metaclass>

			<attr name="project" column="project">
				<script-converter><![CDATA[
                    def codeMap = ['1' : '0001', '2' : '0002', '3' : '0003'];
                    def code = codeMap[value];
                    return null == code ? null : utils.get('Projects', ['code' : code]);
                ]]></script-converter>
			</attr>
		</metaclass-attrs>

		<!-- Пользовательские атрибуты "Организация конференции, обучения и т.п." -->
		<metaclass-attrs>
			<metaclass>serviceCall$VKC</metaclass>

			<attr name="OptionalEquipme" column="OptionalEquipme" /><!-- узнать формат -->
			<attr name="OptionalEquipSt" column="OptionalEquipSt" />
			<attr name="Place" column="Place" />
		</metaclass-attrs>

		<script-customizer>
			<after-process><![CDATA[
                def state = item.properties.state.trim();
                def fqn = subject.metaClass.toString();
                
                if ("accepted" == state) {
                    utils.edit(subject, ['state' : 'accepted']);
                } else if ("resolved" == state) {
                    utils.edit(subject, ['state' : 'accepted']);
                    utils.edit(subject, ['state' : 'resolved']);
                } else if ("deferred" == state) {
                    utils.edit(subject, ['state' : 'accepted']);
                    utils.edit(subject, ['state' : 'deferred']);
                } else if ("resumed" == state) {
                    utils.edit(subject, ['state' : 'accepted']);
                    utils.edit(subject, ['state' : 'deferred']);
                    utils.edit(subject, ['state' : 'resumed']);
                } else if ("closed" == state && 'serviceCall$Email' == fqn) {
                    utils.edit(subject, ['state' : 'accepted']);
                    utils.edit(subject, ['state' : 'closed']);
                } else if ("closed" == state && 'serviceCall$rfsc' == fqn) {
                    utils.edit(subject, ['state' : 'closed']);
                } else if ("closed" == state) {
                    utils.edit(subject, ['state' : 'accepted']);
                    utils.edit(subject, ['state' : 'resolved']);
                    utils.edit(subject, ['state' : 'closed']);
                } else
                
                // Запрос на обслуживание корп. системы
                if ("registered2" == state) {
                    utils.edit(subject, ['state' : 'registered2']);
                } else if ("not_accept" == state) {
                    utils.edit(subject, ['state' : 'notAccept']);
                } else if ("clarify2" == state) {
                    utils.edit(subject, ['state' : 'registered2']);
                    utils.edit(subject, ['state' : 'accepted']);
                    utils.edit(subject, ['state' : 'clarify2']);
                } else
                
                // Запрос на доработку корп. системы
                if ("CreateTZ" == state) {
                    utils.edit(subject, ['state' : 'accepted']);
                    utils.edit(subject, ['state' : 'CreateTZ']);
                } else if ("implementation" == state) {
                    utils.edit(subject, ['state' : 'accepted']);
                    utils.edit(subject, ['state' : 'CreateTZ']);
                    utils.edit(subject, ['state' : 'implementation']);
                } else if ("testing" == state) {
                    utils.edit(subject, ['state' : 'accepted']);
                    utils.edit(subject, ['state' : 'CreateTZ']);
                    utils.edit(subject, ['state' : 'implementation']);
                    utils.edit(subject, ['state' : 'testing']);
                } else if ("ApprovalTK" == state) {
                    utils.edit(subject, ['state' : 'accepted']);
                    utils.edit(subject, ['state' : 'CreateTZ']);
                    utils.edit(subject, ['state' : 'ApprovalTK']);
                } else if ("GoToRealease" == state) {
                    utils.edit(subject, ['state' : 'accepted']);
                    utils.edit(subject, ['state' : 'CreateTZ']);
                    utils.edit(subject, ['state' : 'implementation']);
                    utils.edit(subject, ['state' : 'testing']);
                    utils.edit(subject, ['state' : 'GoToRealease']);
                }
            ]]></after-process>
		</script-customizer>
	</class>
</config>