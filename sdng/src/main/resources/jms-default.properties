# number of threads for eventActionsChangeTracking queue on the applications instance
eventActions.changeTracking.threadNumber=1
#Промежуток времени, указывает за какое время необходимо выполнить действия по событиям, когда приложение было отключено
eventActions.deviationPeriod=120
# number of threads for eventActions by escalations queue on the applications instance
eventActions.escalations.threadNumber=1
# number of threads for eventActionsExternal queue on the applications instance
eventActions.external.threadNumber=1
# number of message groups. must be more then eventActions.threadNumber . for cluster it is sum of eventActions.threadNumber
# all nodes and more
eventActions.groupNumber=4
eventActions.idle.limits.disabled=false
# количество потоков для очередей интеграции
eventActions.integration.threadNumber=1
eventActions.integration.high.threadNumber=1
# enable/disable deferred sender for notifications
eventActions.notifications.sender.deferred=false
# number of threads for eventActionsNotifications queue on the applications instance
eventActions.notifications.threadNumber=1
# number of threads for eventActionsPushes queue on the applications instance
eventActions.pushes.threadNumber=1
# number of thread for eventActions queue on the applications instance
eventActions.threadNumber=1
ru.naumen.jms.artemis.jgroups.channel=default
########################################
ru.naumen.jms.artemis.jgroups.config=artemis-jgroups_udp_unicast.xml
# Настройки для получения алертов из NDAP
ru.naumen.jms.artemis.server.standalone_acceptor=false
ru.naumen.jms.artemis.server.standalone_acceptor.ssl.enabled=false
ru.naumen.jms.artemis.server.standalone_acceptor.ssl.needClientAuth=true
ru.naumen.jms.artemis.server.standalone_host=localhost
ru.naumen.jms.artemis.server.standalone_port=5445
# Настройка Artemis
ru.naumen.jms.artemis.server.use_standalone=false
# Path for custom broker.xml for embedded jms server
ru.naumen.broker_xml.path=broker.xml
#Выполнять асинхронные ДПС асинхронно всегда
ru.naumen.eventActions.alwaysAsyncExecution.enabled=false
ru.naumen.events.service.queue.capacity=2147483647
ru.naumen.events.service.thread.core.pool.size=2
# Max Pool Size должен быть больше или равен Core Pool Size
ru.naumen.events.service.thread.max.pool.size=8
# Какие интеграции с MQ мы считаем включенными
# Нужно для контроля успешности api.mq.reload()
# Если не существует файл конфигурации хотя бы одной интеграции, указанной здесь, то метод вернет false
# Возможные значения смотри в ru.naumen.core.server.script.api.IMqApi.Integration
# Значения разделяются через запятую
ru.naumen.integrations.mq.enabledIntegrations=JMS
# Возможность конфигурирования протоколов ACKNOWLEDGE: client, individual (по умолчанию client)
ru.naumen.jms.acknowledge=client
# Определяет количество обработчиков для очереди Queue.AdvImport
ru.naumen.jms.advimport.threadNumber=1
ru.naumen.jms.artemis.connection.ttl=-1
ru.naumen.jms.artemis.global.scheduledThread.maxPoolSize=1
# The default sizes for the artemis thread pools
ru.naumen.jms.artemis.global.thread.maxPoolSize=4
ru.naumen.jms.artemis.io.netty.allocator.maxOrder=7
# Properties to adjust netty buffer's chunk size
# chunkSize = pageSize << maxOrder
ru.naumen.jms.artemis.io.netty.allocator.pageSize=4096
# Политика балансировки соединений
ru.naumen.jms.artemis.loadBalancing.className=
# Minimum message size in bytes for deciding whether message is large or not
# Applies to Artemis MQ only
ru.naumen.jms.artemis.minLargeMessageSizeInBytes=5242880
ru.naumen.jms.artemis.netty.connect.timeout=60000
#Количество попыток соединения с брокером
ru.naumen.jms.artemis.reconnect.attempts.count=-1
# Использовать ли свою фабрику соединений для каждого jmstemplate
ru.naumen.jms.artemis.separated.connectionFactory.jmsTemplate=true
# Использовать ли свою фабрику соединений для каждой очереди
ru.naumen.jms.artemis.separated.connectionFactory.queue=true
# Список очередей, у которых медленные консьюмеры и нужно выключить буфферизацию на их строне
ru.naumen.jms.artemis.slow.consumers.queues=
# The number of netty threads in standalone mode
ru.naumen.jms.artemis.standalone.remotingThreads=4
# Количество netty threads в standalone mode, если используется механизм создания отдельных фабрик соединений
ru.naumen.jms.artemis.standalone.separated.connectionFactory.remotingThreads=1
# Использовать ли заголовок _AMQ_DUPL_ID для исключения повторной отправки JMS сообщений в Artemis
ru.naumen.jms.artemis.useDuplicateDetection=false
ru.naumen.jms.artemis.uri=

# Настройка сервиса для автоматического восстановления очередей artemis
# включение/отключение задачи автоматического восстановления
ru.naumen.jms.autoRecovery.enabled=false
# список очередей для анализа
ru.naumen.jms.autoRecovery.queues=
# период (в секундах) срабатывания задачи восстановления
ru.naumen.jms.autoRecovery.period=15
# интервал (в секундах), для которого вычисляется коэффициент разбора очереди
ru.naumen.jms.autoRecovery.interval=60
# допустимый порог коэффициента разбора очереди. Коэффициент вычисляется как отношение кол-ва новых сообщений к
# кол-ву обработанных сообщений за интервал времени
ru.naumen.jms.autoRecovery.ratioLimit=2.5
# время (в миллисекундах) для ожидания восстановления консьюмеров перед возобновлением работы очереди
ru.naumen.jms.autoRecovery.waitForResumeMillis=1000

ru.naumen.jms.container.cache.level=4
# Включает/отключает локальные транзакции для обработки очередей действий по событиям,
# если внешний менеджер транзакций выключен.
ru.naumen.jms.eventAction.allowLocalSessionTx=true
# Включает/отключает использование внешнего менеджера транзакций для обработки очередей
ru.naumen.jms.listeners.use.external.txManager=false
# Логин подключения к внешнему artemis (по дефолту admin)
ru.naumen.jms.login=admin
# Пароль для подключения к внешнему artemis (по дефолту admin)
ru.naumen.jms.password=admin
# Зашифрованный пароль подключения к внешнему artemis
ru.naumen.jms.password.enc=
# возможность переключить пользовательские очереди в режим мультикаста (Publish/Subscribe domain)
ru.naumen.jms.pubSubDomain.enabled=false
#default timeout receiving message from jms server, in ms. Default 1.5 seconds
ru.naumen.jms.receive.timeout=1500
#Выставлять значение больше 1, только при кластере артемиса
ru.naumen.jms.send.attempts.count=1
#JMS Statistics enabled
ru.naumen.jms.statistic.enabled=true
ru.naumen.jms.template.session.cache.size=50
#Максимальное количество подключений, которое пул поддерживает в одном пуле подключений
ru.naumen.jms.artemis.pooled.connectionFactory.maxConnections=5
#Максимальное количество сессий в одном соединении
ru.naumen.jms.artemis.pooled.connectionFactory.maxSessionsPerConnection=500
#Таймаут бездействия соединения, по истечении которого соединение может быть закрыто
ru.naumen.jms.artemis.pooled.connectionFactory.connectionIdleTimeout=30000
#Периодичность выполнения проверки соединений в пуле
ru.naumen.jms.artemis.pooled.connectionFactory.connectionCheckInterval=-1
#Максимальное кол-во байт, которое сервер должен возвращать для запроса на выборку
ru.naumen.kafka.consumer.fetchMaxBytes=52428800
#Стартовать ли консьюмеры для кафки после запуска приложения
ru.naumen.kafka.consumer.startAfterStartApplication.enabled=true
#Максимальное кол-во байт на каждую партицию, которое вернет сервер
ru.naumen.kafka.consumer.maxPartitionFetchBytes=1048576
#Максимальное количество записей, возвращаемых за один вызов функции poll().
ru.naumen.kafka.consumer.maxPollRecords=500
#Максимальная задержка между вызовами функции poll() при использовании управления группами потребителей.
ru.naumen.kafka.consumer.maxPollIntervalMs=300000
#Интервал между повторными попытками аутентификации
ru.naumen.kafka.errorHandler.authExceptionRetryInterval=0
#Интервал между повторными подключениями в KafkaMqManagementServiceImpl
ru.naumen.kafka.errorHandler.interval=0
#Автоматически перезапустить контейнер при ошибке
ru.naumen.kafka.errorHandler.restartAfterAuthExceptions.enabled=false
# количество обработчиков очереди обработки изменения статуса у уведомлений в интерфейс с ридонли нод
ru.naumen.changePushState.threadNumber=1
# максимальное количество пользовательских очередей
ru.naumen.max.quantity.user.queues=10
##########################################################################################
# Параметры конфигурации работы с плановыми событиями
##########################################################################################
# Количество потоков для обработки сообщений из очереди Queue.PlannedEvents
ru.naumen.planned.events.threadNumber=1
# максимальное количество потоков в пользовательских очередях
ru.naumen.max.quantity.thread.queue=10
ru.naumen.planned.events.dateCalculator.treshhold=30000
ru.naumen.planned.events.executor.corePoolSize=2
ru.naumen.planned.events.executor.maxPoolSize=4
# максимальный размер очереди Executor для планируемых событий (по умолчанию Integer.MAX)
ru.naumen.planned.events.executor.queue.maxSize=0x7fffffff
ru.naumen.planned.events.listener.execution.threadNumber=1
ru.naumen.planned.events.listener.fastExecution.threadNumber=1
# Тайм-аут транзакции, в рамках которой происходит подъем плановых событий из БД, в секундах.
# Необходимо ограничивать таймаут, для избежания длительного удержания потока исполнения и соединения, на случай сбоев.
ru.naumen.planned.events.job.transactionTimeout=300
# Значение периода (максимальное смещение от текущей даты), за который выбираются события при инициализации и дальнейшем
# заполнении очереди.
# Если разница между текущей датой и событием больше, то планируемое событие не должно подниматься из БД
ru.naumen.planned.events.relevance.period=60000
# Выполнять репланирование всех плановых событий через механизм синхронизации транзакций на событие коммита транзакции
ru.naumen.planned.events.syncReplan.enabled=true
##########################################################################################
# Число миллисекудн, в течении которых результат вызова /rest/status-check будет закеширован
ru.naumen.status.check.limitInMillis=30000
#Передавать в пользовательские ДПС обертку для subject типа EventActionSubjectDtObject, а не полноценный объект
ru.naumen.userEventActions.useWrappedSubject=true
# необходимое количество потоков для очереди асинхронных пользовательских ДПС
userEventActions.threadNumber=1
# Настройка блокирования очередей во время длительных операций по чтению и обработке сообщений консьюмерами
ru.naumen.jms.artemis.blockOnDurableSend=true