<?xml version="1.0" encoding="UTF-8"?>

<config description="Example" 
    threads-number="1"
    save-log="true"
    skip-workflow="false"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="../../../target/classes/advimport/schema1.xsd">
<!-- Значение параметра skip-workflow, указывает на то, нужно ли пропускать проверки, связанные с ЖЦ объектов -->
<!-- (используется, например, для импорта закрытых запросов для перевода их сразу в статус "Закрыт")-->
<!-- По умолчанию - false -->

    <!-- Задаем режим импорта. На данный момент доступны три режима: -->
    <!-- CREATE - только создание объектов; -->
    <!-- UPDATE - только обновление существующих объектов; -->
    <!-- EMPTY - без создания объектов и обновления существующих объектов -->
    <!-- Можно указать сразу два режима CREATE и UPDATE, тогда будут создаваться новые объекты и обновляться уже существующие -->
    <!-- Можно указать только режим EMPTY или добавить его к CREATE и UPDATE. В этом случае создание объектов и обновления существующих -->
    <!-- происходить не будет. -->
    <mode>CREATE</mode>
    <mode>UPDATE</mode>
    <mode>EMPTY</mode>

    <!-- Задает необходимостить запросить у пользователя дополнительные параметры импорта которые можно использовать -->
    <!-- в конфигурации импорта -->
    <!-- Эти параметры будут запрошены только в случае запуска импорта пользователем через интерфейс -->
    <!-- На данный момент реализовано два типа параметров: -->
    <!--    STRING - запрос строкового параметра; -->
    <!--    FILE   - загрузка файла. -->
    <gui-parameter name="paramName" type="STRING" title="Additional parameter"/>
    <gui-parameter name="uploadedFile" type="FILE" title="File for import" />

    <!-- Задает некоторую константную величину которую можно в дальнейшем использовать в конфигурации. В отличае от -->
    <!-- gui-parameter значение не запрашивается у пользователя. -->
    <parameter name="defaulTitle">Default title</parameter>

    <!-- Задает импорт объектов из конкретного источника. В одной конфигурации импорта может быть описано несколько -->
    <!-- источников. -->
    <!-- name          - задает имя конфигурации. Используется для логирования процесса импорта -->
    <!-- thread-number - задает кол-во потоков в которых будет производиься импорт объектов. Должен быть неотрицательным -->
    <!--                 целым числом. Если равен 0, то значение наследуется из конфигурации импорта. -->
    <!-- log-column-name - позволяет задавать имя колонки содержимое которой будет выводить в круглых скобках с сообщениях импорта -->
    <!--                   рядом с UUID объекта. Пример: log-column-name="title" -->
    <class name="import1" threads-number="1">

        <!-- Тоже что и mode в config. Переопределяет значение общее для всех class-ов -->
        <mode>CREATE</mode>
        <mode>UPDATE</mode>
        <mode>EMPTY</mode>

        <!-- Тоже что и parameter в config. Переопределяет значение общее для всех class-ов -->
        <parameter name="defaulTitle">Default title</parameter>

        <!-- Предопределенные параметры (параметры значение которых используется по умолчанию) -->
        <!-- Код метакласса импортируемых объектов -->
        <!-- parameter name="metaClass">ou</parameter -->
        <!-- Код атрибута синхронизации объектов. Предполагается что в нем храниться идентификатор из внешней системы -->
        <parameter name="idHolder">idHolder</parameter>
        <parameter name="importRootUUID">ou$1234</parameter>

        <!-- Описывает источник данных из CSV-файла -->
        <!-- file-name задает имя файла из которого будет производится импорт. Обязательно необходимо указывать -->
        <!--           протокол по которому необходимо получить файл. В приведенном примере импортируемый файл -->
        <!--           запрашивается gui-parametr-ом с именем uploadedFile -->
        <!-- url-timeout задает таймаут ожидания получения данных из внешнего источника, заданного с помощью URL. -->
        <!--           Задается в секундах, 0 подразумевает бесконечное ожидание. Необязательный параметр, -->
        <!--           если не указан, используется значение из настроек соединения по умолчанию. -->
        <!-- with-header задает наличие подписей колонок в имортируемом файле. Если значение true, то scr-key -->
        <!--           колонки задаёт подпись колонки. Если false, то scr-key указывает номер колонки. -->
        <!-- encoding  задает кодировку символов импортируемого файла. По умолчанию CP1251 -->
        <!-- id-column задает идентификатор колонки в которой содержится идентификатор импортируемого объекта. -->
        <!--           В дальнейшем по этому идентификатору будет производится поиск объекта. -->
        <!-- delimiter задает разделитель колонок в csv (по умолчанию ';'). Должен быть одним симоволом. -->
        <!-- text-delimiter задает разделитель текста в csv (по умолчанию '"'). Должен быть одним символом.-->
        <csv-data-source file-name="$uploadedFile" url-timeout="40" with-header="true">
            <!-- Описывает колонку импортируемых данных -->
            <!-- src-key - если with-header=true, то подпись колонки, иначе номер колонки -->
            <column name="id" src-key="@id"/>
        </csv-data-source>

        <!-- Описывает источник данных - NoSQL БД Cassandra-->
        <cassandra-data-source>
            
            Колонки доступны по их индексам в результате выполнения CQL запроса. 1 колонка имеет индекс 0.-->
            <column name="ou_id" src-key="0"/>
            <column name="title" src-key="1"/>
            <!-- Параметры подключения к Cassandra: сервер, пространство ключей, пользователь, пароль. Порт используется 9042 (порт по-умолчанию Cassandra) -->
            <cassandra-connection host="***************" keyspace="testkeyspace" user="user" password="user_password" ssl="true" port="9042"/>

            <!--  -->Запрос результаты выполнения которого - источник данных для импорта -->
            <query>
            select * from ous
            </query>
        </cassandra-data-source>

        <!--Описывает источник данных из sql-соединения:
            id-column задает идентификатор колонки в которой содержится идентификатор импортируемого объекта.
                В дальнейшем по этому идентификатору будет производится поиск объекта.-->
        <sql-data-source>
            
            <!-- Описывает колонку импортируемых данных:
                src-key - номер колонки или ее алиас (если число, то номер колонки иначе алиас колонки) -->
            <column name="id" src-key="@id"/>
             
            <!-- Задаёт ссылку на подключение к источнику SQL (код подключения из каталога подключений): -->
            <connection-code>@код_подключения</connection-code>
            <!-- или непосредственно параметры подключения:
                 url       URL соединения с базой данных
                 driver    имя класса драйвера БД
                 user      имя пользователя подключения к БД
                 password  пароль пользователя -->
            <sql-connection user="" password="" driver="">@url строки подключения</sql-connection>
            <!-- Задает sql-запрос для получения импортируемых данных:-->
            <query>select col1, col2 from table where col1 = ?</query>
            <!--Определяет параметры в sql-запросе(если он параметризованный) с помощью скрипта. 
                В скрипте помимо стандартного апи доступна переменная query(PreparedStatement), 
                в которой содержится параметризованный запрос из тега <query>. 
                Чтобы определить параметры (записанные в виде знака вопроса) в SQL-запросе, 
                нужно у переменной query вызвать один или несколько(в зависимости от количества параметров) методов из следующего списка:
                query.setBoolean(1, boolean);
                query.setString(1, string);
                query.setInt(1, int);
                query.setLong(1, long);
                query.setDate(1, date);
                query.setTimestamp(1, timestamp);
                query.setObject(1, object);
                query.setBlob(1, blob);
                query.setClob(1, clob);
                В этих методах первый параметр - номер параметра в SQL-запросе(начинается с 1), второй - значение соответствующего типа.-->
            <script>query.setString(1,'lalala')</script>
        </sql-data-source>

        <!-- Описывает источник данных из XLS-таблицы -->
        <!-- file-name задает имя файла из которого будет производится импорт. Обязательно необходимо указывать -->
        <!--           протокол по которому необходимо получить файл. -->
        <!-- url-timeout задает таймаут ожидания получения данных из внешнего источника, заданного с помощью URL. -->
        <!--           Задается в секундах, 0 подразумевает бесконечное ожидание. Необязательный параметр, -->
        <!--           если не указан, используется значение из настроек соединения по умолчанию. -->
        <!-- sheet-number номер листа книги на котором находятся импортируемые данные -->
        <!-- start-row номер первой строки содержащей импортируемые данные -->
        <!-- id-column задает идентификатор колонки в которой содержится идентификатор импортируемого объекта. -->
        <!--           В дальнейшем по этому идентификатору будет производится поиск объекта. -->
        <xls-data-source file-name="http://host/path" url-timeout="30" sheet-number="1" start-row="1">
            <!-- Описывает колонку импортируемых данных -->
            <!-- src-key - номер колонки -->
            <column name="id" src-key="@id"/>
        </xls-data-source>

        <!-- Описывает источник данных из XLSX-таблицы -->
        <!-- file-name задает имя файла из которого будет производится импорт. Обязательно необходимо указывать -->
        <!--           протокол по которому необходимо получить файл. -->
        <!-- url-timeout задает таймаут ожидания получения данных из внешнего источника, заданного с помощью URL. -->
        <!--           Задается в секундах, 0 подразумевает бесконечное ожидание. Необязательный параметр, -->
        <!--           если не указан, используется значение из настроек соединения по умолчанию. -->
        <!-- sheet-number номер листа книги на котором находятся импортируемые данные -->
        <!-- start-row номер первой строки содержащей импортируемые данные -->
        <!-- id-column задает идентификатор колонки в которой содержится идентификатор импортируемого объекта. -->
        <!--           В дальнейшем по этому идентификатору будет производится поиск объекта. -->
        <xlsx-data-source file-name="http://host/path" url-timeout="30" sheet-number="1" start-row="1">
            <!-- Описывает колонку импортируемых данных -->
            <!-- src-key - номер колонки -->
            <column name="id" src-key="@id"/>
        </xlsx-data-source>

        <!-- Описывает источник данных из XML -->
        <!-- file-name задает имя файла из которого будет производится импорт. Обязательно необходимо указывать -->
        <!--           протокол по которому необходимо получить файл. -->
        <!-- url-timeout задает таймаут ожидания получения данных из внешнего источника, заданного с помощью URL. -->
        <!--           Задается в секундах, 0 подразумевает бесконечное ожидание. Необязательный параметр, -->
        <!--           если не указан, используется значение из настроек соединения по умолчанию. -->
        <!-- xpath     xpath путь до импортируемых элементов данных -->
        <!-- id-column задает идентификатор колонки в которой содержится идентификатор импортируемого объекта. -->
        <!--           В дальнейшем по этому идентификатору будет производится поиск объекта. -->
        <!-- fast-parsing Задает режим обработки xml документа: -->
        <!-- 			значение false - используется реализация парсера по умолчанию, с полным построением дерева узлов; -->
        <!-- 			значение true - xml документ считывается в потоковом режиме без полной загрузки документа в память -->
        <!-- 			и без полного построения дерева узлов. -->
        <xml-data-source file-name="ftp://host/path" url-timeout="0" xpath="/Cnt/Item" fast-parsing="false">
            <!-- Описывает колонку импортируемых данных -->
            <!-- src-key - xpath значения колонки относительно импортируемого элемента -->
            <column name="id" src-key="@id"/>
        </xml-data-source>

        <!--
        Описывает источник данных из LDAP или AD:
            domain              - ???
            import-root         - указывает необходимость импорта объекта на который указывает DN из root-element
            full-domain         - задает правило формирования логина импортируемого пользователя по его DN
            check-user-disabled - указывает необходимость пропускать пользователей которые неактивны в AD
                Параметры full-domain и check-user-disabled относятся к импорту сотрудников из AD или LDAP и не должны
                использоваться во время импорта других объектов-->
        <ldap-data-source check-user-disabled="true" full-domain="true" import-root="true" domain="">

            <!-- Задает соответствие атрибутам объекта LDAP колонкам импорта. Атрибуты объектов расширяются следующими:
                parent - objectGUID родительского объекта;
                login  - логин пользователя включая доменное имя. -->
            <column name="" src-key=""/>

            <!-- Задаёт ссылку на подключение к источнику данных (код подключения из каталога подключений): -->
            <connection-code>@код_подключения</connection-code>
            <!-- или непосредственно параметры подключения:
                user              - имя пользователя для подключения к LDAP-серверу
                password          - пароль пользователя для подключения к LDAP-серверу
                url               - URL подключения к LDAP-серверу
                auth-type         - уровень идентификации (варианты: none, simple, strong)
                security-protocol - протокол безопасности (варианты: url)
                skip-cert-verification  - игнорировать проверку SSL сертификата (менее безопасно) (варианты: true, false)
                referral          - процесс, посредством которого LDAP-сервер, вместо того, чтобы вернуть результат запроса,
                                    возвращает ссылку (отсылку, referral) на другой LDAP-сервер, который может содержать дополнительную информацию
                                    (варианты: follow - переходить по ссылке, throw - выбрасывать ошибку, если получилась ссылка, ignore - игнорировать ссылки)-->
            <ldap-connection user="" password="" auth-type="" security-protocol="" skip-cert-verification="true" referral="follow">@url строки подключения</ldap-connection>

            <!-- Указывает корень (DN) иерархии объектов LDAP для импорта. Необходимо указать как минимум один корень: -->
            <root-element></root-element>

            <!-- Указывает префикс элементов импорта по которым будет осуществляться фильтрация.
                 Не обязателен. Можно указать несколько раз.  -->
            <import-tag></import-tag>

            <!-- Указывает полный путь (DN) до иерархии игнорируемых объектов LDAP для импорта.
                 Может быть использован несолько раз для одного или нескольких корней -->
            <ignored-postfix></ignored-postfix>

            <!-- Указывает полный путь (DN) до импортируемой иерархии объектов LDAP для импорта.
                 Состоит из полного пути включая корень. В случае указания хотя бы одного allowed-postfix,
                 все импортируемые объекты, которые не будут ему соответствовать будут отброшены.
                 Имеет приоритет ниже чем ignored-postfix.
                 Можено указать несколько раз для одного или нескольких корней. -->
            <allowed-postfix></allowed-postfix>

            <!-- Задает фильтр поиска объектов в LDAP.
                 Значение по умолчанию (|(&(objectClass=user)(objectCategory=person))(objectClass=organizationalUnit)): -->
            <filter></filter>

        </ldap-data-source>

        <!-- id-column должен задаваться при использовании hierarchical-filter и при использовании режима обновления объектов. -->

        <xml-data-source id-column="id" file-name="$uploadedFile" xpath="/Items/OU">
            <column name="id" src-key="./@id" />
            <column name="title" src-key="./Title/text()" />
        </xml-data-source>

        <!-- Определяет необходимость иерархической сортировки импортируемых строк. Т.е. гарантируется что сначала -->
        <!-- будет проимпортирован объект с значением id-column совпадающем со значением parent-column -->
        <!-- root-id - указывает значение колонки указывающего на родителя, которого считать корнем иерархии (импортироватьпервым) -->
        <hierarchical-filter parent-column="parent"/>

        <!-- Фильтр исключающий из импорта строки с пустым значением указанной колонки -->
        <column-notempty-filter column="title"/>

        <!-- Фильтр модифицирующий значения id-column и parent-column (если определен hierarchical-filter) -->
        <!-- дописывая к ним в качестве префикса указанное значение -->
        <id-prefix prefix="prefix-value"/>

        <!-- Фильтр на основе скрипта -->
        <!-- В скрипте доступны глобальные переменные: -->
        <!-- ctx - ImportContext, чтобы можно было делать ctx.evaluate(expr) для получения значения выражений или -->
        <!-- логгирования ctx.getLogger().info('text') -->
        <!--   item       - соответствующая строке импортируемых данных; -->
        <!--   parameters - соответствующая параметрам импорта. -->
        <!--   Скрипт-фильтр работает вне транзакций! Чтобы выполнить обращение к базе данных,  -->
        <!--   например, методом utils.find(...) - его нужно обернуть в транзакцию: api.tx.call {def a = utils.find(...)} -->
        <script-filter mime-type="">return item.properties.id != "skip" || "true" == parameters.noSkip</script-filter>
        <!-- (В приведенном примере noSkip - имя параметра заданного тэгом <parameter ... >) -->

        <!-- MetaClassResolver задает стратегию определения метакласса создаваемых объектов. Нужен только для режима CREATE -->
        <!-- обновления объектов задавать этот параметр не обязательно. -->

        <!-- constant-metaclass-resolver - задает конкретный метакласс создаваемых объектов. Т.е. все объекты будут -->
        <!-- созданы с этим метаклассом -->
        <!-- metaclass - метакласс создаваемых объектов. Значение по умолчанию ${metaClass} -->
        <constant-metaclass-resolver metaclass="ou$forTest" />

        <!-- by-column-metaclass-resolver - позволяет определить тип создаваемого объекта по значению колонки -->
        <!-- импортируемых данных -->
        <!-- metaclass - код метакласса создаваемых объектов (без типа). Значение по умолчанию ${metaClass} -->
        <!-- case-column - колнка, значение которой определяет тип создаваемого объекта -->
        <!-- default-case - тип создаваемых значений по умолчанию (если колонка не содержит данных) -->
        <by-column-metaclass-resolver metaclass="ou" case-column="caseColumn"/>

        <!-- column-metaclass-resolver - позволяет определить тип создаваемого объекта по значению колонки -->
        <!-- импортируемых данных. В отличае от by-column-metaclass-resolver в колонке должен содержаться -->
        <!-- полный идентификатор метакласса. -->
        <!-- column - колнка, значение которой определяет метакласс создаваемого объекта -->
        <!-- default-metaclass - идентификатор метакласса объектов, если в колоке не содержится данных -->
        <column-metaclass-resolver default-metaclass="ou$example" column="column"/>

        <!-- script-metaclass-resolver - позволяет определить тип создаваемого объекта скриптом на основании входящих данных -->
        <!-- В скрипте доступны глобальные переменные: -->
        <!-- ctx - ImportContext, чтобы можно было делать ctx.evaluate(expr) для получения значения выражений или -->
        <!-- логгирования ctx.getLogger().info('text') -->
        <!-- item - соответствующая строке импортируемых данных; -->
        <!-- parameters - соответствующая параметрам импорта. -->
        <!-- mime-type - тип скрипта (не обязателен, по умолчанию application/x-groovy) -->
        <script-metaclass-resolver mime-type="application/x-groovy">return item.properties.columnName</script-metaclass-resolver>

        <constant-metaclass-resolver metaclass="ou$forTest" />

        <!-- Для поиска уже существующих объектов задается ObjectSearcher позволяющий наити объект соответствующий -->
        <!-- строке импортируемых данных -->

        <!-- object-searcher - задает простое правило поиска объекта по значению атрибута объекта. Объекты -->
        <!-- ищутся среди объектов с заданным метаклассом -->
        <!-- attr      - атрибут по которому осуществляется поиск объекта. Значение по умолчанию ${idHolder} -->
        <!-- metaclass - тип объектов среди которых осуществляется поиск. Значение по умолчанию ${metaClass} -->
        <object-searcher attr="idHolder" metaclass="ou$forTest"/>

        <!-- complex-object-searcher - позволяет задать последовательность поиска объекта по нескольким атриубутам -->
        <!-- объекта или искать по различным метаклассам -->
        <complex-object-searcher>
            <object-converter attr="idHolder" metaclass="ou$forTest"/>
            <object-converter attr="title" metaclass="ou"/>
            <script-converter>a + b</script-converter>
        </complex-object-searcher>

        <!-- script-object-searcher позволяет находить объект по сложной логике поиска -->
        <!-- Более подробную документацию смотри у script-converter -->
        <script-object-searcher mime-type="application/x-groovy"> a + b </script-object-searcher>

        <object-searcher attr="idHolder" metaclass="ou$forTest" />

        <!-- Описывает присваивание атрибуту объекта значения -->
        <!-- name          имя атрибута значение которого устанавливается -->
        <!-- column        название колонки из которой берутся данные для присваивания. Может быть не указано, тогда -->
        <!--               устанавливаемое значение будет браться из default-value -->
        <!-- default-value значение устанавливаемое по умолчанию в случае пустого значения из источника данных -->
        <!-- not-null      указывает что объекту в обязательном порядке должно быть присвоено не пустое значение. Если -->
        <!--               значение будет пустое, то объект импортироваться не будет т.к. это будет считаться ошибкой. -->
        <!-- Указывать преобразователь значения не обязательно. В этом случае преобразователь будет определен на -->
        <!-- основе метаинформации. -->
        <attr name="" column="" default-value="" not-null="true">

        <!-- Описывает необходимость заполнять атрибут только в определенном режиме импорта объекта -->
        <!-- include-mode - задает режим в котором необходимо заполнять (изменять) атрибут -->
        <!-- exclude-mode - задает режим в котором не требуется заполнять (изменять) атрибут -->
        <!-- имеет смысл одновременно пользоваться только одним способом задания режима импорта атрибута -->
        <include-mode>CREATE</include-mode>
        <exclude-mode>UPDATE</exclude-mode>

        <!-- Указыват необходимость преобразовать импортируемое значение в логическое значение -->
        <!-- true-value - задает значение соответствующее "истине". Если значение атрибута не задано, то -->
        <!--              импортируемые данные считаются true в случаях если значаение "true" или "yes" -->
        <boolean-converter true-value="goodValue" />

        <!-- Преобразует коллекцию значений (для атрибутов типа "Набор ссылок на бизнес-объект" и -->
        <!-- "Набо элементов спрвочника"). По сути, определяет соответствие для каждого объекта во входящих -->
        <!-- данных и перенаправляет преобразование к конкретному конвертеру. -->
        <collection-converter delimiter=",">
            <object-converter />
            <script-converter></script-converter>
        </collection-converter>

        <!-- Указывает необходимость преобразовать импортируемое значение в объект. -->
        <!-- Задает одно или более правил поиска объекта -->
         <!-- complex-object-converter выполняет указанные в нем правила последовательно. В порядке определения в xml
            Конвертер переходит к следующему правилу, только если предыдущий вернул null.-->
        <complex-object-converter>
            <object-converter attr="" metaclass=""/>
            <script-converter>a + b</script-converter>
        </complex-object-converter>

        <!-- Указывает необходимость преобразовать импортируемое значение в дату со временем -->
        <!-- format - форомат преобразования, например yyyy-MM-dd HH:mm:ss -->
        <datetime-converter format="yyyy-MM-dd HH:mm:ss"/>

        <!-- Указывает необходимость преобразования импортируемого значения в набор типов класса -->
        <case-list-converter delimiter="," class="ou"/>

        <!-- Указывает необходимость преобразовать импортируемое значение во временной интервал -->
        <!-- interval - используемый интервал(oбязательный атрибут конвертера). -->
        <!-- Возможные значения интервала: SECOND, MINUTE, HOUR, DAY, WEEK. -->
        <time-interval-converter interval="SECOND"/>

        <!-- Указывает необходимость преобразования импортируемого значения в вещественное число -->
        <double-converter />

        <!-- Указывает необходимость преобразования импортируемого значения в гиперссылку -->
        <!-- delimiter разделитель названия ссылки от URL -->
        <hyperlink-converter delimiter=";"/>

        <!-- Указывает необходимость преобразования импортируемого значения в целое число -->
        <integer-converter/>
  
        <!-- Импортирует картинки из AD:  -->
        <!-- Логика работы:-->
        <!-- Если mode = CREATE, то атрибуты заполняются, как обычно;-->
        <!-- Если mode = UPDATE, то если в атрибуте лежал такой-же файл - редактирование не происходит,-->
        <!-- если было пусто, либо файлов больше 1, либо 1, но другой - устанавливается новое значение;-->
        <!-- Если старое значение было не пусто, а новое = null, то старое значение затрётся, либо нет - -->
        <!-- взависимости от флага eraseOld.-->
        
        <!-- все параметры не обязательные. Их значения по умолчанию: decoder='default' title='pic.jpeg' eraseOld=false mimeType='image/jpeg' -->
        <!-- Декодеры: 'hex', 'base64' и 'default' -->
        <ad-image-converter decoder='hex' title='pic.jpg' mimeType='image/jpeg' eraseOld='true'/>

        <!-- Указывает необходимость преобразовать импортируемое значение в объект. -->
        <!-- Если найдено более одного значения, то возвращает произвольный объект. -->
        <!-- attr       имя атрибута по которому производится поиск объекта. Значение по умолчанию ${idHolder} -->
        <!-- metaclass  код метакласса среди объектов которого производится поиск. Значение по умолчанию ${metaClass} -->
        <!-- required   если значение true, то конвертор обязательно должен найти объект, а если -->
        <!--            объект не нашелся, то сообщать об ошибке и импорт объекта не производить. -->
        <!--            Но если конвертируемое значение пусто, то результатом преобразования будет -->
        <!--            NULL вне зависимости от значения параметра. если значение false и объект не будет найден, -->
        <!--            то будет возвращен null. По умолчанию true -->
        <!-- removed    если значение true, то искать только архивные объекты -->
        <!--            если значение false, то искать только НЕ архивные объекты -->
        <!--            если не задан, то искать все объекты независимо от архивности -->
        <object-converter attr="idHolder" metaclass="ou"/>

        <!-- Задает произвольное правило преобразования значения на основе скрипта -->
        <!-- В скрипте доступны глобальные переменные: -->
        <!--   value      - конвертируемое значение -->
        <!--   item       - соответствующая строке импортируемых данных; -->
        <!--   subject    - соответствующая проимпортированному объекту; -->
        <!--   parameters - соответствующая параметрам импорта. -->
        <!--   ctx        - ImportContext. Используется как ctx.evluate("parameter") когда нужно получить значение выражения(параметра)-->
        <!--                или  логгирования ctx.getLogger().info('text') его значения -->
        <!--   storage (не Map, но есть методы Map: put() и get())  - значение которое одно и тоже на протяжение всех выполнений скриптов кастомайзера -->
        <!-- Доступна только для чтения глобальная переменная subject соответствующая проимпортированному объекту -->
        <!-- mime-type определяет язык скрипта: application/x-groovy или application/javascript (НЕ ОБЯЗАТЕЛЬНО ДЛЯ ЗАПОЛНЕНИЯ) -->
        <script-converter mime-type="application/x-groovy"> a + b </script-converter>

        <!-- Указывает необходимость преобразования импортируемого значения в строку -->
        <string-converter trim="true"/>
        </attr>
        
        <!-- Если при импорте необходимо проимпортировать объект с ЖЦ сразу с конкретным статус, отличным от "Зарегистрирован"(например, это нужно при импорте закрытых объектов),  -->
        <!-- то следует воспользоваться конструкцией следующего вида. При этом в параметрах конфигурации должно быть skip-workflow="true" -->
        <!-- attr name="state" column="state" -->
            <!-- string-converter / -->
        <!-- /attr -->

        <attr name="idHolder" column="id" />
        <attr name="title" column="title" />
        
        <!-- для указания пользовательских атрибуттов объявленных в конкрентом метаклассе можно воспользоваться -->
        <!-- группировкой атрибутов по метаклассам -->
        <metaclass-attrs>
          <!-- Один или более кодов метакласса для которых необходимо заполнять атрибуты -->
           <metaclass></metaclass>
           
           <!-- список атрибутов метакласса -->
          <attr name="" />
        </metaclass-attrs>

        <!-- Помещает в архив все объект иерархически находящиеся в объекте с заданным uuid-ом и не участвующие -->
        <!-- в импорте (т.е. отсутствовала строка соответствующая объекту) -->
        <!-- hierarchy-root - uuid объекта в иерархии которого необъходимо произвести архивирование не -->
        <!--                  проимпортированных объектов. Значение по умолчанию ${importRootUUID} -->
        <!-- metaclass      - метакласс объектов для архивации. Или список метаклассов через запятую. Значение по умолчанию ${metaClass} -->
        <!-- attr           - код атрибута, по значению которого проверяется что объект импортирован, а не создан -->
        <!--                  вручную. Если не задан, то в архив помещаются все объекты (в т.ч. и созданные вручную) -->
        <remove-customizer hierarchy-root="obj-uuid"/>

        <!-- В случае необходимости задавать для remove-customizer исключения можно использовать вложенные скриптовые обработчики -->
        <remove-customizer hierarchy-root="obj-uuid">

          <!-- Позволяет указать какие объекты не должны архивироваться при работе remove-customizer -->
          <!-- Выполняется до начала логики работы remove-customizer и позволяет возврашать объекты или их uuid -->
          <!-- Результирующий список uuid добвляется к списку uuid участвующих в импорте и не подлежащих архивации -->
          <!-- В скрипте доступны глобальные переменные: -->
          <!--  parent - объект в иерархии которого необъходимо произвести архивирование не -->
          <!--        проимпортированных объектов. Значение по умолчанию ${importRootUUID} или параметр hierarchy-root -->
          <!--  ctx - ImportContext, чтобы можно было делать ctx.evaluate(expr) для получения значения выражений -->
          <!--        или логгирования ctx.getLogger().info('text') -->
          <!--  storage - (не Map, но есть методы Map: put() и get())  - значение которое одно и тоже на протяжение  -->
          <!--        всех выполнений скриптов кастомайзера -->
          <!--  parameters - соответствующая параметрам импорта. -->
          <skip-objects-script><![CDATA[
            return utils.find(parameters.metaClass, [ 'parent' : parent ])
          ]]></skip-objects-script>

          <!-- Скрипт который позволяет добавить дополнительное условие необходимости архивации объекта при работе remove-customizer -->
          <!-- Выполняется для объектов перед их архивацией. Если объект является исключением из архивации или
          <!--        не должен архивровать исход из условий, то скрипт запусктаься не будет. -->
          <!-- Позволяет так же выполнить изменение объекта перед его архивацией -->
          <!-- В скрипте доступны глобальные переменные: -->
          <!--  subject - объект подлежащий архивации при работе remove-customizer -->
          <!--  ctx - ImportContext, чтобы можно было делать ctx.evaluate(expr) для получения значения выражений -->
          <!--        или логгирования ctx.getLogger().info('text') -->
          <!--  storage - (не Map, но есть методы Map: put() и get())  - значение которое одно и тоже на протяжение  -->
          <!--        всех выполнений скриптов кастомайзера -->
          <!--  parameters - соответствующая параметрам импорта. -->
          <!--  Результат выполнения: true - архивировать, false - пропустить архивирование -->
          <remove-condition-script><![CDATA[
              return subject.title != 'testTitle'
          ]]></remove-condition-script>
    </remove-customizer>

        <!-- Дополнительная обработка во время процесса импорта -->
        <!-- isAfterImportMandatory - параметр отвечает за необходимость обязательного выполнения блока "after-import" -->
        <!--         в случае принудительной остановки синхронизации. По умолчанию: "false". -->
        <!-- Во всех скриптах доступны глобальные переменные: -->
        <!--   ctx - ImportContext, чтобы можно было делать ctx.evaluate(expr) для получения значения выражений или -->
        <!--         логгирования ctx.getLogger().info('text') -->
        <!--   storage (не Map, но есть методы Map: put() и get())  - значение которое одно и тоже на протяжение всех выполнений скриптов кастомайзера -->
        <!--   parameters - соответствующая параметрам импорта. -->
        <script-customizer>
            <!-- Вызывается перед импортом объектов -->
            <before-import></before-import>

            <!-- Вызывается перед формированием свойств бизнес-процесса -->
            <!-- Доступна для чтения/записи глобальная переменная item соответствующая строке импортируемых данных -->
            <!-- Доступна только для чтения глобальная переменная subject соответствующая проимпортированному объекту -->
            <before-process-item>a+b</before-process-item>
            
            <!-- Выполняется перед выполнением бизнес процесса-->
            <!-- Доступна только для чтения глобальная переменная item соответствующая строке импортируемых данных -->
            <!-- Доступна только для чтения глобальная переменная properties содержащая своиства бизнесс-процесса -->
            <!--              (итоговые значения атрибутов, которые будут использоваться в бизнесс-процессе) -->
            <!-- Доступна только для чтения глобальная переменная subject соответствующая проимпортированному объекту -->
            <before-process>a+b</before-process>

            <!-- Вызывается поле выполнения бизнес-процесса создания/редактирования объекта -->
            <!-- Доступна для чтения/записи глобальная переменная item соответствующая строке импортируемых данных -->
            <!-- Доступна только для чтения глобальная переменная subject соответствующая проимпортированному объекту -->
            <after-process>a+b</after-process>
            
            <!-- Вызывается после импорта всех объектов -->
            <after-import></after-import>
        </script-customizer>

<!--        Кастомайзер для импорта прямого счетчика времени -->
<!--            attr - код атрибута типа Timer -->
<!--            column - колонка в источнике в которой хранится сколько уже отсчитал счетчик в мс. -->
        <timer-customizer attr="totalTimeTimer" column="timer" />

<!--        Кастомайзер для импорта обратного счетчика времени -->
<!--            attr - код атрибута типа BackTimer -->
<!--            allowance-column - колонка в источнике в которой хранится сколько осталось отсчитать счетчику в мс. -->
<!--            deadline-column - колонка в источнике в которой хранится deadline для данного счетчика в формате deadline-column-format-->
<!--            deadline-column-format - форомат преобразования даты deadline, например yyyy-MM-dd HH:mm:ss -->
        <backtimer-customizer
            attr="timeAllowanceTimer"
            allowance-column="backTimer"
            deadline-column="deadlineTime"
            deadline-column-format="dd.MM.yyyy HH:mm:ss"/>
    </class>
</config>
