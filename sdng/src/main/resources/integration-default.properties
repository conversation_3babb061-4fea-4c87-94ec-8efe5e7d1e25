# Параметры подключения к сервису DaData.ru
#################################################################################
# API-ключ
dadata.apikey=
# Зашифрованный API-ключ
dadata.apikey.enc=
# Секретный код
dadata.secretkey=
# Зашифрованный секретный код
dadata.secretkey.enc=null

# Computer - Telephone Integration
#################################################################################
ru.naumen.cti.cdr.db.password=
ru.naumen.cti.cdr.db.password.enc=null
ru.naumen.cti.cdr.db.url=
ru.naumen.cti.cdr.db.user=
ru.naumen.cti.cdr.path=
#доступна ли интеграция с Naumen Contact Center
ru.naumen.cti.ncc.available=false
#версия протокола обмена с NSMP
ru.naumen.cti.ncc.tp.version=4
#таймаут на получение сообщения после отправки (в секундах)
ru.naumen.cti.ncc.websocket.message.timeout=120
#таймаут на наличие сообщений (в секундах)
ru.naumen.cti.ncc.websocket.timeout=7200

#  Embedded application settings
#################################################################################
# Internal deploy manager port
ru.naumen.embeddedapplication.deploymanager.port=4000
ru.naumen.embeddedapplication.nodejs.disabled=true
# URL of internal server with embedded applications
ru.naumen.embeddedapplication.server=http://127.0.1.1

# Интеграция с внешним шлюзом
ru.naumen.gatewayIntegration.enabled=false

#GIT remote
#################################################################################
ru.naumen.git.remote.timeout=30
#Properties to work with remote git repository
ru.naumen.git.remote.url=
ru.naumen.git.user.email=
ru.naumen.git.user.name=
ru.naumen.git.user.password=
ru.naumen.git.user.password.enc=

# Параметры подключения к InfluxDB
#################################################################################
ru.naumen.influx.accessToken=
ru.naumen.influx.accessToken.enc=
ru.naumen.influx.dbName=
ru.naumen.influx.url=

# Параметры ЕПГУ SOAP-сервиса
#################################################################################
ru.naumen.soap.epgu.create-orders-method=createOrders
ru.naumen.soap.epgu.delete-orders-method=deleteOrders
ru.naumen.soap.epgu.module=fgisEpgu
ru.naumen.soap.epgu.prepare-response-method=prepareResponse
ru.naumen.soap.epgu.update-orders-method=updateOrders

# Параметры SOAP-сервиса
#################################################################################
# Максимальный размер файла(файлов), отправляемого в SOAP-сообщении в байтах
ru.naumen.soap.max-file-size-bytes=4718592
# Максимальное клоичество uuid-ов объектов, возвращаемых методом find
ru.naumen.soap.max-find-ojects=5000
# Максимальное клоичество uuid-ов объектов, возвращаемых в качестве значений ссылочных атрибутов
ru.naumen.soap.max-ojects-in-attr-value=500
ru.naumen.soap.sign-method=sign
ru.naumen.soap.signature-module=fgisSign
ru.naumen.soap.smev3-namespace=http://naumen.ru/soap/server/1.0.3
ru.naumen.soap.smev3-timeout=10
ru.naumen.soap.templates-module=fgisRequestTemplates
ru.naumen.soap.transform-body-method=addSmevTagsToResponseBody
ru.naumen.soap.transform-header-method=addSmevHeaders
ru.naumen.soap.verify-method=verify

# Параметры подключения к SMS - центру
#################################################################################
sms.enquireLinkMillis=300000
sms.host=
sms.login=
sms.maxSendTry=5
sms.password=
sms.password.enc=null
sms.port=3700
sms.sendTimeoutMillis=60000