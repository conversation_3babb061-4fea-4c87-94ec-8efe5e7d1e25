#Устанавливает частоту (в миллисекундах), при которой этот экземпляр "проверяет" другие экземпляры кластера. Влияет на скорость обнаружения проблемных экземпляров.
org.quartz.jobStore.clusterCheckinInterval = 300000
#Включает режим работы в кластере
org.quartz.jobStore.isClustered = true
#Количество миллисекунд, в течение которых планировщик не обращает внимания на триггер, перед его повторным запуском, в случае осечки. Значение по умолчанию - 60000 (60 секунд).
org.quartz.jobStore.misfireThreshold = 60000
#Включает хранение данных в текстовом виде на стороне БД, вместо двоичного формата
org.quartz.jobStore.useProperties = false
#Используем AUTO, для того, чтобы каждый новый объект планировщика имел уникальное название.
org.quartz.scheduler.instanceId = AUTO
#Указываем настройки для создания пула потоков, на котором будут выполняться задачи
org.quartz.threadPool.class = org.quartz.simpl.SimpleThreadPool
org.quartz.threadPool.threadCount = 25
org.quartz.threadPool.threadPriority = 5
org.quartz.scheduler.instanceName = quartzScheduler
org.quartz.jobStore.dataSource = dataSource