<?xml version="1.0" encoding="UTF-8"?>
<infinispan
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="urn:infinispan:config:15.1 https://infinispan.org/schemas/infinispan-config-15.1.xsd"
        xmlns="urn:infinispan:config:15.1">

    <cache-container default-cache="the-default-cache">
        <!-- отключаем попытки создать репозиторий метрик в инфиниспане -->
        <metrics gauges="false"/>

        <local-cache name="the-default-cache">
            <encoding media-type="application/x-java-object"/>
            <expiration max-idle="300000" lifespan="900000"/>
            <memory max-count="1000" when-full="REMOVE"/>
        </local-cache>

        <!-- базовая конфигурация кэша для entity, шаблон для всех кэшей - регионов -->
        <local-cache-configuration name="entity">
            <encoding media-type="application/x-java-object"/>
            <expiration max-idle="300000" lifespan="900000"/>
            <memory max-count="1000" when-full="REMOVE"/>
        </local-cache-configuration>

        <!-- конфигурация кэша для запросов хибернейта -->
        <local-cache-configuration name="local-query">
            <encoding media-type="application/x-java-object"/>
            <expiration max-idle="300000" lifespan="900000"/>
            <memory max-count="5000" when-full="REMOVE"/>
        </local-cache-configuration>

        <!-- конфигурация кэша для хранения времени жизни элемента помещенного в другие кэши, внутренний кэш хибернейта
        должен быть бесконечным, не иметь внешних триггеров для сброса и не иметь ограничения по времени жизни -->
        <local-cache-configuration name="timestamps" simple-cache="true">
            <encoding media-type="application/x-java-object"/>
            <locking concurrency-level="1000" acquire-timeout="15000"/>
            <transaction mode="NONE"/>
            <expiration interval="0"/>
            <memory when-full="NONE"/>
        </local-cache-configuration>

        <!-- When providing custom configuration, always make this cache local and non-transactional.
             To avoid possible leaks, use expiration (max idle time). Optimize for speed.-->
        <local-cache-configuration name="pending-puts" simple-cache="true">
            <encoding media-type="application/x-java-object"/>
            <transaction mode="NONE"/>
            <expiration max-idle="60000"/>
        </local-cache-configuration>

        <!-- ридонли-кэш, управляемый нами, для кейса с рассылкой тысячам пользователей.
        Хранит ограниченную инфу об объекте Пользователь (смотри класс BaseEmployee) -->
        <local-cache name="basicEmployeeCache">
            <encoding media-type="application/x-java-object"/>
            <memory max-count="20000" when-full="REMOVE"/>
        </local-cache>

        <local-cache name="RootRegion">
            <encoding media-type="application/x-java-object"/>
            <memory max-count="10" when-full="REMOVE"/>
        </local-cache>

        <!-- Следующие кэши сущностей изменены на основе анализа статистики использования регионов -->
        <!-- Кэш Employee -->
        <local-cache name="employeeCache">
            <encoding media-type="application/x-java-object"/>
            <memory max-count="5000" when-full="REMOVE"/>
        </local-cache>

        <!-- Кэш файлов ФХ -->
        <local-cache name="file">
            <encoding media-type="application/x-java-object"/>
            <memory max-count="5000" when-full="REMOVE"/>
        </local-cache>

        <!-- Кэш md5 в файловом хранилище -->
        <local-cache name="fileHash">
            <encoding media-type="application/x-java-object"/>
            <memory max-count="5000" when-full="REMOVE"/>
        </local-cache>

        <!-- Кэш для сущности комментария -->
        <local-cache name="comment">
            <encoding media-type="application/x-java-object"/>
            <memory max-count="5000" when-full="REMOVE"/>
        </local-cache>

        <!-- Кэш запросов serviceCall -->
        <local-cache name="serviceCall">
            <encoding media-type="application/x-java-object"/>
            <memory max-count="5000" when-full="REMOVE"/>
        </local-cache>

        <!-- Настройки кэша groupMembersRegion для групп пользователей -->
        <local-cache name="groupMembersRegion">
            <encoding media-type="application/x-java-object"/>
            <memory max-count="5000" when-full="REMOVE"/>
        </local-cache>

    </cache-container>
</infinispan>
