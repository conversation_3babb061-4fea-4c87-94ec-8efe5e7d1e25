ru.naumen.core.authentication.ad-authenticator.connection-timeout=30000
ru.naumen.core.authentication.ad-authenticator.connection-url=ldap://localhost/
ru.naumen.core.authentication.ad-authenticator.user-password=password
ru.naumen.core.authentication.ad-authenticator.user-password.enc=null
ru.naumen.core.authentication.ad-authenticator.user-search-filter=userPrincipalName={0}
ru.naumen.core.authentication.ad-authenticator.user-upn=admin@localhost
ru.naumen.core.authentication.ad-authenticator.users-dn=cn=Users,dc=localhost
#################################################################################
#Список через запятую используемых типов авторизаторов. Допустимы лишь INTERNAL,EXTERNAL,LDAP,X509,AD,SPNEGO.
#Недопустимые значения игнорируются
#Authenticators (comma separated list). Available: INTERNAL,EXTERNAL,LDAP,X509,AD,SPNEGO. Unaccepted values are ignored.
ru.naumen.core.authentication.authenticators=INTERNAL
#################################################################################
# External authentification.
#################################################################################
#Имя файла(без пути до самого файла) с клиентским вариантом реализации отдельной страницы с ошибками при аутентификации
#через Web SSO. На данной странице есть возможность указывать элементы ${errorMessage}, ${logoutLink},
#${customErrorPageContent}, которые будут заменены на сообщение об ошибке, URL для выхода и содержимое переменной
#customErrorPageContent соответственно. Файл в формате html необходимо разместить в папке ext.prop.dir.
#В случае пустого значения будет использована дефолтная страница.
ru.naumen.core.authentication.external.clientCustomErrorPageFileName=
#Текстовка отображаемая на отдельной странице при ошибках аутентификации через Web SSO. В случае пустого значения,
#отображаются дефолтные сообщения об ошибках. Можно использовать html-теги
ru.naumen.core.authentication.external.customErrorPageContent=
#Использовать отдельную страницу для отображения ошибок аутентификации через Web SSO
ru.naumen.core.authentication.external.useCustomErrorPage=true
#################################################################################
# LDAP & AD authenticator settings перенесены (с версии 4.6) в отдельный конфигурационный файл ${ext.prop.dir}/ldap-settings.xml
#################################################################################
#URL подключения к LDAP и AD ### DEPRECATED! ### Эти настройки с версии 4.6 не работают
ru.naumen.core.authentication.ldap-authenticator.connection-timeout=30000
ru.naumen.core.authentication.ldap-authenticator.connection-url=ldap://127.0.0.1:389/ou=users,dc=organization,dc=ru #userDN - DN имени пользователя, ou=users,dc=organization,dc=ru
ru.naumen.core.authentication.ldap-authenticator.user-dn=ou=users,dc=organization,dc=ru
ru.naumen.core.authentication.ldap-authenticator.user-search-filter=uid={0}
# редирект на форму входа, когда ключ невалиден
ru.naumen.core.authentication.redirect_for_invalid_access_key=false
# выводить ли отладочную информацию из модуля (это не логирование процесса)
ru.naumen.core.authentication.spnego-authenticator.debug=false
# место расположения keytab файла.
# значение file:${ext.prop.dir}/ указывает на каталог где лежат настройки
ru.naumen.core.authentication.spnego-authenticator.keytab=krb.HTTP.keytab
# Словарь, содержащий пары (spn + realm) - (keytab) для прозрачной авторизации в нескольких доменах
ru.naumen.core.authentication.spnego-authenticator.keytab-spn-map={:}
# место расположения файла соответствия реалмов
# значение file:${ext.prop.dir}/ указывает на каталог где лежат настройки
ru.naumen.core.authentication.spnego-authenticator.realm-config-file=realm-rules-config.xml
#Составные части хост и реалм, из них конструируется принципал если напрямую он не задан
ru.naumen.core.authentication.spnego-authenticator.service-host=krb
# Пароль пользователя при подключении без использования keytab-файла
ru.naumen.core.authentication.spnego-authenticator.service-password=
ru.naumen.core.authentication.spnego-authenticator.service-password.enc=null
#################################################################################
# SPNEGO authenticator settings
#################################################################################
#Принципал сервиса для подключения сервера к AD
ru.naumen.core.authentication.spnego-authenticator.service-principal=HTTP/krb.dc2.local
ru.naumen.core.authentication.spnego-authenticator.service-realm=.dc2.local
# какой кэш использовать
ru.naumen.core.authentication.spnego-authenticator.ticket-cache=
# флаг указывающий используется ли keytab файл
ru.naumen.core.authentication.spnego-authenticator.use-keytab=true
# Использовать кэш тикетов
ru.naumen.core.authentication.spnego-authenticator.use-ticket-cache=false
#################################################################################
# X509 authenticator settings
#################################################################################
ru.naumen.core.authentication.x509-authenticator.certificate-attribute=CN
ru.naumen.core.authentication.x509-authenticator.certificate-header=
ru.naumen.core.authentication.x509-authenticator.auth-attribute=login
#################################################################################
# OAuth2 authentification.
#################################################################################
#Включена ли аутентификация по oauth2
ru.naumen.oauth2=false
ru.naumen.oauth2.endpoints=
#################################################################################
#Отключить проверку процессной лицензии по-умолчанию в hasEditAttrPermissionForProcessingLicense(AuthorizationContext context, String attrCode)
ru.naumen.authorize.checkAttrPermissionWithProcessLicense=false

#Включен ли метод api.web.signIn()
ru.naumen.api.quick.signin.enabled=true