# Интервал проверки сменился ли IP у БД. Нужен для Saas, и только для C3P0
db.generator.check.ip.timeout=30000
# Время, не менее которого соединение может оставаться в пуле при бездействии.
db.generator.hikari_idleTimeout=900000
# Устанавливает период времени нахождения соединения вне пула, по истечении которого соединение считается утекшим.
# Работает только для HikariCP.
# 15 min по-умолчанию.
db.generator.hikari_leakDetectionThreshold=900000
# Максимальная продолжительность соединения при бездействии.
# Устанавливать чуть меньше, чем аналогичный параметр со стороны БД.
db.generator.hikari_maxLifetime=3600000
# Минимальное количество соединений в пуле.
db.generator.hikari_minimumIdle=1
# Максимальное количество активных соединений
db.generator.max_active_connections=5
# Максимальное время ожидания соединения из пула, мс. В случае если используется c3p0 и установлен db.c3p0_unlimited_startup=true,
# значение игнорируется
db.generator.max_time_waiting_connection=120000
# Задает пул соединений
# Сейчас возможно два варианта default и c3p0
db.generator.pool=default
# Путь к конфигу пула
db.generator.pool.config=${user.home}/.naumen/sd/conf/pool.properties
# Время, не менее которого соединение может оставаться в пуле при бездействии.
db.generator.unlimited_startup=false
db.hikari_idleTimeout=900000
# Устанавливает период времени нахождения соединения вне пула, по истечении которого соединение считается утекшим.
# Работает только для HikariCP.
# 15 min по-умолчани.
db.hikari_leakDetectionThreshold=900000
# Максимальная продолжительность соединения при бездействии.
# Устанавливать чуть меньше, чем аналогичный параметр со стороны БД.
db.hikari_maxLifetime=3600000
# Минимальное количество соединений в пуле.
db.hikari_minimumIdle=3
db.hikari_unlimited_startup=false
# Интервал попыток подключиться к базе в SQLServerReadCommittedEnabler при db.hikari_unlimited_startup=true
db.hikari_unlimited_startup_mssql_check_delay=1000
#################################################################################
# Создавать индексы в БД в отдельном потоке
db.indexes.createIndexes.concurrently=false
# Выполнять валидацию внешних ключей в отдельном потоке
db.constraints.validateForeignKey.concurrently=false
#################################################################################
# Максимальное количество активных соединений
db.max_active_connections=10
# Максимальное время ожидания соединения из пула, мс. В случае если используется c3p0 и установлен db.c3p0_unlimited_startup=true,
# значение игнорируется
db.max_time_waiting_connection=120000
db.password=
db.password.enc=null
# -- PGJDBC cache settings --
#number of executed prepared statements after which they are cached, defaults to 5 in pgdriver
db.pg.prepare_threshold=1
#Prepared statements cache max entries per connection, defaults to 256 in pgdriver
db.pg.ps_cache_queries=
#Prepared statements cache max size in MiB, defaults to 5 in pgdriver
db.pg.ps_cache_size_mib=
# Задает пул соединений
# Сейчас возможно два варианта default и c3p0
db.pool=default
# Путь к конфигу пула
db.pool.config=${user.home}/.naumen/sd/conf/pool.properties
# Для cluster-jdbc пула
# таймаут на выполнение запроса в текущем соединении в секундах
# если запрос выполняется дольше, то соединение закрыватеся, запрос перестает выполняться и в приложение возвращается
# ошибка. Если 0, то таймаута нет
db.pool.clusterJdbc.connectionQueryTimeout=0
# Для event пула
# таймаут на выполнение запроса в текущем соединении в секундах
# если запрос выполняется дольше, то соединение закрыватеся, запрос перестает выполняться и в приложение возвращается
# ошибка. Если 0, то таймаута нет
db.pool.event.connectionQueryTimeout=3600
# Для read-only пула
# таймаут на выполнение запроса в текущем соединении в секундах
# если запрос выполняется дольше, то соединение закрыватеся, запрос перестает выполняться и в приложение возвращается
# ошибка. Если 0, то таймаута нет
db.pool.readOnly.connectionQueryTimeout=0
# Для регулярного пула
# таймаут на выполнение запроса в текущем соединении в секундах
# если запрос выполняется дольше, то соединение закрыватеся, запрос перестает выполняться и в приложение возвращается
# ошибка. Если 0, то таймаута нет
db.pool.regular.connectionQueryTimeout=3600
# Для отчетного пула
# таймаут на выполнение запроса в текущем соединении в секундах
# если запрос выполняется дольше, то соединение закрыватеся, запрос перестает выполняться и в приложение возвращается
# ошибка. Если 0, то таймаута нет
db.pool.report.connectionQueryTimeout=0
db.reWriteBatchedInserts=true
db.read_only.cache_mode=get
db.read_only.hikari_idleTimeout=900000
db.read_only.hikari_leakDetectionThreshold=900000
db.read_only.hikari_maxLifetime=3600000
db.read_only.hikari_minimumIdle=1
db.read_only.max_active_connections=10
db.read_only.max_time_waiting_connection=120000
db.read_only.sync.timeout=1000
db.read_only.unlimited_startup=false
db.read_only.url=
db.mssql.trustServerCertificate=true
#Вставим пустые значения по умолчанию для возможности автоматического подключения к MSSQL через механизм integratedSecurity
db.user=
hibernate.default_schema=public
hibernate.events_schema=public
hibernate.format_sql=false
# If you enable hibernate.generate_statistics, Hibernate exposes a number of metrics that are useful when tuning a running system via SessionFactory.getStatistics(). Hibernate can even be configured to expose these statistics via JMX. Read the Javadoc of the interfaces in org.hibernate.stats for more information.
hibernate.generate_statistics=false
hibernate.hbm2ddl.auto=update
hibernate.id.new_generator_mappings=true
# При использовании секционированных таблиц в Postgresql нужен особый batchBuilder который не будет проверять число строк в ответе PG
hibernate.jdbc.batch.builder=org.hibernate.engine.jdbc.batch.internal.BatchBuilderImpl
#Доступно два значения - AUTO и MANUAL, в режиме AUTO SessionFactory перезагружается после коммита транзакции, в который была изменена структура слоя данных
#В режиме MANUAL нужно вручную перезагружать, позволяет уменьшить количество перезагрузок для АТ
#Потенциально может пригодиться для пакетной перезагрузки при настройке приложения
hibernate.sessionFactory.reload.mode=AUTO
#hibernate.jdbc.batch.builder=ru.naumen.core.server.jta.NauHibernateBatchBuilderPgPartitioned
hibernate.show_sql=false
hibernate.use_sql_comments=false
# Включает проверку наличия старых фабрик сессий: тех, которые после перезагрузки метаинформации, продолжают работать по старой модели данных.
# пока такие фабрики сессий существуют, для всех фабрик сессий будет отключена возможность получения данных из кэша второго уровня
old-session-factories-check.enabled=true
#размер значения после которого редактирование набора ссылок выполняется через sql
ru.naumen.bcp.bolinks.threshold_sql=100
# Делать ли рефреш объекта при обновлении его атрибутов типа обратная ссылка (через изменение прямых ссылок в подпроцессах)
ru.naumen.bo.backlinks.refresh.enabled=true
# переименовывать колонки/таблицы в БД вместо удаления
ru.naumen.db.deferred.deletion.enabled=false
ru.naumen.db.pg.application_name.fallback=false
ru.naumen.db.url.resolve.skip=false
ru.naumen.dto.collection.countValueInString=10
# использовать ли distinct в подзапросах для динамических атрибутов
ru.naumen.dynamicField.useDistinct.enabled=true
# Признак необходимости проверять используемый диалект Hibernate, и при его обнаружении в списке автозаменяемых - заменять (см. NauHibernateDialectHelper)
ru.naumen.hibernate.autoSubstituteDialect=true
ru.naumen.hibernate.persistence_context.limit=50000
# Id Generator
#################################################################################
# Использовать ли кастомный запрос для получения всех ключей
ru.naumen.oracle.custom.getAllImportedKeysInfoQuery=true
ru.naumen.persistence.second_level_cache.use=true
ru.naumen.query.distinct.disable_for_contents=
ru.naumen.query.distinct.disable_for_related=
ru.naumen.query.distinct.disable_for_single_source=true
#Add comments like '-- Main(timestamp): select ..'
ru.naumen.query_stat.add_comments_to_queries=false
#Query statistics settings
#Collect query execution statistic
ru.naumen.query_stat.collect.thread.queries=false
#Max query stat data buffer size before flush to disk
ru.naumen.query_stat.max_elements_before_flush=1000
#Max query stat data in storage queue
ru.naumen.query_stat.max_store_queue_size=4096
#Rollover interval for query stat data file
ru.naumen.query_stat.rollover_interval_hours=72
ru.naumen.readonly.allWithExclusions=false
ru.naumen.readonly.contents=
ru.naumen.readonly.exclusions=
#Таймаут на получение блокировки для совершения ddl операции в секундах. Если значение <= 0, то таймаут не ставится.
ru.naumen.schema.ddl.timeout=100000
# Вкл/выкл команду для удаления TRUNCATE (вместо DELETE) в SQL-запросах
ru.naumen.truncateOff=false
ru.naumen.uploadFileLifetimeByHours=12
# Задаёт socketTimeout для соединений с базой (фактически ожидание на Socket#read).
# 0 - нет таймаута (бесконечное ожидание).
sequence.acquire.timeout=0
#Включить сохранение ранее введенного значения времени при редактировании даты.
ru.naumen.attributes.dataTime.saveOldValueTime=false
#Включение возможности использования кастомных SQL запросов в некоторых списках объектов.
ru.naumen.lists.custom_queries.enabled=false
#Количество элементов списка, которые будут выводиться при вызове ScriptDtOCollection#toString()
#Если < 0 => выводятся все элементы, если >= 0 => выводится указанное количество
#Включить индексацию при проверке уникальных атрибутов в Oracle
ru.naumen.oracle.custom.indexedUniqueAttributes=true
# Можно ли секционировать по дате
ru.naumen.partition.allowedMigrateByDate=false
# Кол-во объектов, перемещаемых в рамках одной миграции
ru.naumen.partition.migrate.batchSize=1000
# Кол-во соединений до СУБД при миграции объектов (необходимо устанавливать меньше, чем db.max_active_connections (глобальное ограничение), учитывая, что соединения до субд нужны и другим процессам системы)
ru.naumen.partition.migrate.connectDbCount=1
# Кол-во потоков для пула потоков при миграции объектов (необходимо устанавливать не больше, чем кол-во ядер у цпу и рассчитывать на то, что потоки нужны еще и на другие процессы в системе)
ru.naumen.partition.migrate.threadsCount=2
