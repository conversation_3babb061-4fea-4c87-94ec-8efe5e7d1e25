<!--
  Default stack using IP multicasting. It is similar to the "udp"
  stack in stacks.xml, but doesn't use streaming state transfer and flushing
  author: Bel<PERSON> Ban

  dkirpichenkov: проверненный конфиг на TCP.
  Положение протокола в XML имеет значение

	ucast_recv_buf_size="212K"
	ucast_send_buf_size="212K"
	mcast_recv_buf_size="212K"
	mcast_send_buf_size="212K"
	max_bundle_size="64K"

Пример настройки JGroups. Стек протокол и их параметры.
Необходимо соблюдать последовательность объявления протоколов
-->

<config xmlns="urn:org:jgroups"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="urn:org:jgroups http://www.jgroups.org/schema/JGroups-3.4.xsd">

    <!-- Транспорт и миллион его настроек:

    Настройки пулов для обработки входящих сообщений. и сообщений

    thread_pool
    oob_thread_pool

    queue_enabled - включение очереди для сообщений

    Политика что делать когда входящее собщение не влазит в очередь

    thread_pool.rejection_policy="discard"
    oob_thread_pool.rejection_policy="discard"

    ip_mcast="false" не использовать IP Multicast. По-умолчанию ip_mcast="true"

    -->
    <TCP bind_addr="${jgroups.bus.bind_addr:localhost}"
         bind_port="${jgroups.bus.bind_port:7801}"
         recv_buf_size="${tcp.recv_buf_size:5M}"
         send_buf_size="${tcp.send_buf_size:5M}"
         max_bundle_size="64K"
         max_bundle_timeout="30"
         use_send_queues="true"
         sock_conn_timeout="300"
         enable_diagnostics="true"
         thread_naming_pattern="cl"

         timer_type="new3"
         timer.min_threads="4"
         timer.max_threads="10"
         timer.keep_alive_time="3000"
         timer.queue_max_size="500"

         thread_pool.enabled="true"
         thread_pool.min_threads="2"
         thread_pool.max_threads="8"
         thread_pool.keep_alive_time="5000"
         thread_pool.queue_enabled="true"
         thread_pool.queue_max_size="10000"
         thread_pool.rejection_policy="discard"

         oob_thread_pool.enabled="true"
         oob_thread_pool.min_threads="1"
         oob_thread_pool.max_threads="8"
         oob_thread_pool.keep_alive_time="5000"
         oob_thread_pool.queue_enabled="false"
         oob_thread_pool.queue_max_size="100"
         oob_thread_pool.rejection_policy="discard"/>

    <!-- ip адреса начальных нод. Следует указать нод участвующих в кластеризации. -->
    <TCPPING initial_hosts="${jgroups.tcpping.initial_hosts:localhost[7800],localhost[7801]}"
             port_range="1"/>
    <!-- Протокол для совмещения ранее разбитых групп -->
    <MERGE3 max_interval="30000"
            min_interval="10000"/>
    <!-- Протокол определения пропавших нод через сокеты какие-то -->
    <FD_SOCK/>
    <!-- Протокол определения пропавших нод через heart-beat пакеты -->
    <FD_ALL/>
    <!-- Протокол проверки подозреваемой в "смерти" ноды -->
    <VERIFY_SUSPECT timeout="1500"/>

    <!-- Протокол для гарантирования доставки сообщений и сохранения порядка доставки сообщений от ноды. ( FIFO guarantees that all messages from sender P will be received in the order P sent them) -->
    <pbcast.NAKACK2 xmit_interval="500"
                    xmit_table_num_rows="100"
                    xmit_table_msgs_per_row="2000"
                    xmit_table_max_compaction_time="30000"
                    max_msg_batch_size="500"
                    use_mcast_xmit="false"
                    discard_delivered_msgs="true"/>

    <!-- Тоже же что и NAKACK но для UNICAST сообщений -->
    <UNICAST3 xmit_interval="500"
              xmit_table_num_rows="100"
              xmit_table_msgs_per_row="2000"
              xmit_table_max_compaction_time="60000"
              conn_expiry_timeout="0"
              max_msg_batch_size="500"/>
    <!-- Удаление уже полученных всеми нодами сообщений. Сборка сетевого мусора -->
    <pbcast.STABLE stability_delay="1000" desired_avg_gossip="50000"
                   max_bytes="4M"/>
    <!-- Протокол объединения в группы. Отвечает за (при)отсоединение нод и формирование нового состава группы с уведомление нод о составе	                -->
    <pbcast.GMS print_local_addr="true" join_timeout="3000"
                view_bundling="true"/>
    <!-- Unicast flow control между двумя нодами. Защита от слишком большого и частого общения -->
    <UFC max_credits="2M"
         min_threshold="0.4"/>
    <!-- Multicast flow control между отправителем и всеми нодами-получателями. Предотвращение избыточного общения нод -->
    <MFC max_credits="2M"
         min_threshold="0.4"/>
    <!-- Протокол для разбиения большого сообщения на кучку маленьких при отправке и наоборот -->
    <FRAG2 frag_size="60K"/>

    <!-- Таймаут синхронных сообщений сейчас 100 секунда. Интервал повторных отправок - 2 сек -->
    <RSVP resend_interval="2000" timeout="100000"/>

    <!-- Централизованные локи в кластере -->
    <CENTRAL_LOCK stats="true" num_backups="1" level="DEBUG"/>

    <!-- pbcast.FLUSH  /-->
</config>
             