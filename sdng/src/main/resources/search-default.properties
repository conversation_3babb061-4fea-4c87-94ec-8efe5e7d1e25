#Параметр отвечающий за отключение индексации для определенных классов.
#Допустимо перечисление идентификаторов классов через ',' или ';'.
indexing.exceptions.forbiddenClasses=
#Параметр отвечающий за регуляцию приоритизации индексации новых объектов.
#true - новым объектам будет отдаваться предпочтение при индексации
indexing.priority.preferNewObjects=false
#Для Lucene устанавливает количество потоков для слушателя в DocumentIndexerContainer.
#Для OpenSearch устанавливает количество потоков для ExecutorService, отвечающего за индексацию.
#По умолчанию/если указано -1, количетсов потоков будет = КОЛИЧЕСТВО ЯДЕР * 3/4
indexing.threadNumber=1
ru.naumen.indexing.backpressure.factor=4
ru.naumen.indexing.backpressure.ignore=false
ru.naumen.indexing.backpressure.max_batch_duration=5000
ru.naumen.indexing.backpressure.sleep.time=100
ru.naumen.indexing.batch.size=1500
ru.naumen.indexing.jdbc.table.use_node_id=true
#Количество индексирующих потоков для очереди индексации связанных объектов
#Для Lucene устанавливает количество потоков для слушателя в LinkedDocumentIndexerContainer.
#Для OpenSearch устанавливает количество потоков для ExecutorService, отвечающего за индексацию.
ru.naumen.linkedobjects.indexing.threadNumber=1
#включить индексирование связанных объектов (через атрибуты СБО, НБО и АО)
ru.naumen.linkedobjects.indexing=false
# количестов символов для индексации тела комментария, если <= 0 - то индексировать все, иначе первые n - символов
#################################################################################
# Lucene settings
#################################################################################
# использовать БД (true) или Map (false) локально для служебной таблицы с id и fqn
ru.naumen.fts.server.lucene.reindex.usedb=true
# количестов символов для индексации тела комментария, если <= 0 - то индексировать все, иначе первые n - символов
ru.naumen.search.comment.index.length=0
#Записывать ли дату последнего изменения в индекс
ru.naumen.lucene.indexMutator.addCommitDate=false
#Позволяет использовать механизм конкурентного мержа
ru.naumen.lucene.indexWriter.useConcurrentMergeScheduler=true
ru.naumen.lucene.invalid.index.throwException.enabled=true
#Использовать точку для разделения токенов при индексации
ru.naumen.lucene.use_dot_splitting=false
#Логин подключения к opensearch
ru.naumen.opensearch.client.login=admin
#Пароль подключения к opensearch
ru.naumen.opensearch.client.password=admin
#Зашифрованный пароль подключения к opensearch
ru.naumen.opensearch.client.password.enc=
#Урл подключения к opensearch
ru.naumen.opensearch.client.url=
#Indexing configuration
ru.naumen.reindexing.batch.size=1500
#Фильтровать или нет список uuid в ListRightsFilter.getPermittedObjects, на которых включена галочка "Показывать в результатах поиска"
#теми uuid, что нашлись ранее с помощью lucene.
#True - фильтровать, false - не фильтровать.
ru.naumen.search.enabledFilterOnUuid=false
#Максимальное время на индексацию одного объекта
ru.naumen.search.escape-search-symbols=false
#Максимальне количество объектов в поисковой выдаче при использовании в СФДС с полнотекстовым поиском, по умолчанию 10 000
ru.naumen.search.fullTextSearchOnFormObjectsLimit=10000
# Будет ли при выполнении поисковых запросов использоваться fuzzy-поиск, то есть поиск с учетом опечаток, небольших отличий в написании слов
ru.naumen.search.fuzzySearch.enabled=true
# Уровень неточного совпадения (fuzziness) при fuzzy-поиске. Допустимые значения: 1 или 2. Если значения вне этого диапазона, используестся значение по умолчанию.
ru.naumen.search.fuzzySearch.fuzziness=2
#Максимальное количество индексируемых комментариев у объекта
ru.naumen.search.indexing.comment.limit=1000
ru.naumen.search.object_indexing_timeout=7
#Максимальное количество объектов в поисковой выдаче (ограничение на выдачу результатов)
ru.naumen.search.objects_limit=100
ru.naumen.search.simple_reader_management=false
#Включить режим ограничения поисковой строки.
ru.naumen.search.string.maxLength=0
ru.naumen.search.string.minLength=0
#Simple search settings
ru.naumen.simpleSearch.maxLength=255
#TreeSearchSettings
ru.naumen.tree.search.string.max.length=-1
ru.naumen.tree.search.string.min.length=1
# Нужно ли производить поиск ответственного так же как и для остальных агрегирующих атрибутов
search.resp.like.others=false